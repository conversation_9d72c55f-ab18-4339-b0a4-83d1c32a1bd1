import{r as l,j as h}from"./main-B9Fv5CdX.js";import{c as J,t as ge,a as xe,b as E,q as Q,u as Ce,e as L,P as U,d as Re,y as Ee}from"./dialog-iGlJJq5Q.js";import{u as N,a as Z}from"./button-Ispz1G12.js";import{P as O}from"./label-CNQvdrLZ.js";var B="rovingFocusGroup.onEntryFocus",Pe={bubbles:!1,cancelable:!0},W="RovingFocusGroup",[X,ee,Te]=ge(W),[Ae,oo]=J(W,[Te]),[ye,Ie]=Ae(W),oe=l.forwardRef((e,r)=>h.jsx(X.Provider,{scope:e.__scopeRovingFocusGroup,children:h.jsx(X.Slot,{scope:e.__scopeRovingFocusGroup,children:h.jsx(_e,{...e,ref:r})})}));oe.displayName=W;var _e=l.forwardRef((e,r)=>{const{__scopeRovingFocusGroup:o,orientation:n,loop:t=!1,dir:s,currentTabStopId:c,defaultCurrentTabStopId:i,onCurrentTabStopIdChange:u,onEntryFocus:d,preventScrollOnEntryFocus:a=!1,...b}=e,f=l.useRef(null),v=N(r,f),w=Q(s),[T,p]=Ce({prop:c,defaultProp:i??null,onChange:u,caller:W}),[g,I]=l.useState(!1),x=L(d),R=ee(o),_=l.useRef(!1),[D,F]=l.useState(0);return l.useEffect(()=>{const S=f.current;if(S)return S.addEventListener(B,x),()=>S.removeEventListener(B,x)},[x]),h.jsx(ye,{scope:o,orientation:n,dir:w,loop:t,currentTabStopId:T,onItemFocus:l.useCallback(S=>p(S),[p]),onItemShiftTab:l.useCallback(()=>I(!0),[]),onFocusableItemAdd:l.useCallback(()=>F(S=>S+1),[]),onFocusableItemRemove:l.useCallback(()=>F(S=>S-1),[]),children:h.jsx(O.div,{tabIndex:g||D===0?-1:0,"data-orientation":n,...b,ref:v,style:{outline:"none",...e.style},onMouseDown:E(e.onMouseDown,()=>{_.current=!0}),onFocus:E(e.onFocus,S=>{const m=!_.current;if(S.target===S.currentTarget&&m&&!g){const C=new CustomEvent(B,Pe);if(S.currentTarget.dispatchEvent(C),!C.defaultPrevented){const A=R().filter(j=>j.focusable),$=A.find(j=>j.active),Se=A.find(j=>j.id===T),we=[$,Se,...A].filter(Boolean).map(j=>j.ref.current);ne(we,a)}}_.current=!1}),onBlur:E(e.onBlur,()=>I(!1))})})}),re="RovingFocusGroupItem",te=l.forwardRef((e,r)=>{const{__scopeRovingFocusGroup:o,focusable:n=!0,active:t=!1,tabStopId:s,children:c,...i}=e,u=xe(),d=s||u,a=Ie(re,o),b=a.currentTabStopId===d,f=ee(o),{onFocusableItemAdd:v,onFocusableItemRemove:w,currentTabStopId:T}=a;return l.useEffect(()=>{if(n)return v(),()=>w()},[n,v,w]),h.jsx(X.ItemSlot,{scope:o,id:d,focusable:n,active:t,children:h.jsx(O.span,{tabIndex:b?0:-1,"data-orientation":a.orientation,...i,ref:r,onMouseDown:E(e.onMouseDown,p=>{n?a.onItemFocus(d):p.preventDefault()}),onFocus:E(e.onFocus,()=>a.onItemFocus(d)),onKeyDown:E(e.onKeyDown,p=>{if(p.key==="Tab"&&p.shiftKey){a.onItemShiftTab();return}if(p.target!==p.currentTarget)return;const g=Fe(p,a.orientation,a.dir);if(g!==void 0){if(p.metaKey||p.ctrlKey||p.altKey||p.shiftKey)return;p.preventDefault();let x=f().filter(R=>R.focusable).map(R=>R.ref.current);if(g==="last")x.reverse();else if(g==="prev"||g==="next"){g==="prev"&&x.reverse();const R=x.indexOf(p.currentTarget);x=a.loop?je(x,R+1):x.slice(R+1)}setTimeout(()=>ne(x))}}),children:typeof c=="function"?c({isCurrentTabStop:b,hasTabStop:T!=null}):c})})});te.displayName=re;var Le={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function De(e,r){return r!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Fe(e,r,o){const n=De(e.key,o);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(n))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(n)))return Le[n]}function ne(e,r=!1){const o=document.activeElement;for(const n of e)if(n===o||(n.focus({preventScroll:r}),document.activeElement!==o))return}function je(e,r){return e.map((o,n)=>e[(r+n)%e.length])}var ro=oe,to=te;function Ne(e,r){return l.useReducer((o,n)=>r[o][n]??o,e)}var V="ScrollArea",[le,no]=J(V),[Oe,P]=le(V),se=l.forwardRef((e,r)=>{const{__scopeScrollArea:o,type:n="hover",dir:t,scrollHideDelay:s=600,...c}=e,[i,u]=l.useState(null),[d,a]=l.useState(null),[b,f]=l.useState(null),[v,w]=l.useState(null),[T,p]=l.useState(null),[g,I]=l.useState(0),[x,R]=l.useState(0),[_,D]=l.useState(!1),[F,S]=l.useState(!1),m=N(r,A=>u(A)),C=Q(t);return h.jsx(Oe,{scope:o,type:n,dir:C,scrollHideDelay:s,scrollArea:i,viewport:d,onViewportChange:a,content:b,onContentChange:f,scrollbarX:v,onScrollbarXChange:w,scrollbarXEnabled:_,onScrollbarXEnabledChange:D,scrollbarY:T,onScrollbarYChange:p,scrollbarYEnabled:F,onScrollbarYEnabledChange:S,onCornerWidthChange:I,onCornerHeightChange:R,children:h.jsx(O.div,{dir:C,...c,ref:m,style:{position:"relative","--radix-scroll-area-corner-width":g+"px","--radix-scroll-area-corner-height":x+"px",...e.style}})})});se.displayName=V;var ce="ScrollAreaViewport",ae=l.forwardRef((e,r)=>{const{__scopeScrollArea:o,children:n,nonce:t,...s}=e,c=P(ce,o),i=l.useRef(null),u=N(r,i,c.onViewportChange);return h.jsxs(h.Fragment,{children:[h.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:t}),h.jsx(O.div,{"data-radix-scroll-area-viewport":"",...s,ref:u,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:h.jsx("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:n})})]})});ae.displayName=ce;var y="ScrollAreaScrollbar",ie=l.forwardRef((e,r)=>{const{forceMount:o,...n}=e,t=P(y,e.__scopeScrollArea),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:c}=t,i=e.orientation==="horizontal";return l.useEffect(()=>(i?s(!0):c(!0),()=>{i?s(!1):c(!1)}),[i,s,c]),t.type==="hover"?h.jsx(He,{...n,ref:r,forceMount:o}):t.type==="scroll"?h.jsx(We,{...n,ref:r,forceMount:o}):t.type==="auto"?h.jsx(ue,{...n,ref:r,forceMount:o}):t.type==="always"?h.jsx(G,{...n,ref:r}):null});ie.displayName=y;var He=l.forwardRef((e,r)=>{const{forceMount:o,...n}=e,t=P(y,e.__scopeScrollArea),[s,c]=l.useState(!1);return l.useEffect(()=>{const i=t.scrollArea;let u=0;if(i){const d=()=>{window.clearTimeout(u),c(!0)},a=()=>{u=window.setTimeout(()=>c(!1),t.scrollHideDelay)};return i.addEventListener("pointerenter",d),i.addEventListener("pointerleave",a),()=>{window.clearTimeout(u),i.removeEventListener("pointerenter",d),i.removeEventListener("pointerleave",a)}}},[t.scrollArea,t.scrollHideDelay]),h.jsx(U,{present:o||s,children:h.jsx(ue,{"data-state":s?"visible":"hidden",...n,ref:r})})}),We=l.forwardRef((e,r)=>{const{forceMount:o,...n}=e,t=P(y,e.__scopeScrollArea),s=e.orientation==="horizontal",c=k(()=>u("SCROLL_END"),100),[i,u]=Ne("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return l.useEffect(()=>{if(i==="idle"){const d=window.setTimeout(()=>u("HIDE"),t.scrollHideDelay);return()=>window.clearTimeout(d)}},[i,t.scrollHideDelay,u]),l.useEffect(()=>{const d=t.viewport,a=s?"scrollLeft":"scrollTop";if(d){let b=d[a];const f=()=>{const v=d[a];b!==v&&(u("SCROLL"),c()),b=v};return d.addEventListener("scroll",f),()=>d.removeEventListener("scroll",f)}},[t.viewport,s,u,c]),h.jsx(U,{present:o||i!=="hidden",children:h.jsx(G,{"data-state":i==="hidden"?"hidden":"visible",...n,ref:r,onPointerEnter:E(e.onPointerEnter,()=>u("POINTER_ENTER")),onPointerLeave:E(e.onPointerLeave,()=>u("POINTER_LEAVE"))})})}),ue=l.forwardRef((e,r)=>{const o=P(y,e.__scopeScrollArea),{forceMount:n,...t}=e,[s,c]=l.useState(!1),i=e.orientation==="horizontal",u=k(()=>{if(o.viewport){const d=o.viewport.offsetWidth<o.viewport.scrollWidth,a=o.viewport.offsetHeight<o.viewport.scrollHeight;c(i?d:a)}},10);return H(o.viewport,u),H(o.content,u),h.jsx(U,{present:n||s,children:h.jsx(G,{"data-state":s?"visible":"hidden",...t,ref:r})})}),G=l.forwardRef((e,r)=>{const{orientation:o="vertical",...n}=e,t=P(y,e.__scopeScrollArea),s=l.useRef(null),c=l.useRef(0),[i,u]=l.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=ve(i.viewport,i.content),a={...n,sizes:i,onSizesChange:u,hasThumb:d>0&&d<1,onThumbChange:f=>s.current=f,onThumbPointerUp:()=>c.current=0,onThumbPointerDown:f=>c.current=f};function b(f,v){return Be(f,c.current,i,v)}return o==="horizontal"?h.jsx(ze,{...a,ref:r,onThumbPositionChange:()=>{if(t.viewport&&s.current){const f=t.viewport.scrollLeft,v=q(f,i,t.dir);s.current.style.transform=`translate3d(${v}px, 0, 0)`}},onWheelScroll:f=>{t.viewport&&(t.viewport.scrollLeft=f)},onDragScroll:f=>{t.viewport&&(t.viewport.scrollLeft=b(f,t.dir))}}):o==="vertical"?h.jsx(Me,{...a,ref:r,onThumbPositionChange:()=>{if(t.viewport&&s.current){const f=t.viewport.scrollTop,v=q(f,i);s.current.style.transform=`translate3d(0, ${v}px, 0)`}},onWheelScroll:f=>{t.viewport&&(t.viewport.scrollTop=f)},onDragScroll:f=>{t.viewport&&(t.viewport.scrollTop=b(f))}}):null}),ze=l.forwardRef((e,r)=>{const{sizes:o,onSizesChange:n,...t}=e,s=P(y,e.__scopeScrollArea),[c,i]=l.useState(),u=l.useRef(null),d=N(r,u,s.onScrollbarXChange);return l.useEffect(()=>{u.current&&i(getComputedStyle(u.current))},[u]),h.jsx(fe,{"data-orientation":"horizontal",...t,ref:d,sizes:o,style:{bottom:0,left:s.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:s.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Y(o)+"px",...e.style},onThumbPointerDown:a=>e.onThumbPointerDown(a.x),onDragScroll:a=>e.onDragScroll(a.x),onWheelScroll:(a,b)=>{if(s.viewport){const f=s.viewport.scrollLeft+a.deltaX;e.onWheelScroll(f),me(f,b)&&a.preventDefault()}},onResize:()=>{u.current&&s.viewport&&c&&n({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:M(c.paddingLeft),paddingEnd:M(c.paddingRight)}})}})}),Me=l.forwardRef((e,r)=>{const{sizes:o,onSizesChange:n,...t}=e,s=P(y,e.__scopeScrollArea),[c,i]=l.useState(),u=l.useRef(null),d=N(r,u,s.onScrollbarYChange);return l.useEffect(()=>{u.current&&i(getComputedStyle(u.current))},[u]),h.jsx(fe,{"data-orientation":"vertical",...t,ref:d,sizes:o,style:{top:0,right:s.dir==="ltr"?0:void 0,left:s.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Y(o)+"px",...e.style},onThumbPointerDown:a=>e.onThumbPointerDown(a.y),onDragScroll:a=>e.onDragScroll(a.y),onWheelScroll:(a,b)=>{if(s.viewport){const f=s.viewport.scrollTop+a.deltaY;e.onWheelScroll(f),me(f,b)&&a.preventDefault()}},onResize:()=>{u.current&&s.viewport&&c&&n({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:M(c.paddingTop),paddingEnd:M(c.paddingBottom)}})}})}),[Ue,de]=le(y),fe=l.forwardRef((e,r)=>{const{__scopeScrollArea:o,sizes:n,hasThumb:t,onThumbChange:s,onThumbPointerUp:c,onThumbPointerDown:i,onThumbPositionChange:u,onDragScroll:d,onWheelScroll:a,onResize:b,...f}=e,v=P(y,o),[w,T]=l.useState(null),p=N(r,m=>T(m)),g=l.useRef(null),I=l.useRef(""),x=v.viewport,R=n.content-n.viewport,_=L(a),D=L(u),F=k(b,10);function S(m){if(g.current){const C=m.clientX-g.current.left,A=m.clientY-g.current.top;d({x:C,y:A})}}return l.useEffect(()=>{const m=C=>{const A=C.target;(w==null?void 0:w.contains(A))&&_(C,R)};return document.addEventListener("wheel",m,{passive:!1}),()=>document.removeEventListener("wheel",m,{passive:!1})},[x,w,R,_]),l.useEffect(D,[n,D]),H(w,F),H(v.content,F),h.jsx(Ue,{scope:o,scrollbar:w,hasThumb:t,onThumbChange:L(s),onThumbPointerUp:L(c),onThumbPositionChange:D,onThumbPointerDown:L(i),children:h.jsx(O.div,{...f,ref:p,style:{position:"absolute",...f.style},onPointerDown:E(e.onPointerDown,m=>{m.button===0&&(m.target.setPointerCapture(m.pointerId),g.current=w.getBoundingClientRect(),I.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",v.viewport&&(v.viewport.style.scrollBehavior="auto"),S(m))}),onPointerMove:E(e.onPointerMove,S),onPointerUp:E(e.onPointerUp,m=>{const C=m.target;C.hasPointerCapture(m.pointerId)&&C.releasePointerCapture(m.pointerId),document.body.style.webkitUserSelect=I.current,v.viewport&&(v.viewport.style.scrollBehavior=""),g.current=null})})})}),z="ScrollAreaThumb",he=l.forwardRef((e,r)=>{const{forceMount:o,...n}=e,t=de(z,e.__scopeScrollArea);return h.jsx(U,{present:o||t.hasThumb,children:h.jsx(Ye,{ref:r,...n})})}),Ye=l.forwardRef((e,r)=>{const{__scopeScrollArea:o,style:n,...t}=e,s=P(z,o),c=de(z,o),{onThumbPositionChange:i}=c,u=N(r,b=>c.onThumbChange(b)),d=l.useRef(void 0),a=k(()=>{d.current&&(d.current(),d.current=void 0)},100);return l.useEffect(()=>{const b=s.viewport;if(b){const f=()=>{if(a(),!d.current){const v=Xe(b,i);d.current=v,i()}};return i(),b.addEventListener("scroll",f),()=>b.removeEventListener("scroll",f)}},[s.viewport,a,i]),h.jsx(O.div,{"data-state":c.hasThumb?"visible":"hidden",...t,ref:u,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...n},onPointerDownCapture:E(e.onPointerDownCapture,b=>{const v=b.target.getBoundingClientRect(),w=b.clientX-v.left,T=b.clientY-v.top;c.onThumbPointerDown({x:w,y:T})}),onPointerUp:E(e.onPointerUp,c.onThumbPointerUp)})});he.displayName=z;var K="ScrollAreaCorner",be=l.forwardRef((e,r)=>{const o=P(K,e.__scopeScrollArea),n=!!(o.scrollbarX&&o.scrollbarY);return o.type!=="scroll"&&n?h.jsx(ke,{...e,ref:r}):null});be.displayName=K;var ke=l.forwardRef((e,r)=>{const{__scopeScrollArea:o,...n}=e,t=P(K,o),[s,c]=l.useState(0),[i,u]=l.useState(0),d=!!(s&&i);return H(t.scrollbarX,()=>{var b;const a=((b=t.scrollbarX)==null?void 0:b.offsetHeight)||0;t.onCornerHeightChange(a),u(a)}),H(t.scrollbarY,()=>{var b;const a=((b=t.scrollbarY)==null?void 0:b.offsetWidth)||0;t.onCornerWidthChange(a),c(a)}),d?h.jsx(O.div,{...n,ref:r,style:{width:s,height:i,position:"absolute",right:t.dir==="ltr"?0:void 0,left:t.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function M(e){return e?parseInt(e,10):0}function ve(e,r){const o=e/r;return isNaN(o)?0:o}function Y(e){const r=ve(e.viewport,e.content),o=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,n=(e.scrollbar.size-o)*r;return Math.max(n,18)}function Be(e,r,o,n="ltr"){const t=Y(o),s=t/2,c=r||s,i=t-c,u=o.scrollbar.paddingStart+c,d=o.scrollbar.size-o.scrollbar.paddingEnd-i,a=o.content-o.viewport,b=n==="ltr"?[0,a]:[a*-1,0];return pe([u,d],b)(e)}function q(e,r,o="ltr"){const n=Y(r),t=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,s=r.scrollbar.size-t,c=r.content-r.viewport,i=s-n,u=o==="ltr"?[0,c]:[c*-1,0],d=Ee(e,u);return pe([0,c],[0,i])(d)}function pe(e,r){return o=>{if(e[0]===e[1]||r[0]===r[1])return r[0];const n=(r[1]-r[0])/(e[1]-e[0]);return r[0]+n*(o-e[0])}}function me(e,r){return e>0&&e<r}var Xe=(e,r=()=>{})=>{let o={left:e.scrollLeft,top:e.scrollTop},n=0;return function t(){const s={left:e.scrollLeft,top:e.scrollTop},c=o.left!==s.left,i=o.top!==s.top;(c||i)&&r(),o=s,n=window.requestAnimationFrame(t)}(),()=>window.cancelAnimationFrame(n)};function k(e,r){const o=L(e),n=l.useRef(0);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),l.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(o,r)},[o,r])}function H(e,r){const o=L(r);Re(()=>{let n=0;if(e){const t=new ResizeObserver(()=>{cancelAnimationFrame(n),n=window.requestAnimationFrame(o)});return t.observe(e),()=>{window.cancelAnimationFrame(n),t.unobserve(e)}}},[e,o])}var Ve=se,Ge=ae,Ke=be;function lo({className:e,children:r,...o}){return h.jsxs(Ve,{"data-slot":"scroll-area",className:Z("relative",e),...o,children:[h.jsx(Ge,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:r}),h.jsx($e,{}),h.jsx(Ke,{})]})}function $e({className:e,orientation:r="vertical",...o}){return h.jsx(ie,{"data-slot":"scroll-area-scrollbar",orientation:r,className:Z("flex touch-none p-px transition-colors select-none",r==="vertical"&&"h-full w-2.5 border-l border-l-transparent",r==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",e),...o,children:h.jsx(he,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}export{to as I,ro as R,lo as S,$e as a,oo as c};
