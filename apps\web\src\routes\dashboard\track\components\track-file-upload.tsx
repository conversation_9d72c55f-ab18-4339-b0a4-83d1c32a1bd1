import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Upload, X, File, Music, AlertCircle } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useEdgeStore } from "@/lib/storage";

type TrackFile = {
  fileUrl: string;
  fileKey: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  duration?: number;
};

interface TrackFileUploadProps {
  files: TrackFile[];
  onFilesChange: (files: TrackFile[]) => void;
}

export function TrackFileUpload({ files, onFilesChange }: TrackFileUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { edgestore } = useEdgeStore();

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      setUploading(true);
      setUploadProgress(0);

      try {
        const uploadPromises = acceptedFiles.map(async (file) => {
          // Validate file type
          if (!file.type.startsWith("audio/")) {
            toast.error(`${file.name} is not a valid audio file`);
            return null;
          }

          // Validate file size (200MB max)
          const maxSize = 200 * 1024 * 1024; // 200MB
          if (file.size > maxSize) {
            toast.error(`${file.name} is too large. Maximum size is 200MB.`);
            return null;
          }

          try {
            const res = await edgestore.audio.upload({
              file,
              onProgressChange: (progress) => {
                setUploadProgress(progress);
              },
            });

            // Get audio duration if possible
            let duration: number | undefined;
            try {
              duration = await getAudioDuration(file);
            } catch (error) {
              console.warn("Could not get audio duration:", error);
            }

            return {
              fileUrl: res.url,
              fileKey: res.url.split("/").pop() || "",
              fileName: file.name,
              fileSize: file.size,
              mimeType: file.type,
              duration,
            } as TrackFile;
          } catch (error) {
            console.error("Upload failed:", error);
            toast.error(`Failed to upload ${file.name}`);
            return null;
          }
        });

        const uploadedFiles = await Promise.all(uploadPromises);
        const validFiles = uploadedFiles.filter((file): file is TrackFile => file !== null);

        if (validFiles.length > 0) {
          onFilesChange([...files, ...validFiles]);
          toast.success(`Successfully uploaded ${validFiles.length} file(s)`);
        }
      } catch (error) {
        console.error("Upload error:", error);
        toast.error("Failed to upload files");
      } finally {
        setUploading(false);
        setUploadProgress(0);
      }
    },
    [files, onFilesChange, edgestore]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "audio/*": [".mp3", ".wav", ".flac", ".aac", ".ogg", ".m4a"],
    },
    multiple: true,
    disabled: uploading,
  });

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    onFilesChange(newFiles);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25"}
              ${uploading ? "pointer-events-none opacity-50" : "hover:border-primary hover:bg-primary/5"}
            `}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center space-y-4">
              <div className="p-4 bg-muted rounded-full">
                <Upload className="h-8 w-8 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-medium">
                  {isDragActive ? "Drop audio files here" : "Upload audio files"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  Drag and drop audio files here, or click to browse
                </p>
                <p className="text-xs text-muted-foreground">
                  Supported formats: MP3, WAV, FLAC, AAC, OGG, M4A (Max 200MB each)
                </p>
              </div>
            </div>
          </div>

          {uploading && (
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Uploading...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Uploaded Files List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Uploaded Files ({files.length})</h4>
          <div className="space-y-2">
            {files.map((file, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-primary/10 rounded">
                        <Music className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {file.fileName || "Unknown file"}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                          {file.fileSize && (
                            <span>{formatFileSize(file.fileSize)}</span>
                          )}
                          {file.duration && (
                            <span>{formatDuration(file.duration)}</span>
                          )}
                          {file.mimeType && (
                            <span className="uppercase">
                              {file.mimeType.split("/")[1]}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(file.fileUrl, "_blank")}
                      >
                        <File className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        className="text-destructive hover:text-destructive"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {files.length === 0 && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <AlertCircle className="h-4 w-4" />
          <span>At least one audio file is required</span>
        </div>
      )}
    </div>
  );
}

// Helper function to get audio duration
function getAudioDuration(file: File): Promise<number> {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    const url = URL.createObjectURL(file);

    audio.addEventListener("loadedmetadata", () => {
      URL.revokeObjectURL(url);
      resolve(audio.duration);
    });

    audio.addEventListener("error", () => {
      URL.revokeObjectURL(url);
      reject(new Error("Failed to load audio metadata"));
    });

    audio.src = url;
  });
}
