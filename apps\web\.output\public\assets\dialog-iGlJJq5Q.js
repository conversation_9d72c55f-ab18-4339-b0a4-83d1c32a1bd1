import{c as it,r as s,j as g,h as I,Z as De,_ as st}from"./main-B9Fv5CdX.js";import{u as O,e as ue,a as W}from"./button-Ispz1G12.js";import{P as A,d as ct}from"./label-CNQvdrLZ.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ut=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],lt=it("X",ut);function dt(e,t){const n=s.createContext(t),r=a=>{const{children:c,...i}=a,v=s.useMemo(()=>i,Object.values(i));return g.jsx(n.Provider,{value:v,children:c})};r.displayName=e+"Provider";function o(a){const c=s.useContext(n);if(c)return c;if(t!==void 0)return t;throw new Error(`\`${a}\` must be used within \`${e}\``)}return[r,o]}function Pe(e,t=[]){let n=[];function r(a,c){const i=s.createContext(c),v=n.length;n=[...n,c];const l=f=>{var y;const{scope:m,children:p,...w}=f,u=((y=m==null?void 0:m[e])==null?void 0:y[v])||i,h=s.useMemo(()=>w,Object.values(w));return g.jsx(u.Provider,{value:h,children:p})};l.displayName=a+"Provider";function d(f,m){var u;const p=((u=m==null?void 0:m[e])==null?void 0:u[v])||i,w=s.useContext(p);if(w)return w;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${a}\``)}return[l,d]}const o=()=>{const a=n.map(c=>s.createContext(c));return function(i){const v=(i==null?void 0:i[e])||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:v}}),[i,v])}};return o.scopeName=e,[r,ft(o,...t)]}function ft(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=r.reduce((i,{useScope:v,scopeName:l})=>{const f=v(a)[`__scope${l}`];return{...i,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}function Hn(e){const t=e+"CollectionProvider",[n,r]=Pe(t),[o,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=u=>{const{scope:h,children:y}=u,E=I.useRef(null),b=I.useRef(new Map).current;return g.jsx(o,{scope:h,itemMap:b,collectionRef:E,children:y})};c.displayName=t;const i=e+"CollectionSlot",v=ue(i),l=I.forwardRef((u,h)=>{const{scope:y,children:E}=u,b=a(i,y),C=O(h,b.collectionRef);return g.jsx(v,{ref:C,children:E})});l.displayName=i;const d=e+"CollectionItemSlot",f="data-radix-collection-item",m=ue(d),p=I.forwardRef((u,h)=>{const{scope:y,children:E,...b}=u,C=I.useRef(null),x=O(h,C),R=a(d,y);return I.useEffect(()=>(R.itemMap.set(C,{ref:C,...b}),()=>void R.itemMap.delete(C))),g.jsx(m,{[f]:"",ref:x,children:E})});p.displayName=d;function w(u){const h=a(e+"CollectionConsumer",u);return I.useCallback(()=>{const E=h.collectionRef.current;if(!E)return[];const b=Array.from(E.querySelectorAll(`[${f}]`));return Array.from(h.itemMap.values()).sort((R,S)=>b.indexOf(R.ref.current)-b.indexOf(S.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:c,Slot:l,ItemSlot:p},w,r]}function M(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}var U=globalThis!=null&&globalThis.document?s.useLayoutEffect:()=>{},vt=De[" useInsertionEffect ".trim().toString()]||U;function mt({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,a,c]=ht({defaultProp:t,onChange:n}),i=e!==void 0,v=i?e:o;{const d=s.useRef(e!==void 0);s.useEffect(()=>{const f=d.current;f!==i&&console.warn(`${r} is changing from ${f?"controlled":"uncontrolled"} to ${i?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=i},[i,r])}const l=s.useCallback(d=>{var f;if(i){const m=gt(d)?d(e):d;m!==e&&((f=c.current)==null||f.call(c,m))}else a(d)},[i,e,a,c]);return[v,l]}function ht({defaultProp:e,onChange:t}){const[n,r]=s.useState(e),o=s.useRef(n),a=s.useRef(t);return vt(()=>{a.current=t},[t]),s.useEffect(()=>{var c;o.current!==n&&((c=a.current)==null||c.call(a,n),o.current=n)},[n,o]),[n,r,a]}function gt(e){return typeof e=="function"}function pt(e,t){return s.useReducer((n,r)=>t[n][r]??n,e)}var q=e=>{const{present:t,children:n}=e,r=yt(t),o=typeof n=="function"?n({present:r.isPresent}):s.Children.only(n),a=O(r.ref,Et(o));return typeof n=="function"||r.isPresent?s.cloneElement(o,{ref:a}):null};q.displayName="Presence";function yt(e){const[t,n]=s.useState(),r=s.useRef(null),o=s.useRef(e),a=s.useRef("none"),c=e?"mounted":"unmounted",[i,v]=pt(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return s.useEffect(()=>{const l=z(r.current);a.current=i==="mounted"?l:"none"},[i]),U(()=>{const l=r.current,d=o.current;if(d!==e){const m=a.current,p=z(l);e?v("MOUNT"):p==="none"||(l==null?void 0:l.display)==="none"?v("UNMOUNT"):v(d&&m!==p?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,v]),U(()=>{if(t){let l;const d=t.ownerDocument.defaultView??window,f=p=>{const u=z(r.current).includes(p.animationName);if(p.target===t&&u&&(v("ANIMATION_END"),!o.current)){const h=t.style.animationFillMode;t.style.animationFillMode="forwards",l=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=h)})}},m=p=>{p.target===t&&(a.current=z(r.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{d.clearTimeout(l),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else v("ANIMATION_END")},[t,v]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:s.useCallback(l=>{r.current=l?getComputedStyle(l):null,n(l)},[])}}function z(e){return(e==null?void 0:e.animationName)||"none"}function Et(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var bt=De[" useId ".trim().toString()]||(()=>{}),Ct=0;function te(e){const[t,n]=s.useState(bt());return U(()=>{n(r=>r??String(Ct++))},[e]),e||(t?`radix-${t}`:"")}var St=s.createContext(void 0);function Zn(e){const t=s.useContext(St);return e||t||"ltr"}function $(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function wt(e,t=globalThis==null?void 0:globalThis.document){const n=$(e);s.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var xt="DismissableLayer",le="dismissableLayer.update",Rt="dismissableLayer.pointerDownOutside",Nt="dismissableLayer.focusOutside",he,Oe=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ae=s.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:c,onDismiss:i,...v}=e,l=s.useContext(Oe),[d,f]=s.useState(null),m=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,p]=s.useState({}),w=O(t,S=>f(S)),u=Array.from(l.layers),[h]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),y=u.indexOf(h),E=d?u.indexOf(d):-1,b=l.layersWithOutsidePointerEventsDisabled.size>0,C=E>=y,x=Ot(S=>{const P=S.target,B=[...l.branches].some(ee=>ee.contains(P));!C||B||(o==null||o(S),c==null||c(S),S.defaultPrevented||i==null||i())},m),R=At(S=>{const P=S.target;[...l.branches].some(ee=>ee.contains(P))||(a==null||a(S),c==null||c(S),S.defaultPrevented||i==null||i())},m);return wt(S=>{E===l.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&i&&(S.preventDefault(),i()))},m),s.useEffect(()=>{if(d)return n&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(he=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(d)),l.layers.add(d),ge(),()=>{n&&l.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=he)}},[d,m,n,l]),s.useEffect(()=>()=>{d&&(l.layers.delete(d),l.layersWithOutsidePointerEventsDisabled.delete(d),ge())},[d,l]),s.useEffect(()=>{const S=()=>p({});return document.addEventListener(le,S),()=>document.removeEventListener(le,S)},[]),g.jsx(A.div,{...v,ref:w,style:{pointerEvents:b?C?"auto":"none":void 0,...e.style},onFocusCapture:M(e.onFocusCapture,R.onFocusCapture),onBlurCapture:M(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:M(e.onPointerDownCapture,x.onPointerDownCapture)})});Ae.displayName=xt;var Dt="DismissableLayerBranch",Pt=s.forwardRef((e,t)=>{const n=s.useContext(Oe),r=s.useRef(null),o=O(t,r);return s.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),g.jsx(A.div,{...e,ref:o})});Pt.displayName=Dt;function Ot(e,t=globalThis==null?void 0:globalThis.document){const n=$(e),r=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{const a=i=>{if(i.target&&!r.current){let v=function(){Te(Rt,n,l,{discrete:!0})};const l={originalEvent:i};i.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=v,t.addEventListener("click",o.current,{once:!0})):v()}else t.removeEventListener("click",o.current);r.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function At(e,t=globalThis==null?void 0:globalThis.document){const n=$(e),r=s.useRef(!1);return s.useEffect(()=>{const o=a=>{a.target&&!r.current&&Te(Nt,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function ge(){const e=new CustomEvent(le);document.dispatchEvent(e)}function Te(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?ct(o,a):o.dispatchEvent(a)}var ne="focusScope.autoFocusOnMount",re="focusScope.autoFocusOnUnmount",pe={bubbles:!1,cancelable:!0},Tt="FocusScope",Me=s.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...c}=e,[i,v]=s.useState(null),l=$(o),d=$(a),f=s.useRef(null),m=O(t,u=>v(u)),p=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(r){let u=function(b){if(p.paused||!i)return;const C=b.target;i.contains(C)?f.current=C:T(f.current,{select:!0})},h=function(b){if(p.paused||!i)return;const C=b.relatedTarget;C!==null&&(i.contains(C)||T(f.current,{select:!0}))},y=function(b){if(document.activeElement===document.body)for(const x of b)x.removedNodes.length>0&&T(i)};document.addEventListener("focusin",u),document.addEventListener("focusout",h);const E=new MutationObserver(y);return i&&E.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",u),document.removeEventListener("focusout",h),E.disconnect()}}},[r,i,p.paused]),s.useEffect(()=>{if(i){Ee.add(p);const u=document.activeElement;if(!i.contains(u)){const y=new CustomEvent(ne,pe);i.addEventListener(ne,l),i.dispatchEvent(y),y.defaultPrevented||(Mt(Ft(Ie(i)),{select:!0}),document.activeElement===u&&T(i))}return()=>{i.removeEventListener(ne,l),setTimeout(()=>{const y=new CustomEvent(re,pe);i.addEventListener(re,d),i.dispatchEvent(y),y.defaultPrevented||T(u??document.body,{select:!0}),i.removeEventListener(re,d),Ee.remove(p)},0)}}},[i,l,d,p]);const w=s.useCallback(u=>{if(!n&&!r||p.paused)return;const h=u.key==="Tab"&&!u.altKey&&!u.ctrlKey&&!u.metaKey,y=document.activeElement;if(h&&y){const E=u.currentTarget,[b,C]=It(E);b&&C?!u.shiftKey&&y===C?(u.preventDefault(),n&&T(b,{select:!0})):u.shiftKey&&y===b&&(u.preventDefault(),n&&T(C,{select:!0})):y===E&&u.preventDefault()}},[n,r,p.paused]);return g.jsx(A.div,{tabIndex:-1,...c,ref:m,onKeyDown:w})});Me.displayName=Tt;function Mt(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(T(r,{select:t}),document.activeElement!==n)return}function It(e){const t=Ie(e),n=ye(t,e),r=ye(t.reverse(),e);return[n,r]}function Ie(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ye(e,t){for(const n of e)if(!_t(n,{upTo:t}))return n}function _t(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Lt(e){return e instanceof HTMLInputElement&&"select"in e}function T(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Lt(e)&&t&&e.select()}}var Ee=kt();function kt(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=be(e,t),e.unshift(t)},remove(t){var n;e=be(e,t),(n=e[0])==null||n.resume()}}}function be(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Ft(e){return e.filter(t=>t.tagName!=="A")}var jt="Portal",_e=s.forwardRef((e,t)=>{var i;const{container:n,...r}=e,[o,a]=s.useState(!1);U(()=>a(!0),[]);const c=n||o&&((i=globalThis==null?void 0:globalThis.document)==null?void 0:i.body);return c?st.createPortal(g.jsx(A.div,{...r,ref:t}),c):null});_e.displayName=jt;var oe=0;function Wt(){s.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ce()),document.body.insertAdjacentElement("beforeend",e[1]??Ce()),oe++,()=>{oe===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),oe--}},[])}function Ce(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var D=function(){return D=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},D.apply(this,arguments)};function Le(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function qn(e,t,n,r){function o(a){return a instanceof n?a:new n(function(c){c(a)})}return new(n||(n=Promise))(function(a,c){function i(d){try{l(r.next(d))}catch(f){c(f)}}function v(d){try{l(r.throw(d))}catch(f){c(f)}}function l(d){d.done?a(d.value):o(d.value).then(i,v)}l((r=r.apply(e,t||[])).next())})}function Bt(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var Y="right-scroll-bar-position",H="width-before-scroll-bar",Ut="with-scroll-bars-hidden",$t="--removed-body-scroll-bar-size";function ae(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function zt(e,t){var n=s.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Kt=typeof window<"u"?s.useLayoutEffect:s.useEffect,Se=new WeakMap;function Vt(e,t){var n=zt(null,function(r){return e.forEach(function(o){return ae(o,r)})});return Kt(function(){var r=Se.get(n);if(r){var o=new Set(r),a=new Set(e),c=n.current;o.forEach(function(i){a.has(i)||ae(i,null)}),a.forEach(function(i){o.has(i)||ae(i,c)})}Se.set(n,e)},[e]),n}function Gt(e){return e}function Xt(e,t){t===void 0&&(t=Gt);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var c=t(a,r);return n.push(c),function(){n=n.filter(function(i){return i!==c})}},assignSyncMedium:function(a){for(r=!0;n.length;){var c=n;n=[],c.forEach(a)}n={push:function(i){return a(i)},filter:function(){return n}}},assignMedium:function(a){r=!0;var c=[];if(n.length){var i=n;n=[],i.forEach(a),c=n}var v=function(){var d=c;c=[],d.forEach(a)},l=function(){return Promise.resolve().then(v)};l(),n={push:function(d){c.push(d),l()},filter:function(d){return c=c.filter(d),n}}}};return o}function Yt(e){e===void 0&&(e={});var t=Xt(null);return t.options=D({async:!0,ssr:!1},e),t}var ke=function(e){var t=e.sideCar,n=Le(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return s.createElement(r,D({},n))};ke.isSideCarExport=!0;function Ht(e,t){return e.useMedium(t),ke}var Fe=Yt(),ie=function(){},Q=s.forwardRef(function(e,t){var n=s.useRef(null),r=s.useState({onScrollCapture:ie,onWheelCapture:ie,onTouchMoveCapture:ie}),o=r[0],a=r[1],c=e.forwardProps,i=e.children,v=e.className,l=e.removeScrollBar,d=e.enabled,f=e.shards,m=e.sideCar,p=e.noRelative,w=e.noIsolation,u=e.inert,h=e.allowPinchZoom,y=e.as,E=y===void 0?"div":y,b=e.gapMode,C=Le(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=m,R=Vt([n,t]),S=D(D({},C),o);return s.createElement(s.Fragment,null,d&&s.createElement(x,{sideCar:Fe,removeScrollBar:l,shards:f,noRelative:p,noIsolation:w,inert:u,setCallbacks:a,allowPinchZoom:!!h,lockRef:n,gapMode:b}),c?s.cloneElement(s.Children.only(i),D(D({},S),{ref:R})):s.createElement(E,D({},S,{className:v,ref:R}),i))});Q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Q.classNames={fullWidth:H,zeroRight:Y};var Zt=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function qt(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Zt();return t&&e.setAttribute("nonce",t),e}function Qt(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Jt(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var en=function(){var e=0,t=null;return{add:function(n){e==0&&(t=qt())&&(Qt(t,n),Jt(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tn=function(){var e=en();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},je=function(){var e=tn(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},nn={left:0,top:0,right:0,gap:0},se=function(e){return parseInt(e||"",10)||0},rn=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[se(n),se(r),se(o)]},on=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return nn;var t=rn(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},an=je(),j="data-scroll-locked",sn=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,i=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Ut,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(i,"px ").concat(r,`;
  }
  body[`).concat(j,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(i,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Y,` {
    right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(H,` {
    margin-right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(Y," .").concat(Y,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(H," .").concat(H,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(j,`] {
    `).concat($t,": ").concat(i,`px;
  }
`)},we=function(){var e=parseInt(document.body.getAttribute(j)||"0",10);return isFinite(e)?e:0},cn=function(){s.useEffect(function(){return document.body.setAttribute(j,(we()+1).toString()),function(){var e=we()-1;e<=0?document.body.removeAttribute(j):document.body.setAttribute(j,e.toString())}},[])},un=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;cn();var a=s.useMemo(function(){return on(o)},[o]);return s.createElement(an,{styles:sn(a,!t,o,n?"":"!important")})},de=!1;if(typeof window<"u")try{var K=Object.defineProperty({},"passive",{get:function(){return de=!0,!0}});window.addEventListener("test",K,K),window.removeEventListener("test",K,K)}catch{de=!1}var L=de?{passive:!1}:!1,ln=function(e){return e.tagName==="TEXTAREA"},We=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!ln(e)&&n[t]==="visible")},dn=function(e){return We(e,"overflowY")},fn=function(e){return We(e,"overflowX")},xe=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Be(e,r);if(o){var a=Ue(e,r),c=a[1],i=a[2];if(c>i)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},vn=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},mn=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Be=function(e,t){return e==="v"?dn(t):fn(t)},Ue=function(e,t){return e==="v"?vn(t):mn(t)},hn=function(e,t){return e==="h"&&t==="rtl"?-1:1},gn=function(e,t,n,r,o){var a=hn(e,window.getComputedStyle(t).direction),c=a*r,i=n.target,v=t.contains(i),l=!1,d=c>0,f=0,m=0;do{if(!i)break;var p=Ue(e,i),w=p[0],u=p[1],h=p[2],y=u-h-a*w;(w||y)&&Be(e,i)&&(f+=y,m+=w);var E=i.parentNode;i=E&&E.nodeType===Node.DOCUMENT_FRAGMENT_NODE?E.host:E}while(!v&&i!==document.body||v&&(t.contains(i)||t===i));return(d&&Math.abs(f)<1||!d&&Math.abs(m)<1)&&(l=!0),l},V=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Re=function(e){return[e.deltaX,e.deltaY]},Ne=function(e){return e&&"current"in e?e.current:e},pn=function(e,t){return e[0]===t[0]&&e[1]===t[1]},yn=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},En=0,k=[];function bn(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(En++)[0],a=s.useState(je)[0],c=s.useRef(e);s.useEffect(function(){c.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var u=Bt([e.lockRef.current],(e.shards||[]).map(Ne),!0).filter(Boolean);return u.forEach(function(h){return h.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),u.forEach(function(h){return h.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=s.useCallback(function(u,h){if("touches"in u&&u.touches.length===2||u.type==="wheel"&&u.ctrlKey)return!c.current.allowPinchZoom;var y=V(u),E=n.current,b="deltaX"in u?u.deltaX:E[0]-y[0],C="deltaY"in u?u.deltaY:E[1]-y[1],x,R=u.target,S=Math.abs(b)>Math.abs(C)?"h":"v";if("touches"in u&&S==="h"&&R.type==="range")return!1;var P=xe(S,R);if(!P)return!0;if(P?x=S:(x=S==="v"?"h":"v",P=xe(S,R)),!P)return!1;if(!r.current&&"changedTouches"in u&&(b||C)&&(r.current=x),!x)return!0;var B=r.current||x;return gn(B,h,u,B==="h"?b:C)},[]),v=s.useCallback(function(u){var h=u;if(!(!k.length||k[k.length-1]!==a)){var y="deltaY"in h?Re(h):V(h),E=t.current.filter(function(x){return x.name===h.type&&(x.target===h.target||h.target===x.shadowParent)&&pn(x.delta,y)})[0];if(E&&E.should){h.cancelable&&h.preventDefault();return}if(!E){var b=(c.current.shards||[]).map(Ne).filter(Boolean).filter(function(x){return x.contains(h.target)}),C=b.length>0?i(h,b[0]):!c.current.noIsolation;C&&h.cancelable&&h.preventDefault()}}},[]),l=s.useCallback(function(u,h,y,E){var b={name:u,delta:h,target:y,should:E,shadowParent:Cn(y)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(C){return C!==b})},1)},[]),d=s.useCallback(function(u){n.current=V(u),r.current=void 0},[]),f=s.useCallback(function(u){l(u.type,Re(u),u.target,i(u,e.lockRef.current))},[]),m=s.useCallback(function(u){l(u.type,V(u),u.target,i(u,e.lockRef.current))},[]);s.useEffect(function(){return k.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:m}),document.addEventListener("wheel",v,L),document.addEventListener("touchmove",v,L),document.addEventListener("touchstart",d,L),function(){k=k.filter(function(u){return u!==a}),document.removeEventListener("wheel",v,L),document.removeEventListener("touchmove",v,L),document.removeEventListener("touchstart",d,L)}},[]);var p=e.removeScrollBar,w=e.inert;return s.createElement(s.Fragment,null,w?s.createElement(a,{styles:yn(o)}):null,p?s.createElement(un,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Cn(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Sn=Ht(Fe,bn);var $e=s.forwardRef(function(e,t){return s.createElement(Q,D({},e,{ref:t,sideCar:Sn}))});$e.classNames=Q.classNames;var wn=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},F=new WeakMap,G=new WeakMap,X={},ce=0,ze=function(e){return e&&(e.host||ze(e.parentNode))},xn=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=ze(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Rn=function(e,t,n,r){var o=xn(t,Array.isArray(e)?e:[e]);X[n]||(X[n]=new WeakMap);var a=X[n],c=[],i=new Set,v=new Set(o),l=function(f){!f||i.has(f)||(i.add(f),l(f.parentNode))};o.forEach(l);var d=function(f){!f||v.has(f)||Array.prototype.forEach.call(f.children,function(m){if(i.has(m))d(m);else try{var p=m.getAttribute(r),w=p!==null&&p!=="false",u=(F.get(m)||0)+1,h=(a.get(m)||0)+1;F.set(m,u),a.set(m,h),c.push(m),u===1&&w&&G.set(m,!0),h===1&&m.setAttribute(n,"true"),w||m.setAttribute(r,"true")}catch(y){console.error("aria-hidden: cannot operate on ",m,y)}})};return d(t),i.clear(),ce++,function(){c.forEach(function(f){var m=F.get(f)-1,p=a.get(f)-1;F.set(f,m),a.set(f,p),m||(G.has(f)||f.removeAttribute(r),G.delete(f)),p||f.removeAttribute(n)}),ce--,ce||(F=new WeakMap,F=new WeakMap,G=new WeakMap,X={})}},Nn=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=wn(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Rn(r,o,n,"aria-hidden")):function(){return null}},J="Dialog",[Ke,Qn]=Pe(J),[Dn,N]=Ke(J),Ve=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:c=!0}=e,i=s.useRef(null),v=s.useRef(null),[l,d]=mt({prop:r,defaultProp:o??!1,onChange:a,caller:J});return g.jsx(Dn,{scope:t,triggerRef:i,contentRef:v,contentId:te(),titleId:te(),descriptionId:te(),open:l,onOpenChange:d,onOpenToggle:s.useCallback(()=>d(f=>!f),[d]),modal:c,children:n})};Ve.displayName=J;var Ge="DialogTrigger",Xe=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=N(Ge,n),a=O(t,o.triggerRef);return g.jsx(A.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":me(o.open),...r,ref:a,onClick:M(e.onClick,o.onOpenToggle)})});Xe.displayName=Ge;var fe="DialogPortal",[Pn,Ye]=Ke(fe,{forceMount:void 0}),He=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=N(fe,t);return g.jsx(Pn,{scope:t,forceMount:n,children:s.Children.map(r,c=>g.jsx(q,{present:n||a.open,children:g.jsx(_e,{asChild:!0,container:o,children:c})}))})};He.displayName=fe;var Z="DialogOverlay",Ze=s.forwardRef((e,t)=>{const n=Ye(Z,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=N(Z,e.__scopeDialog);return a.modal?g.jsx(q,{present:r||a.open,children:g.jsx(An,{...o,ref:t})}):null});Ze.displayName=Z;var On=ue("DialogOverlay.RemoveScroll"),An=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=N(Z,n);return g.jsx($e,{as:On,allowPinchZoom:!0,shards:[o.contentRef],children:g.jsx(A.div,{"data-state":me(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_="DialogContent",qe=s.forwardRef((e,t)=>{const n=Ye(_,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=N(_,e.__scopeDialog);return g.jsx(q,{present:r||a.open,children:a.modal?g.jsx(Tn,{...o,ref:t}):g.jsx(Mn,{...o,ref:t})})});qe.displayName=_;var Tn=s.forwardRef((e,t)=>{const n=N(_,e.__scopeDialog),r=s.useRef(null),o=O(t,n.contentRef,r);return s.useEffect(()=>{const a=r.current;if(a)return Nn(a)},[]),g.jsx(Qe,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:M(e.onCloseAutoFocus,a=>{var c;a.preventDefault(),(c=n.triggerRef.current)==null||c.focus()}),onPointerDownOutside:M(e.onPointerDownOutside,a=>{const c=a.detail.originalEvent,i=c.button===0&&c.ctrlKey===!0;(c.button===2||i)&&a.preventDefault()}),onFocusOutside:M(e.onFocusOutside,a=>a.preventDefault())})}),Mn=s.forwardRef((e,t)=>{const n=N(_,e.__scopeDialog),r=s.useRef(!1),o=s.useRef(!1);return g.jsx(Qe,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var c,i;(c=e.onCloseAutoFocus)==null||c.call(e,a),a.defaultPrevented||(r.current||(i=n.triggerRef.current)==null||i.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{var v,l;(v=e.onInteractOutside)==null||v.call(e,a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=a.target;((l=n.triggerRef.current)==null?void 0:l.contains(c))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),Qe=s.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...c}=e,i=N(_,n),v=s.useRef(null),l=O(t,v);return Wt(),g.jsxs(g.Fragment,{children:[g.jsx(Me,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:g.jsx(Ae,{role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":me(i.open),...c,ref:l,onDismiss:()=>i.onOpenChange(!1)})}),g.jsxs(g.Fragment,{children:[g.jsx(In,{titleId:i.titleId}),g.jsx(Ln,{contentRef:v,descriptionId:i.descriptionId})]})]})}),ve="DialogTitle",Je=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=N(ve,n);return g.jsx(A.h2,{id:o.titleId,...r,ref:t})});Je.displayName=ve;var et="DialogDescription",tt=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=N(et,n);return g.jsx(A.p,{id:o.descriptionId,...r,ref:t})});tt.displayName=et;var nt="DialogClose",rt=s.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=N(nt,n);return g.jsx(A.button,{type:"button",...r,ref:t,onClick:M(e.onClick,()=>o.onOpenChange(!1))})});rt.displayName=nt;function me(e){return e?"open":"closed"}var ot="DialogTitleWarning",[Jn,at]=dt(ot,{contentName:_,titleName:ve,docsSlug:"dialog"}),In=({titleId:e})=>{const t=at(ot),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},_n="DialogDescriptionWarning",Ln=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${at(_n).contentName}}.`;return s.useEffect(()=>{var a;const o=(a=e.current)==null?void 0:a.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},kn=Ve,Fn=Xe,jn=He,Wn=Ze,Bn=qe,Un=Je,$n=tt,zn=rt;function er(e,[t,n]){return Math.min(n,Math.max(t,e))}function tr({...e}){return g.jsx(kn,{"data-slot":"dialog",...e})}function nr({...e}){return g.jsx(Fn,{"data-slot":"dialog-trigger",...e})}function Kn({...e}){return g.jsx(jn,{"data-slot":"dialog-portal",...e})}function Vn({className:e,...t}){return g.jsx(Wn,{"data-slot":"dialog-overlay",className:W("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function rr({className:e,children:t,showCloseButton:n=!0,...r}){return g.jsxs(Kn,{"data-slot":"dialog-portal",children:[g.jsx(Vn,{}),g.jsxs(Bn,{"data-slot":"dialog-content",className:W("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-6 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg overflow-y-auto max-h-[calc(90vh-2rem)] sm:max-h-[calc(100vh-4rem)]",e),...r,children:[t,n&&g.jsxs(zn,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[g.jsx(lt,{}),g.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function or({className:e,...t}){return g.jsx("div",{"data-slot":"dialog-header",className:W("flex flex-col gap-2 text-left",e),...t})}function ar({className:e,...t}){return g.jsx("div",{"data-slot":"dialog-footer",className:W("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function ir({className:e,...t}){return g.jsx(Un,{"data-slot":"dialog-title",className:W("text-lg leading-none font-semibold",e),...t})}function sr({className:e,...t}){return g.jsx($n,{"data-slot":"dialog-description",className:W("text-muted-foreground text-sm",e),...t})}export{Bn as C,Ae as D,Me as F,Wn as O,q as P,kn as R,Un as T,Jn as W,lt as X,qn as _,te as a,M as b,Pe as c,U as d,$ as e,_e as f,zn as g,$n as h,jn as i,tr as j,nr as k,rr as l,or as m,ir as n,sr as o,ar as p,Zn as q,Qn as r,Fn as s,Hn as t,mt as u,Nn as v,Wt as w,$e as x,er as y};
