import{c as oe,r as u,a as R,t as C,j as e,L as ce,g as de,q as me,k as B}from"./main-CiHxKC0e.js";import{a as E}from"./auth-client-Bm508aVF.js";import{E as ue,A as K,a as O,b as Q,c as X,d as J,e as W,f as Y,g as Z,h as ee,u as he,t as xe,v as ge,w as pe,x as je,y as fe,C as be,T as Ce,i as Ne,j as S,k as ve,l as I,m as we,n as L,P as ye,o as Se,p as A,q as De,r as ze,s as ke}from"./table-TBKnZXRI.js";import{u as Le}from"./useQuery-DciuO3_z.js";import{u as V}from"./useMutation-C9Oj6nVm.js";import{B as g,a as $}from"./button-mzA_gOdo.js";import{D as se,a as ae,b as te,e as Ae,f as _,d as Te,c as Ie,k as Me,C as Pe}from"./dropdown-menu-BXhbheYO.js";import{L as M,I as P}from"./label-C9y0GF5L.js";import{P as Fe,h as Re,i as Ee,S as Ve,d as He,e as Ue,f as qe,g as Be}from"./select-Be91QLyZ.js";import{C as $e}from"./create-label-dialog-B5nj_Mv8.js";import{T as ne,C as G}from"./checkbox-CnzziXQ_.js";import{j as _e,k as Ge,l as Ke,m as Oe,n as Qe,o as Xe,p as Je}from"./dialog-B3qaM3HV.js";import{S as We}from"./square-pen-cru0dwrj.js";import{C as F}from"./circle-alert-CqYvwAi4.js";import{L as Ye}from"./list-filter-BGL9woXK.js";import{C as Ze}from"./circle-x-6nZ_0hIQ.js";import"./scroll-area-ASo8OkdS.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],ss=oe("Tag",es);function as({children:r,label:l,onLabelUpdated:o}){var D;const[a,i]=u.useState(!1),p=R(),{data:f}=E.useSession(),x=((D=f==null?void 0:f.user)==null?void 0:D.role)==="admin",[N,d]=u.useState({name:""});u.useEffect(()=>{a&&d({name:l.name||""})},[a,l]);const h=V({mutationFn:async c=>p.label.update.mutate(c),onSuccess:()=>{C.success("Label updated successfully"),i(!1),o()},onError:c=>{console.error("Failed to update label:",c),C.error("Failed to update label: "+(c.message||"Unknown error"))}}),v=async c=>{if(c.preventDefault(),!N.name.trim()){C.error("Label name is required");return}try{await h.mutateAsync({id:l.id,name:N.name.trim()})}catch{}},T=(c,w)=>{d(n=>({...n,[c]:w}))};return e.jsxs(_e,{open:a,onOpenChange:i,children:[e.jsx(Ge,{asChild:!0,children:r}),e.jsxs(Ke,{className:"sm:max-w-[500px]",children:[e.jsxs(Oe,{children:[e.jsxs(Qe,{children:["Edit Label: ",l.name]}),e.jsx(Xe,{children:"Update the label information."})]}),e.jsxs("form",{onSubmit:v,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[x&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(M,{htmlFor:"createdBy",children:"Created By"}),e.jsx(P,{id:"createdBy",value:`${l.user.email} | ${l.user.name}`,disabled:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(M,{htmlFor:"name",className:"block",children:["Label Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",role:"region","aria-live":"polite",children:"This is the name that will be displayed for the label. Please make sure to use the correct name."}),e.jsx(P,{id:"name",placeholder:"Enter label name",value:N.name,onChange:c=>T("name",c.target.value),required:!0,disabled:h.isPending})]})]}),e.jsxs(Je,{children:[e.jsx(g,{type:"button",variant:"outline",onClick:()=>i(!1),disabled:h.isPending,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:h.isPending||!N.name.trim(),children:h.isPending?e.jsxs(e.Fragment,{children:[e.jsx(ce,{className:"h-4 w-4 animate-spin"}),"Updating..."]}):"Update Label"})]})]})]})]})}function ts({row:r,onLabelDeleted:l}){var f;const o=R(),a=V({mutationFn:async()=>o.label.delete.mutate({id:r.original.id}),onSuccess:()=>{C.success(`Successfully deleted label ${r.original.name}`),l()},onError:x=>{console.error("Failed to delete label:",x),C.error("Failed to delete label: "+(x.message||"Unknown error"))}}),i=async()=>{try{await a.mutateAsync()}catch{}},p=((f=r.original.artists)==null?void 0:f.length)||0;return e.jsxs(se,{children:[e.jsx(ae,{asChild:!0,children:e.jsx("div",{className:"flex justify-end",children:e.jsx(g,{size:"icon",variant:"ghost",className:"shadow-none cursor-pointer","aria-label":"Edit item",children:e.jsx(ue,{size:16,"aria-hidden":"true"})})})}),e.jsxs(te,{align:"end",children:[e.jsx(Ae,{children:e.jsx(as,{label:r.original,onLabelUpdated:l,children:e.jsxs(_,{onSelect:x=>x.preventDefault(),className:"cursor-pointer",children:[e.jsx(We,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit Label"})]})})}),e.jsx(Te,{className:"h-[2px]"}),e.jsxs(K,{children:[e.jsx(O,{asChild:!0,children:e.jsx(_,{className:"text-destructive focus:text-destructive cursor-pointer",onSelect:x=>x.preventDefault(),children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ne,{size:16,className:"text-destructive focus:text-destructive"}),e.jsx("span",{children:"Delete"})]})})}),e.jsxs(Q,{children:[e.jsxs("div",{className:"flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4",children:[e.jsx("div",{className:"flex size-9 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(F,{className:"opacity-80",size:16})}),e.jsxs(X,{children:[e.jsx(J,{children:"Delete Label"}),e.jsxs(W,{children:["Are you sure you want to delete"," ",e.jsx("strong",{children:r.original.name}),"?"," ",p>0&&e.jsxs(e.Fragment,{children:["This label has"," ",e.jsxs("strong",{children:[p," artist",p!==1?"s":""]})," ","associated with it. These artists will be unassigned from the label but will remain in the system."]}),"This action cannot be undone and will permanently remove the label."]})]})]}),e.jsxs(Y,{children:[e.jsx(Z,{children:"Cancel"}),e.jsx(ee,{onClick:i,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:a.isPending,children:a.isPending?"Deleting...":"Delete Label"})]})]})]})]})]})}const ns=(r,l,o)=>{const a=`${r.original.name}`.toLowerCase(),i=(o??"").toLowerCase();return a.includes(i)},is=(r,l)=>{const o=[{id:"select",header:({table:a})=>e.jsx(G,{checked:a.getIsAllPageRowsSelected()||a.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:i=>a.toggleAllPageRowsSelected(!!i),"aria-label":"Select all"}),cell:({row:a})=>e.jsx(G,{checked:a.getIsSelected(),onCheckedChange:i=>a.toggleSelected(!!i),"aria-label":"Select row"}),size:28,enableSorting:!1,enableHiding:!1},{header:"Label Name",accessorKey:"name",cell:({row:a})=>e.jsx("div",{className:"font-medium",children:a.getValue("name")}),size:200,filterFn:ns,enableHiding:!1},{header:"Artists Count",accessorKey:"artists",cell:({row:a})=>{const i=a.original.artists||[];return e.jsx("div",{className:"text-sm",children:i.length>0?e.jsxs("span",{children:[i.length," artist",i.length!==1?"s":""]}):e.jsx("span",{className:"text-muted-foreground",children:"No artists"})})},size:120,enableSorting:!1},{header:"Created At",accessorKey:"createdAt",cell:({row:a})=>{const i=new Date(a.getValue("createdAt"));return e.jsx("div",{className:"text-sm text-muted-foreground",children:i.toLocaleDateString()})},size:120}];return l&&o.push({header:"Created By",accessorKey:"user",cell:({row:a})=>{var i,p;return e.jsxs("div",{className:"text-sm min-w-0 truncate",children:[((i=a.original.user)==null?void 0:i.email)??"-"," | ",((p=a.original.user)==null?void 0:p.name)??"-"]})},size:200}),o.push({id:"actions",header:()=>e.jsx("span",{className:"sr-only",children:"Actions"}),cell:({row:a})=>e.jsx(ts,{row:a,onLabelDeleted:r}),size:80,enableHiding:!1}),o};function ls(){var U,q;const r=u.useId(),l=de(),o=R(),{data:a}=E.useSession(),i=((U=a==null?void 0:a.user)==null?void 0:U.role)==="admin",[p,f]=u.useState([]),[x,N]=u.useState({}),[d,h]=u.useState({pageIndex:0,pageSize:10}),v=u.useRef(null),[T,D]=u.useState([{id:"name",desc:!1}]),[c,w]=u.useState(""),{data:n,isLoading:b,error:H,refetch:y}=Le(l.label.getAll.queryOptions({page:d.pageIndex+1,limit:d.pageSize,search:c||void 0})),ie=V({mutationFn:async s=>o.label.delete.mutate({id:s}),onSuccess:()=>{C.success("Label deleted successfully"),y()},onError:s=>{C.error(`Failed to delete label: ${s.message}`)}}),le=u.useMemo(()=>((n==null?void 0:n.labels)||[]).map(t=>({...t,createdAt:new Date(t.createdAt),updatedAt:new Date(t.updatedAt)})),[n==null?void 0:n.labels]),re=async()=>{const t=j.getSelectedRowModel().rows.map(m=>m.original.id);try{const m=t.map(k=>ie.mutateAsync(k));await Promise.all(m),j.resetRowSelection()}catch{}},z=u.useMemo(()=>is(y,i),[y,i]),j=he({data:le,columns:z,getCoreRowModel:fe(),getSortedRowModel:je(),onSortingChange:D,enableSortingRemoval:!1,getPaginationRowModel:pe(),onPaginationChange:h,onColumnFiltersChange:f,onColumnVisibilityChange:N,getFilteredRowModel:ge(),getFacetedUniqueValues:xe(),state:{sorting:T,pagination:d,columnFilters:p,columnVisibility:x}});return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(P,{id:`${r}-input`,ref:v,className:$("peer min-w-60 max-sm:min-w-48 ps-9 h-9",!!c&&"pe-9"),value:c,onChange:s=>w(s.target.value),placeholder:"Search by label name...",type:"text","aria-label":"Search by label name"}),e.jsx("div",{className:"text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50",children:e.jsx(Ye,{size:16,"aria-hidden":"true"})}),!!c&&e.jsx("button",{className:"text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","aria-label":"Clear search",onClick:()=>{w(""),v.current&&v.current.focus()},disabled:b,children:e.jsx(Ze,{size:16,"aria-hidden":"true"})})]}),e.jsxs(se,{children:[e.jsx(ae,{asChild:!0,children:e.jsxs(g,{variant:"outline",className:"hidden sm:flex",children:[e.jsx(be,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"View"]})}),e.jsxs(te,{align:"end",children:[e.jsx(Ie,{children:"Toggle columns"}),j.getAllColumns().filter(s=>s.getCanHide()).map(s=>{const t=typeof s.columnDef.header=="string"?s.columnDef.header:s.id;return e.jsx(Me,{className:"capitalize",checked:s.getIsVisible(),onCheckedChange:m=>s.toggleVisibility(!!m),onSelect:m=>m.preventDefault(),children:t},s.id)})]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[j.getSelectedRowModel().rows.length>0&&e.jsxs(K,{children:[e.jsx(O,{asChild:!0,children:e.jsxs(g,{variant:"outline",children:[e.jsx(ne,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Delete",e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:j.getSelectedRowModel().rows.length})]})}),e.jsxs(Q,{children:[e.jsxs("div",{className:"flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4",children:[e.jsx("div",{className:"flex size-9 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(F,{className:"opacity-80",size:16})}),e.jsxs(X,{children:[e.jsx(J,{children:"Are you absolutely sure?"}),e.jsxs(W,{children:["This action cannot be undone. This will permanently delete"," ",j.getSelectedRowModel().rows.length," selected"," ",j.getSelectedRowModel().rows.length===1?"label":"labels","."]})]})]}),e.jsxs(Y,{children:[e.jsx(Z,{children:"Cancel"}),e.jsx(ee,{onClick:re,children:"Delete"})]})]})]}),e.jsx($e,{onLabelCreated:y,children:e.jsxs(g,{variant:"outline",children:[e.jsx(Fe,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Add Label"]})})]})]}),e.jsx("div",{className:"bg-background rounded-md border",children:e.jsxs(Ce,{className:"table-fixed",children:[e.jsx(Ne,{children:j.getHeaderGroups().map(s=>e.jsx(S,{className:"hover:bg-transparent",children:s.headers.map(t=>e.jsx(ve,{style:{width:`${t.getSize()}px`},className:"h-11",children:t.isPlaceholder?null:t.column.getCanSort()?e.jsxs("div",{className:$(t.column.getCanSort()&&"flex h-full cursor-pointer items-center justify-between gap-2 select-none"),onClick:t.column.getToggleSortingHandler(),onKeyDown:m=>{var k;t.column.getCanSort()&&(m.key==="Enter"||m.key===" ")&&(m.preventDefault(),(k=t.column.getToggleSortingHandler())==null||k(m))},tabIndex:t.column.getCanSort()?0:void 0,children:[I(t.column.columnDef.header,t.getContext()),{asc:e.jsx(Ee,{className:"shrink-0 opacity-60",size:16,"aria-hidden":"true"}),desc:e.jsx(Re,{className:"shrink-0 opacity-60",size:16,"aria-hidden":"true"})}[t.column.getIsSorted()]??null]}):I(t.column.columnDef.header,t.getContext())},t.id))},s.id))}),e.jsx(we,{children:b?e.jsx(S,{children:e.jsx(L,{colSpan:z.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"}),"Loading labels..."]})})}):H?e.jsx(S,{children:e.jsx(L,{colSpan:z.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-2",children:[e.jsx(F,{className:"h-8 w-8 text-red-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Failed to load labels"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:H.message||"An unknown error occurred"}),e.jsx(g,{variant:"outline",size:"sm",onClick:()=>y(),className:"mt-2",children:"Try again"})]})]})})}):(q=j.getRowModel().rows)!=null&&q.length?j.getRowModel().rows.map(s=>e.jsx(S,{"data-state":s.getIsSelected()&&"selected",children:s.getVisibleCells().map(t=>e.jsx(L,{className:"last:py-0",children:I(t.column.columnDef.cell,t.getContext())},t.id))},s.id)):e.jsx(S,{children:e.jsx(L,{colSpan:z.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-2 p-6",children:[e.jsx(ss,{className:"h-8 w-8 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"No labels found"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:c?"Try adjusting your search":"No labels have been created yet"})]})]})})})})]})}),e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3",children:[e.jsx(M,{htmlFor:r,className:"text-sm whitespace-nowrap",children:"Rows per page"}),e.jsxs(Ve,{value:d.pageSize.toString(),onValueChange:s=>{h(t=>({...t,pageSize:Number(s),pageIndex:0}))},disabled:b,children:[e.jsx(He,{id:r,className:"w-fit whitespace-nowrap min-w-[4rem]",children:e.jsx(Ue,{placeholder:"Select number of results"})}),e.jsx(qe,{className:"[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2",children:[5,10,25,50].map(s=>e.jsx(Be,{value:s.toString(),children:s},s))})]})]}),e.jsxs("div",{className:"flex items-center gap-4 sm:gap-8",children:[e.jsx("div",{className:"text-muted-foreground text-sm whitespace-nowrap",children:e.jsx("p",{className:"text-muted-foreground text-sm whitespace-nowrap","aria-live":"polite",children:b?"Loading...":e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"text-foreground",children:[d.pageIndex*d.pageSize+1,"-",Math.min((d.pageIndex+1)*d.pageSize,(n==null?void 0:n.pagination.total)||0)]})," ","of"," ",e.jsx("span",{className:"text-foreground",children:(n==null?void 0:n.pagination.total)||0})]})})}),e.jsx("div",{children:e.jsx(ye,{children:e.jsxs(Se,{className:"gap-1",children:[e.jsx(A,{children:e.jsx(g,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>h(s=>({...s,pageIndex:0})),disabled:d.pageIndex===0||b,"aria-label":"Go to first page",children:e.jsx(De,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(A,{children:e.jsx(g,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>h(s=>({...s,pageIndex:s.pageIndex-1})),disabled:d.pageIndex===0||b,"aria-label":"Go to previous page",children:e.jsx(ze,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(A,{children:e.jsx(g,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>h(s=>({...s,pageIndex:s.pageIndex+1})),disabled:b||!(n!=null&&n.pagination)||d.pageIndex>=n.pagination.totalPages-1,"aria-label":"Go to next page",children:e.jsx(Pe,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(A,{children:e.jsx(g,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>{const s=(n==null?void 0:n.pagination.totalPages)||1;h(t=>({...t,pageIndex:s-1}))},disabled:b||!(n!=null&&n.pagination)||d.pageIndex>=n.pagination.totalPages-1,"aria-label":"Go to last page",children:e.jsx(ke,{size:14,className:"sm:size-4","aria-hidden":"true"})})})]})})})]})]})]})}const ys=function(){const l=me.useNavigate(),{data:o,isPending:a}=E.useSession();return u.useEffect(()=>{if(!o&&!a){l({to:"/auth"});return}},[o,a,l]),a?e.jsx(B,{}):!o||!o.user?e.jsx(B,{}):e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"pl-2 text-xl font-bold",children:"Label Management"}),e.jsx("div",{className:"p-2",children:e.jsx(ls,{})})]})};export{ys as component};
