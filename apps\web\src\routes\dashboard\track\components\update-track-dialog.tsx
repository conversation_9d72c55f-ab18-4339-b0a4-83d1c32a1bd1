import { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { useTRPCClient } from "@/utils/trpc";
import { authClient } from "@/lib/auth-client";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { GenreSelect } from "@/components/ui/genre-select";
import { TrackFileUpload } from "@/routes/dashboard/track/components/track-file-upload";
import { ArtistSelector } from "@/routes/dashboard/track/components/artist-selector";
import { ContributorSelector } from "@/routes/dashboard/track/components/contributor-selector";
import type { TrackTableItem } from "@/routes/dashboard/track/components/track-table";

interface UpdateTrackDialogProps {
  children: React.ReactNode;
  track: TrackTableItem;
  onTrackUpdated: () => void;
}

type TrackFile = {
  fileUrl: string;
  fileKey: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  duration?: number;
};

type TrackArtist = {
  artistId: string;
  role: "PRIMARY" | "FEATURING";
};

type TrackContributor = {
  contributorId: string;
  role: string;
};

export function UpdateTrackDialog({
  children,
  track,
  onTrackUpdated,
}: UpdateTrackDialogProps) {
  const [open, setOpen] = useState(false);
  const trpcClient = useTRPCClient();
  const { data: session } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";

  const [formData, setFormData] = useState({
    title: "",
    isrc: "",
    trackVersion: "",
    recordingYear: new Date().getFullYear(),
    publishingYear: new Date().getFullYear(),
    publishingHolder: "",
    genre: "",
    subGenre: "",
    lyrics: "",
    previewStart: "",
    previewLength: "",
    metadataLanguage: "en",
    explicit: "NOT_EXPLICIT" as "EXPLICIT" | "NOT_EXPLICIT" | "CLEAN",
    audioLanguage: "en",
    rightsClaim: "NO_CLAIM" as "NO_CLAIM" | "REPORT" | "MONETIZE" | "BLOCK",
    status: "DRAFT" as "DRAFT" | "READY",
    trackFiles: [] as TrackFile[],
    artists: [] as TrackArtist[],
    contributors: [] as TrackContributor[],
  });

  // Initialize form data when dialog opens
  useEffect(() => {
    if (open && track) {
      setFormData({
        title: track.title || "",
        isrc: track.isrc || "",
        trackVersion: track.trackVersion || "",
        recordingYear: track.recordingYear,
        publishingYear: track.publishingYear,
        publishingHolder: track.publishingHolder || "",
        genre: track.genre || "",
        subGenre: track.subGenre || "",
        lyrics: track.lyrics || "",
        previewStart: track.previewStart || "",
        previewLength: track.previewLength || "",
        metadataLanguage: track.metadataLanguage || "en",
        explicit: track.explicit,
        audioLanguage: track.audioLanguage || "en",
        rightsClaim: track.rightsClaim,
        status: track.status,
        trackFiles: track.trackFiles.map((file) => ({
          fileUrl: file.fileUrl,
          fileKey: file.fileKey,
          fileName: file.fileName,
          fileSize: file.fileSize,
          mimeType: file.mimeType,
          duration: file.duration,
        })),
        artists: track.artists.map((artist) => ({
          artistId: artist.artist.id,
          role: artist.role,
        })),
        contributors: track.contributors.map((contributor) => ({
          contributorId: contributor.contributor.id,
          role: contributor.role,
        })),
      });
    }
  }, [open, track]);

  const updateTrackMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      return trpcClient.track.update.mutate({
        id: track.id,
        ...data,
      });
    },
    onSuccess: () => {
      toast.success("Track updated successfully");
      setOpen(false);
      onTrackUpdated();
    },
    onError: (error: any) => {
      console.error("Failed to update track:", error);
      toast.error(
        "Failed to update track: " + (error.message || "Unknown error")
      );
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.title.trim()) {
      toast.error("Track title is required");
      return;
    }

    if (!formData.genre) {
      toast.error("Genre is required");
      return;
    }

    if (!formData.publishingHolder.trim()) {
      toast.error("Publishing holder is required");
      return;
    }

    if (formData.trackFiles.length === 0) {
      toast.error("At least one track file is required");
      return;
    }

    if (formData.artists.length === 0) {
      toast.error("At least one artist is required");
      return;
    }

    if (formData.contributors.length === 0) {
      toast.error("At least one contributor is required");
      return;
    }

    try {
      await updateTrackMutation.mutateAsync(formData);
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from(
    { length: currentYear - 1900 + 2 },
    (_, i) => currentYear + 1 - i
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Track</DialogTitle>
          <DialogDescription>
            Update track information and metadata.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange("title", e.target.value)}
                  placeholder="Enter track title"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="isrc">ISRC</Label>
                <Input
                  id="isrc"
                  value={formData.isrc}
                  onChange={(e) => handleInputChange("isrc", e.target.value)}
                  placeholder="CC-XXX-YY-NNNNN"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="genre">Genre *</Label>
                <GenreSelect
                  value={formData.genre}
                  onValueChange={(value) => handleInputChange("genre", value)}
                  placeholder="Select genre"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="subGenre">Sub-genre</Label>
                <Input
                  id="subGenre"
                  value={formData.subGenre}
                  onChange={(e) =>
                    handleInputChange("subGenre", e.target.value)
                  }
                  placeholder="Enter sub-genre"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="trackVersion">Track Version</Label>
              <Input
                id="trackVersion"
                value={formData.trackVersion}
                onChange={(e) =>
                  handleInputChange("trackVersion", e.target.value)
                }
                placeholder="e.g., Radio Edit, Extended Mix"
              />
            </div>
          </div>

          {/* Years and Publishing */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Years & Publishing</h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="recordingYear">Recording Year *</Label>
                <Select
                  value={formData.recordingYear.toString()}
                  onValueChange={(value) =>
                    handleInputChange("recordingYear", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="publishingYear">Publishing Year *</Label>
                <Select
                  value={formData.publishingYear.toString()}
                  onValueChange={(value) =>
                    handleInputChange("publishingYear", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="publishingHolder">Publishing Holder *</Label>
              <Input
                id="publishingHolder"
                value={formData.publishingHolder}
                onChange={(e) =>
                  handleInputChange("publishingHolder", e.target.value)
                }
                placeholder="Enter publishing holder name"
              />
            </div>
          </div>

          {/* Content & Language */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Content & Language</h3>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="explicit">Explicit Content</Label>
                <Select
                  value={formData.explicit}
                  onValueChange={(value) =>
                    handleInputChange("explicit", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="NOT_EXPLICIT">Not Explicit</SelectItem>
                    <SelectItem value="EXPLICIT">Explicit</SelectItem>
                    <SelectItem value="CLEAN">Clean</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="metadataLanguage">Metadata Language</Label>
                <Select
                  value={formData.metadataLanguage}
                  onValueChange={(value) =>
                    handleInputChange("metadataLanguage", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="it">Italian</SelectItem>
                    <SelectItem value="pt">Portuguese</SelectItem>
                    <SelectItem value="ja">Japanese</SelectItem>
                    <SelectItem value="ko">Korean</SelectItem>
                    <SelectItem value="zh">Chinese</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="audioLanguage">Audio Language</Label>
                <Select
                  value={formData.audioLanguage}
                  onValueChange={(value) =>
                    handleInputChange("audioLanguage", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="it">Italian</SelectItem>
                    <SelectItem value="pt">Portuguese</SelectItem>
                    <SelectItem value="ja">Japanese</SelectItem>
                    <SelectItem value="ko">Korean</SelectItem>
                    <SelectItem value="zh">Chinese</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="rightsClaim">Rights Claim</Label>
              <Select
                value={formData.rightsClaim}
                onValueChange={(value) =>
                  handleInputChange("rightsClaim", value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="NO_CLAIM">No Claim</SelectItem>
                  <SelectItem value="REPORT">Report</SelectItem>
                  <SelectItem value="MONETIZE">Monetize</SelectItem>
                  <SelectItem value="BLOCK">Block</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="lyrics">Lyrics</Label>
              <Textarea
                id="lyrics"
                value={formData.lyrics}
                onChange={(e) => handleInputChange("lyrics", e.target.value)}
                placeholder="Enter track lyrics (optional)"
                rows={4}
              />
            </div>
          </div>

          {/* Preview Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Preview Settings</h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="previewStart">Preview Start (MM:SS)</Label>
                <Input
                  id="previewStart"
                  value={formData.previewStart}
                  onChange={(e) =>
                    handleInputChange("previewStart", e.target.value)
                  }
                  placeholder="1:30"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="previewLength">Preview Length (MM:SS)</Label>
                <Input
                  id="previewLength"
                  value={formData.previewLength}
                  onChange={(e) =>
                    handleInputChange("previewLength", e.target.value)
                  }
                  placeholder="0:30"
                />
              </div>
            </div>
          </div>

          {/* Track Files */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Track Files *</h3>
            <TrackFileUpload
              files={formData.trackFiles}
              onFilesChange={(files) => handleInputChange("trackFiles", files)}
            />
          </div>

          {/* Artists */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Artists *</h3>
            <ArtistSelector
              artists={formData.artists}
              onArtistsChange={(artists) =>
                handleInputChange("artists", artists)
              }
            />
          </div>

          {/* Contributors */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Contributors *</h3>
            <ContributorSelector
              contributors={formData.contributors}
              onContributorsChange={(contributors) =>
                handleInputChange("contributors", contributors)
              }
            />
          </div>

          {/* Status */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Status</h3>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="READY">Ready</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={updateTrackMutation.isPending}>
              {updateTrackMutation.isPending ? "Updating..." : "Update Track"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
