import{r as n,j as a,B as m}from"./main-B9Fv5CdX.js";import{e as p,a as s}from"./button-Ispz1G12.js";var b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],x=b.reduce((e,t)=>{const r=p(`Primitive.${t}`),o=n.forwardRef((i,d)=>{const{asChild:u,...f}=i,c=u?r:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),a.jsx(c,{...f,ref:d})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function y(e,t){e&&m.flushSync(()=>e.dispatchEvent(t))}function E({className:e,type:t,...r}){return a.jsx("input",{type:t,"data-slot":"input",className:s("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}var v="Label",l=n.forwardRef((e,t)=>a.jsx(x.label,{...e,ref:t,onMouseDown:r=>{var i;r.target.closest("button, input, select, textarea")||((i=e.onMouseDown)==null||i.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));l.displayName=v;var g=l;function N({className:e,...t}){return a.jsx(g,{"data-slot":"label",className:s("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}export{E as I,N as L,x as P,y as d};
