import{c as ke,r as m,j as e,L as E,t as b,h as Ke,l as _e,k as Se}from"./main-B9Fv5CdX.js";import{a as k}from"./auth-client-C8WifPV2.js";import{E as Ge,A as Fe,a as ze,b as Re,c as Ae,d as Ue,e as Ie,f as Me,g as Te,h as Pe,u as Qe,C as Xe,T as Ye,i as Je,j as T,k as We,l as X,m as Ze,n as q,P as es,o as ss,p as H,q as as,r as ts,s as ns,t as rs,v as ls,w as is,x as os,y as ds}from"./table-gaCg9Yy-.js";import{u as $e}from"./useQuery-CFAncLHa.js";import{B as C,a as G}from"./button-Ispz1G12.js";import{T as Ee,C as A}from"./checkbox-Dk7Edl6C.js";import{D as Ve,a as Be,b as Le,e as De,f as I,g as cs,h as ms,i as xs,j as hs,d as us,c as gs,k as ps,C as js}from"./dropdown-menu-DKdrXVD1.js";import{L as p,I as U}from"./label-CNQvdrLZ.js";import{P as Y,a as J,b as W}from"./popover-DzeimUGg.js";import{S as se,d as ae,e as te,f as ne,g as B,P as fs,h as Ns,i as bs}from"./select-Cv6EF9My.js";import{j as re,k as le,l as ie,m as oe,n as de,o as ce,p as me}from"./dialog-iGlJJq5Q.js";import{B as V}from"./badge-B7y-QlNI.js";import{C as qe}from"./circle-check-big-BA7hvIno.js";import{C as xe}from"./circle-x-Dhf4rEsb.js";import{U as Q}from"./user-DCDNZ7An.js";import{C as ee}from"./circle-alert-DwqW4ucM.js";import{L as ys}from"./list-filter-BY1QXxSs.js";import{F as Z}from"./filter-DkLeLFHN.js";import{C as P,d as $,a as O,b as K}from"./card-PyhbSuya.js";import{S as _}from"./skeleton-BimQrrV-.js";import{T as vs}from"./triangle-alert-BretsIXJ.js";import{U as ws}from"./users-uEpTONEC.js";import{C as Cs}from"./clock-BWWmp-9w.js";import"./scroll-area-BkIMWkxZ.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ss=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],He=ke("Shield",Ss);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ds=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]],ks=ke("UserX",Ds);function Fs({children:n,onUserCreated:a}){const[i,d]=m.useState(!1),[j,c]=m.useState(!1),[l,x]=m.useState({name:"",email:"",phone:"",password:"",role:"user"}),[h,v]=m.useState({email:"",phone:""}),[u,f]=m.useState({email:!1,phone:!1}),y=m.useCallback(async r=>{var N;if(!r||!r.includes("@")){v(g=>({...g,email:""}));return}f(g=>({...g,email:!0}));try{const g=await k.admin.listUsers({query:{searchField:"email",searchOperator:"contains",searchValue:r,limit:10}});if((N=g.data)!=null&&N.users){const S=g.data.users.find(w=>w.email===r);v(S?w=>({...w,email:"Email already exists"}):w=>({...w,email:""}))}else v(S=>({...S,email:""}))}catch(g){console.error("Error validating email:",g),v(S=>({...S,email:""}))}finally{f(g=>({...g,email:!1}))}},[]),M=m.useCallback(async r=>{var N;if(!r){v(g=>({...g,phone:""}));return}f(g=>({...g,phone:!0}));try{const g=await k.admin.listUsers({query:{limit:1e3}});if((N=g.data)!=null&&N.users){const S=g.data.users.some(w=>w.phone===r);v(S?w=>({...w,phone:"Phone number already exists"}):w=>({...w,phone:""}))}}catch(g){console.error("Error validating phone:",g),v(S=>({...S,phone:""}))}finally{f(g=>({...g,phone:!1}))}},[]);m.useEffect(()=>{const r=setTimeout(()=>{l.email&&y(l.email)},500);return()=>clearTimeout(r)},[l.email,y]),m.useEffect(()=>{const r=setTimeout(()=>{l.phone&&M(l.phone)},500);return()=>clearTimeout(r)},[l.phone,M]);const D=async r=>{if(r.preventDefault(),h.email||h.phone){b.error("Please fix validation errors before submitting");return}if(u.email||u.phone){b.error("Please wait for validation to complete");return}c(!0);try{await k.admin.createUser({name:l.name,email:l.email,password:l.password,role:l.role,data:{phone:l.phone||null}});try{await k.sendVerificationEmail({email:l.email,callbackURL:"http://localhost:3001/auth"}),b.success("User created successfully and verification email sent")}catch(N){console.error("Failed to send verification email:",N),b.success("User created successfully, but failed to send verification email")}d(!1),x({name:"",email:"",phone:"",password:"",role:"user"}),v({email:"",phone:""}),f({email:!1,phone:!1}),a()}catch(N){console.error("Failed to create user:",N),b.error("Failed to create user: "+(N.message||"Unknown error"))}finally{c(!1)}},z=(r,N)=>{x(g=>({...g,[r]:N}))};return e.jsxs(re,{open:i,onOpenChange:d,children:[e.jsx(le,{asChild:!0,children:n}),e.jsxs(ie,{className:"sm:max-w-[425px]",children:[e.jsxs(oe,{children:[e.jsx(de,{children:"Create New User"}),e.jsx(ce,{children:"Add a new user to the system. They will be able to sign in with the provided credentials."})]}),e.jsxs("form",{onSubmit:D,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"name",children:"Name"}),e.jsx(U,{id:"name",placeholder:"Enter full name",value:l.name,onChange:r=>z("name",r.target.value),required:!0,disabled:j})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"email",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(U,{id:"email",type:"email",placeholder:"Enter email address",value:l.email,onChange:r=>z("email",r.target.value),required:!0,disabled:j,className:h.email?"border-red-500":""}),u.email&&e.jsx("div",{className:"absolute right-3 top-3",children:e.jsx(E,{className:"h-4 w-4 animate-spin text-gray-400"})})]}),h.email&&e.jsx("p",{className:"text-sm text-red-500",children:h.email})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"phone",children:"Phone Number (Optional)"}),e.jsxs("div",{className:"relative",children:[e.jsx(U,{id:"phone",type:"tel",placeholder:"Enter phone number",value:l.phone,onChange:r=>z("phone",r.target.value),disabled:j,className:h.phone?"border-red-500":""}),u.phone&&e.jsx("div",{className:"absolute right-3 top-3",children:e.jsx(E,{className:"h-4 w-4 animate-spin text-gray-400"})})]}),h.phone&&e.jsx("p",{className:"text-sm text-red-500",children:h.phone})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"password",children:"Password"}),e.jsx(U,{id:"password",type:"password",placeholder:"Enter password",value:l.password,onChange:r=>z("password",r.target.value),required:!0,disabled:j})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"role",children:"Role"}),e.jsxs(se,{value:l.role,onValueChange:r=>z("role",r),disabled:j,children:[e.jsx(ae,{children:e.jsx(te,{placeholder:"Select a role"})}),e.jsxs(ne,{children:[e.jsx(B,{value:"user",children:"Artist"}),e.jsx(B,{value:"admin",children:"Admin"})]})]})]}),e.jsxs(me,{children:[e.jsx(C,{type:"button",variant:"outline",onClick:()=>d(!1),disabled:j,children:"Cancel"}),e.jsx(C,{type:"submit",disabled:j||!!h.email||!!h.phone||u.email||u.phone,children:j?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):u.email||u.phone?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-4 w-4 animate-spin"}),"Validating..."]}):"Create User"})]})]})]})]})}function zs({children:n,user:a,onUserBanned:i}){const[d,j]=m.useState(!1),[c,l]=m.useState(!1),[x,h]=m.useState({banReason:"",banDuration:"permanent",banDays:7}),v=async u=>{u.preventDefault(),l(!0);try{const f={userId:a.id};x.banReason.trim()&&(f.banReason=x.banReason.trim()),x.banDuration==="temporary"&&(f.banExpiresIn=x.banDays*24*60*60),await k.admin.banUser(f),b.success(`Successfully banned ${a.name}${x.banDuration==="temporary"?` for ${x.banDays} day${x.banDays>1?"s":""}`:" permanently"}`),j(!1),h({banReason:"",banDuration:"permanent",banDays:7}),i()}catch(f){console.error("Failed to ban user:",f),b.error("Failed to ban user: "+(f.message||"Unknown error"))}finally{l(!1)}};return e.jsxs(re,{open:d,onOpenChange:j,children:[e.jsx(le,{asChild:!0,children:n}),e.jsxs(ie,{className:"sm:max-w-[425px]",children:[e.jsxs(oe,{children:[e.jsxs(de,{children:["Ban User: ",a.name]}),e.jsxs(ce,{children:["Prevent ",a.email," from signing in. You can specify a reason and duration."]})]}),e.jsxs("form",{onSubmit:v,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"banReason",children:"Ban Reason"}),e.jsx(U,{id:"banReason",placeholder:"Enter reason for ban (optional)",value:x.banReason,onChange:u=>h(f=>({...f,banReason:u.target.value})),disabled:c})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{children:"Ban Duration"}),e.jsxs(se,{value:x.banDuration,onValueChange:u=>h(f=>({...f,banDuration:u})),disabled:c,children:[e.jsx(ae,{children:e.jsx(te,{})}),e.jsxs(ne,{children:[e.jsx(B,{value:"permanent",children:"Permanent"}),e.jsx(B,{value:"temporary",children:"Temporary"})]})]})]}),x.banDuration==="temporary"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"banDays",children:"Duration (Days)"}),e.jsx(U,{id:"banDays",type:"number",min:"1",max:"365",placeholder:"Enter number of days",value:x.banDays,onChange:u=>h(f=>({...f,banDays:parseInt(u.target.value)||1})),disabled:c})]}),e.jsxs(me,{children:[e.jsx(C,{type:"button",variant:"outline",onClick:()=>j(!1),disabled:c,children:"Cancel"}),e.jsx(C,{type:"submit",variant:"destructive",disabled:c,children:c?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-4 w-4 animate-spin"}),"Banning..."]}):"Ban User"})]})]})]})]})}function Rs({children:n,user:a}){const[i,d]=m.useState(!1);return e.jsxs(re,{open:i,onOpenChange:d,children:[e.jsx(le,{asChild:!0,children:n}),e.jsxs(ie,{className:"sm:max-w-[600px]",children:[e.jsxs(oe,{children:[e.jsx(de,{children:"User Details"}),e.jsxs(ce,{children:["Complete information about ",a.name]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Basic Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-muted-foreground",children:"Full Name"}),e.jsx("p",{className:"text-sm",children:a.name})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-muted-foreground",children:"Email"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-sm",children:a.email}),a.emailVerified?e.jsx(qe,{className:"h-4 w-4 text-green-500"}):e.jsx(xe,{className:"h-4 w-4 text-red-500"})]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-muted-foreground",children:"Phone"}),e.jsx("p",{className:"text-sm",children:a.phone||e.jsx("span",{className:"text-muted-foreground italic",children:"Not provided"})})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-muted-foreground",children:"Role"}),e.jsxs("div",{className:"flex items-center gap-2",children:[a.role==="admin"?e.jsx(He,{className:"h-4 w-4 text-blue-500"}):e.jsx(Q,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm capitalize",children:a.displayRole})]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-muted-foreground",children:"Status"}),e.jsx(V,{className:G(a.status==="Banned"&&"bg-red-500/10 text-red-700",a.status==="Active"&&"bg-green-500/10 text-green-700",a.status==="Pending"&&"bg-yellow-500/10 text-yellow-700"),children:a.status})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-muted-foreground",children:"Email Verified"}),e.jsx("p",{className:"text-sm",children:a.emailVerified?e.jsx("span",{className:"text-green-600",children:"Yes"}):e.jsx("span",{className:"text-red-600",children:"No"})})]})]})]}),a.banned&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Ban Information"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 space-y-3",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-red-800",children:"Ban Reason"}),e.jsx("p",{className:"text-sm text-red-700",children:a.banReason||e.jsx("span",{className:"italic",children:"No reason provided"})})]}),a.banExpires&&e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-red-800",children:"Ban Expires"}),e.jsx("p",{className:"text-sm text-red-700",children:new Date(a.banExpires).toLocaleString()})]}),!a.banExpires&&e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-red-800",children:"Ban Type"}),e.jsx("p",{className:"text-sm text-red-700",children:"Permanent"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Account Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-muted-foreground",children:"Created At"}),e.jsx("p",{className:"text-sm",children:new Date(a.createdAt).toLocaleString()})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx(p,{className:"text-sm font-medium text-muted-foreground",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:new Date(a.updatedAt).toLocaleString()})]})]})]})]}),e.jsx(me,{children:e.jsx(C,{variant:"outline",onClick:()=>d(!1),children:"Close"})})]})]})}function As({row:n,onUserDeleted:a}){const i=async()=>{try{await k.admin.removeUser({userId:n.original.id}),b.success(`Successfully deleted user ${n.original.name}`),a()}catch(l){console.error("Failed to delete user:",l),b.error("Failed to delete user: "+(l.message||"Unknown error"))}},d=async()=>{try{await k.admin.revokeUserSessions({userId:n.original.id}),b.success(`Successfully revoked all sessions for ${n.original.name}`)}catch(l){console.error("Failed to revoke sessions:",l),b.error("Failed to revoke sessions: "+(l.message||"Unknown error"))}},j=async()=>{try{await k.admin.unbanUser({userId:n.original.id}),b.success(`Successfully unbanned ${n.original.name}`),a()}catch(l){console.error("Failed to unban user:",l),b.error("Failed to unban user: "+(l.message||"Unknown error"))}},c=async()=>{const x=(n.original.role||"user")==="admin"?"user":"admin",h=x==="user"?"Artist":"Admin";try{await k.admin.setRole({userId:n.original.id,role:x}),b.success(`Successfully changed ${n.original.name}'s role to ${h}`),a()}catch(v){console.error("Failed to change user role:",v),b.error("Failed to change user role: "+(v.message||"Unknown error"))}};return e.jsxs(Ve,{children:[e.jsx(Be,{asChild:!0,children:e.jsx("div",{className:"flex justify-end",children:e.jsx(C,{size:"icon",variant:"ghost",className:"shadow-none","aria-label":"Edit item",children:e.jsx(Ge,{size:16,"aria-hidden":"true"})})})}),e.jsxs(Le,{align:"end",children:[e.jsxs(De,{children:[e.jsx(Rs,{user:n.original,children:e.jsx(I,{onSelect:l=>l.preventDefault(),children:e.jsx("span",{children:"View Details"})})}),n.original.banned?e.jsx(I,{onClick:j,children:e.jsx("span",{children:"Unban User"})}):e.jsx(zs,{user:n.original,onUserBanned:a,children:e.jsx(I,{onSelect:l=>l.preventDefault(),children:e.jsx("span",{children:"Ban User"})})})]}),e.jsx(De,{children:e.jsxs(cs,{children:[e.jsx(ms,{children:"More"}),e.jsx(xs,{children:e.jsxs(hs,{children:[e.jsx(I,{onClick:c,children:n.original.role==="admin"?"Change to Artist":"Change to Admin"}),e.jsx(I,{onClick:d,children:"Revoke All Sessions"})]})})]})}),e.jsx(us,{className:"h-[2px]"}),e.jsxs(Fe,{children:[e.jsx(ze,{asChild:!0,children:e.jsx(I,{className:"text-destructive focus:text-destructive",onSelect:l=>l.preventDefault(),children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ee,{size:16,className:"text-destructive focus:text-destructive"}),e.jsx("span",{children:"Delete"})]})})}),e.jsxs(Re,{children:[e.jsxs("div",{className:"flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4",children:[e.jsx("div",{className:"flex size-9 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(ee,{className:"opacity-80",size:16})}),e.jsxs(Ae,{children:[e.jsx(Ue,{children:"Delete User"}),e.jsxs(Ie,{children:["Are you sure you want to delete"," ",e.jsx("strong",{children:n.original.name}),"? This action cannot be undone and will permanently remove the user and all associated data."]})]})]}),e.jsxs(Me,{children:[e.jsx(Te,{children:"Cancel"}),e.jsx(Pe,{onClick:i,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete User"})]})]})]})]})]})}const Us=(n,a,i)=>{const d=`${n.original.name} ${n.original.email}`.toLowerCase(),j=(i??"").toLowerCase();return d.includes(j)},Is=(n,a,i)=>{if(!(i!=null&&i.length))return!0;const d=n.getValue(a);return i.includes(d)},Ms=(n,a,i)=>{if(!(i!=null&&i.length))return!0;const d=n.getValue(a);return i.includes(d)},Ts=n=>[{id:"select",header:({table:a})=>e.jsx(A,{checked:a.getIsAllPageRowsSelected()||a.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:i=>a.toggleAllPageRowsSelected(!!i),"aria-label":"Select all"}),cell:({row:a})=>e.jsx(A,{checked:a.getIsSelected(),onCheckedChange:i=>a.toggleSelected(!!i),"aria-label":"Select row"}),size:28,enableSorting:!1,enableHiding:!1},{header:"Name",accessorKey:"name",cell:({row:a})=>e.jsx("div",{className:"font-medium",children:a.getValue("name")}),size:180,filterFn:Us,enableHiding:!1},{header:"Email",accessorKey:"email",cell:({row:a})=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:a.getValue("email")}),a.original.emailVerified?e.jsx(qe,{className:"h-4 w-4 text-green-500"}):e.jsx(xe,{className:"h-4 w-4 text-red-500"})]}),size:250},{header:"Phone",accessorKey:"phone",cell:({row:a})=>e.jsx("div",{className:"text-sm",children:a.getValue("phone")||e.jsx("span",{className:"text-muted-foreground",children:"Not provided"})}),size:150},{header:"Role",accessorKey:"displayRole",cell:({row:a})=>e.jsxs("div",{className:"flex items-center gap-2",children:[a.original.role==="admin"?e.jsx(He,{className:"h-4 w-4 text-blue-500"}):e.jsx(Q,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"capitalize",children:a.getValue("displayRole")})]}),size:120,filterFn:Ms},{header:"Status",accessorKey:"status",cell:({row:a})=>e.jsx(V,{className:G(a.getValue("status")==="Banned"&&"bg-red-500/10 text-red-700",a.getValue("status")==="Active"&&"bg-green-500/10 text-green-700",a.getValue("status")==="Pending"&&"bg-yellow-500/10 text-yellow-700"),children:a.getValue("status")}),size:100,filterFn:Is},{header:"Created",accessorKey:"createdAt",cell:({row:a})=>{const i=new Date(a.getValue("createdAt"));return e.jsx("div",{className:"text-sm text-muted-foreground",children:i.toLocaleDateString()})},size:120},{id:"actions",header:()=>e.jsx("span",{className:"sr-only",children:"Actions"}),cell:({row:a})=>e.jsx(As,{row:a,onUserDeleted:n}),size:60,enableHiding:!1}],Ps=n=>{let a="Active";n.banned?a="Banned":n.emailVerified||(a="Pending");const i=n.role==="user"?"Artist":n.role==="admin"?"Admin":"Artist";return{...n,status:a,displayRole:i}};function $s(){var Ne,be,ye,ve,we,Ce;const n=m.useId(),[a,i]=m.useState([]),[d,j]=m.useState({}),[c,l]=m.useState({pageIndex:0,pageSize:10}),x=m.useRef(null),[h,v]=m.useState([{id:"name",desc:!1}]),[u,f]=m.useState(""),[y,M]=m.useState([]),[D,z]=m.useState([]),{data:r,isLoading:N,error:g,refetch:S}=$e({queryKey:["admin-users",c.pageIndex,c.pageSize,u,y,D,h],queryFn:async()=>{try{const s={limit:c.pageSize,offset:c.pageIndex*c.pageSize};if(u&&(u.includes("@")?(s.searchField="email",s.searchOperator="contains",s.searchValue=u):(s.searchField="name",s.searchOperator="contains",s.searchValue=u)),h.length>0&&(s.sortBy=h[0].id,s.sortDirection=h[0].desc?"desc":"asc"),y.length>0){const o=y[0]==="Artist"?"user":y[0]==="Admin"?"admin":y[0];s.filterField="role",s.filterOperator="eq",s.filterValue=o}return await k.admin.listUsers({query:s})}catch(s){throw console.error("Failed to fetch users:",s),b.error("Failed to fetch users: "+(s.message||"Unknown error")),s}},staleTime:3e4,retry:2}),w=m.useMemo(()=>{var t;if(!((t=r==null?void 0:r.data)!=null&&t.users))return[];let s=r.data.users.map(Ps);return D.length>0&&(s=s.filter(o=>D.includes(o.status))),y.length>1&&(s=s.filter(o=>y.includes(o.displayRole))),s},[(Ne=r==null?void 0:r.data)==null?void 0:Ne.users,y,D]),Oe=async()=>{const t=F.getSelectedRowModel().rows.map(o=>o.original.id);try{const o=t.map(R=>k.admin.removeUser({userId:R}));await Promise.all(o),b.success(`Successfully deleted ${t.length} user${t.length>1?"s":""}`),F.resetRowSelection(),S()}catch(o){console.error("Failed to delete users:",o),b.error("Failed to delete users: "+(o.message||"Unknown error"))}},L=m.useMemo(()=>Ts(S),[S]),F=Qe({data:w,columns:L,getCoreRowModel:ds(),getSortedRowModel:os(),onSortingChange:v,enableSortingRemoval:!1,getPaginationRowModel:is(),onPaginationChange:l,onColumnFiltersChange:i,onColumnVisibilityChange:j,getFilteredRowModel:ls(),getFacetedUniqueValues:rs(),meta:{refetch:S},state:{sorting:h,pagination:c,columnFilters:a,columnVisibility:d}}),he=m.useMemo(()=>["Active","Banned","Pending"],[]),ue=m.useMemo(()=>["Admin","Artist"],[]),ge=m.useMemo(()=>{const s=new Map;return w.forEach(t=>{const o=s.get(t.status)||0;s.set(t.status,o+1)}),s},[w]),pe=m.useMemo(()=>{const s=new Map;return w.forEach(t=>{const o=s.get(t.displayRole)||0;s.set(t.displayRole,o+1)}),s},[w]),je=(s,t)=>{z(s?o=>[...o,t]:o=>o.filter(R=>R!==t))},fe=(s,t)=>{M(s?o=>[...o,t]:o=>o.filter(R=>R!==t))};return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(U,{id:`${n}-input`,ref:x,className:G("peer min-w-60 max-sm:min-w-48 ps-9 h-9",!!u&&"pe-9"),value:u,onChange:s=>f(s.target.value),placeholder:"Search by name or email...",type:"text","aria-label":"Search by name or email"}),e.jsx("div",{className:"text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50",children:e.jsx(ys,{size:16,"aria-hidden":"true"})}),!!u&&e.jsx("button",{className:"text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","aria-label":"Clear search",onClick:()=>{f(""),x.current&&x.current.focus()},disabled:N,children:e.jsx(xe,{size:16,"aria-hidden":"true"})})]}),e.jsxs(Y,{children:[e.jsx(J,{asChild:!0,children:e.jsxs(C,{variant:"outline",className:"sm:hidden",children:[e.jsx(Z,{className:"opacity-60",size:16,"aria-hidden":"true"}),(D.length>0||y.length>0)&&e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:D.length+y.length})]})}),e.jsx(W,{className:"w-80 p-4",align:"start",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-muted-foreground text-sm font-medium",children:"Status"}),e.jsx("div",{className:"space-y-2",children:he.map((s,t)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{id:`${n}-mobile-status-${t}`,checked:D.includes(s),onCheckedChange:o=>je(o,s)}),e.jsxs(p,{htmlFor:`${n}-mobile-status-${t}`,className:"flex grow justify-between gap-2 font-normal",children:[s," ",e.jsx("span",{className:"text-muted-foreground ms-2 text-xs",children:ge.get(s)||0})]})]},s))})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-muted-foreground text-sm font-medium",children:"Role"}),e.jsx("div",{className:"space-y-2",children:ue.map((s,t)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{id:`${n}-mobile-role-${t}`,checked:y.includes(s),onCheckedChange:o=>fe(o,s)}),e.jsxs(p,{htmlFor:`${n}-mobile-role-${t}`,className:"flex grow justify-between gap-2 font-normal",children:[e.jsx("span",{className:"capitalize",children:s}),e.jsx("span",{className:"text-muted-foreground ms-2 text-xs",children:pe.get(s)||0})]})]},s))})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-muted-foreground text-sm font-medium",children:"Columns"}),e.jsx("div",{className:"space-y-2",children:F.getAllColumns().filter(s=>s.getCanHide()).map(s=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{id:`${n}-mobile-column-${s.id}`,checked:s.getIsVisible(),onCheckedChange:t=>s.toggleVisibility(!!t)}),e.jsx(p,{htmlFor:`${n}-mobile-column-${s.id}`,className:"flex grow justify-between gap-2 font-normal capitalize",children:s.id})]},s.id))})]})]})})]}),e.jsxs("div",{className:"hidden sm:flex items-center gap-3",children:[e.jsxs(Y,{children:[e.jsx(J,{asChild:!0,children:e.jsxs(C,{variant:"outline",children:[e.jsx(Z,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Status",D.length>0&&e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:D.length})]})}),e.jsx(W,{className:"w-auto min-w-36 p-3",align:"start",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-muted-foreground text-xs font-medium",children:"Filters"}),e.jsx("div",{className:"space-y-3",children:he.map((s,t)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{id:`${n}-status-${t}`,checked:D.includes(s),onCheckedChange:o=>je(o,s)}),e.jsxs(p,{htmlFor:`${n}-status-${t}`,className:"flex grow justify-between gap-2 font-normal",children:[s," ",e.jsx("span",{className:"text-muted-foreground ms-2 text-xs",children:ge.get(s)||0})]})]},s))})]})})]}),e.jsxs(Y,{children:[e.jsx(J,{asChild:!0,children:e.jsxs(C,{variant:"outline",children:[e.jsx(Z,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Role",y.length>0&&e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:y.length})]})}),e.jsx(W,{className:"w-auto min-w-36 p-3",align:"start",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-muted-foreground text-xs font-medium",children:"Role Filters"}),e.jsx("div",{className:"space-y-3",children:ue.map((s,t)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{id:`${n}-role-${t}`,checked:y.includes(s),onCheckedChange:o=>fe(o,s)}),e.jsxs(p,{htmlFor:`${n}-role-${t}`,className:"flex grow justify-between gap-2 font-normal",children:[e.jsx("span",{className:"capitalize",children:s}),e.jsx("span",{className:"text-muted-foreground ms-2 text-xs",children:pe.get(s)||0})]})]},s))})]})})]})]}),e.jsxs(Ve,{children:[e.jsx(Be,{asChild:!0,children:e.jsxs(C,{variant:"outline",className:"hidden sm:flex",children:[e.jsx(Xe,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"View"]})}),e.jsxs(Le,{align:"end",children:[e.jsx(gs,{children:"Toggle columns"}),F.getAllColumns().filter(s=>s.getCanHide()).map(s=>e.jsx(ps,{className:"capitalize",checked:s.getIsVisible(),onCheckedChange:t=>s.toggleVisibility(!!t),onSelect:t=>t.preventDefault(),children:s.id},s.id))]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[F.getSelectedRowModel().rows.length>0&&e.jsxs(Fe,{children:[e.jsx(ze,{asChild:!0,children:e.jsxs(C,{variant:"outline",children:[e.jsx(Ee,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Delete",e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:F.getSelectedRowModel().rows.length})]})}),e.jsxs(Re,{children:[e.jsxs("div",{className:"flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4",children:[e.jsx("div",{className:"flex size-9 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(ee,{className:"opacity-80",size:16})}),e.jsxs(Ae,{children:[e.jsx(Ue,{children:"Are you absolutely sure?"}),e.jsxs(Ie,{children:["This action cannot be undone. This will permanently delete"," ",F.getSelectedRowModel().rows.length," selected"," ",F.getSelectedRowModel().rows.length===1?"row":"rows","."]})]})]}),e.jsxs(Me,{children:[e.jsx(Te,{children:"Cancel"}),e.jsx(Pe,{onClick:Oe,children:"Delete"})]})]})]}),e.jsx(Fs,{onUserCreated:S,children:e.jsxs(C,{variant:"outline",children:[e.jsx(fs,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Add User"]})})]})]}),e.jsx("div",{className:"bg-background rounded-md border",children:e.jsxs(Ye,{className:"table-fixed",children:[e.jsx(Je,{children:F.getHeaderGroups().map(s=>e.jsx(T,{className:"hover:bg-transparent",children:s.headers.map(t=>e.jsx(We,{style:{width:`${t.getSize()}px`},className:"h-11",children:t.isPlaceholder?null:t.column.getCanSort()?e.jsxs("div",{className:G(t.column.getCanSort()&&"flex h-full cursor-pointer items-center justify-between gap-2 select-none"),onClick:t.column.getToggleSortingHandler(),onKeyDown:o=>{var R;t.column.getCanSort()&&(o.key==="Enter"||o.key===" ")&&(o.preventDefault(),(R=t.column.getToggleSortingHandler())==null||R(o))},tabIndex:t.column.getCanSort()?0:void 0,children:[X(t.column.columnDef.header,t.getContext()),{asc:e.jsx(bs,{className:"shrink-0 opacity-60",size:16,"aria-hidden":"true"}),desc:e.jsx(Ns,{className:"shrink-0 opacity-60",size:16,"aria-hidden":"true"})}[t.column.getIsSorted()]??null]}):X(t.column.columnDef.header,t.getContext())},t.id))},s.id))}),e.jsx(Ze,{children:N?e.jsx(T,{children:e.jsx(q,{colSpan:L.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"}),"Loading users..."]})})}):g?e.jsx(T,{children:e.jsx(q,{colSpan:L.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-2",children:[e.jsx(ee,{className:"h-8 w-8 text-red-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Failed to load users"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:g.message||"An unknown error occurred"}),e.jsx(C,{variant:"outline",size:"sm",onClick:()=>S(),className:"mt-2",children:"Try again"})]})]})})}):(be=F.getRowModel().rows)!=null&&be.length?F.getRowModel().rows.map(s=>e.jsx(T,{"data-state":s.getIsSelected()&&"selected",children:s.getVisibleCells().map(t=>e.jsx(q,{className:"last:py-0",children:X(t.column.columnDef.cell,t.getContext())},t.id))},s.id)):e.jsx(T,{children:e.jsx(q,{colSpan:L.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-2 p-6",children:[e.jsx(Q,{className:"h-8 w-8 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"No users found"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:u||y.length>0||D.length>0?"Try adjusting your search or filters":"No users have been created yet"})]})]})})})})]})}),e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3",children:[e.jsx(p,{htmlFor:n,className:"text-sm whitespace-nowrap",children:"Rows per page"}),e.jsxs(se,{value:c.pageSize.toString(),onValueChange:s=>{l(t=>({...t,pageSize:Number(s),pageIndex:0}))},disabled:N,children:[e.jsx(ae,{id:n,className:"w-fit whitespace-nowrap min-w-[4rem]",children:e.jsx(te,{placeholder:"Select number of results"})}),e.jsx(ne,{className:"[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2",children:[5,10,25,50].map(s=>e.jsx(B,{value:s.toString(),children:s},s))})]})]}),e.jsxs("div",{className:"flex items-center gap-4 sm:gap-8",children:[e.jsx("div",{className:"text-muted-foreground text-sm whitespace-nowrap",children:e.jsx("p",{className:"text-muted-foreground text-sm whitespace-nowrap","aria-live":"polite",children:N?"Loading...":e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"text-foreground",children:[c.pageIndex*c.pageSize+1,"-",Math.min((c.pageIndex+1)*c.pageSize,((ye=r==null?void 0:r.data)==null?void 0:ye.total)||0)]})," ","of"," ",e.jsx("span",{className:"text-foreground",children:((ve=r==null?void 0:r.data)==null?void 0:ve.total)||0})]})})}),e.jsx("div",{children:e.jsx(es,{children:e.jsxs(ss,{className:"gap-1",children:[e.jsx(H,{children:e.jsx(C,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>l(s=>({...s,pageIndex:0})),disabled:c.pageIndex===0||N,"aria-label":"Go to first page",children:e.jsx(as,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(H,{children:e.jsx(C,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>l(s=>({...s,pageIndex:s.pageIndex-1})),disabled:c.pageIndex===0||N,"aria-label":"Go to previous page",children:e.jsx(ts,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(H,{children:e.jsx(C,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>l(s=>({...s,pageIndex:s.pageIndex+1})),disabled:N||!((we=r==null?void 0:r.data)!=null&&we.total)||(c.pageIndex+1)*c.pageSize>=r.data.total,"aria-label":"Go to next page",children:e.jsx(js,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(H,{children:e.jsx(C,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>{var t;const s=Math.ceil((((t=r==null?void 0:r.data)==null?void 0:t.total)||0)/c.pageSize);l(o=>({...o,pageIndex:s-1}))},disabled:N||!((Ce=r==null?void 0:r.data)!=null&&Ce.total)||(c.pageIndex+1)*c.pageSize>=r.data.total,"aria-label":"Go to last page",children:e.jsx(ns,{size:14,className:"sm:size-4","aria-hidden":"true"})})})]})})})]})]})]})}function Es(){var j;const{data:n,isLoading:a,error:i}=$e({queryKey:["user-summary-stats"],queryFn:async()=>{try{return await k.admin.listUsers({query:{limit:1e3}})}catch(c){throw console.error("Failed to fetch user summary stats:",c),b.error("Failed to fetch user statistics: "+(c.message||"Unknown error")),c}},staleTime:3e4,retry:2}),d=Ke.useMemo(()=>{var l;return(l=n==null?void 0:n.data)!=null&&l.users?n.data.users.reduce((x,h)=>(x.total++,h.banned?x.banned++:h.emailVerified||x.pending++,h.role!=="admin"&&x.artists++,x),{total:0,banned:0,pending:0,artists:0}):{total:0,banned:0,pending:0,artists:0}},[(j=n==null?void 0:n.data)==null?void 0:j.users]);return i?e.jsx("div",{className:"grid gap-4 grid-cols-2 lg:grid-cols-4",children:e.jsx(P,{className:"border-red-200",children:e.jsx($,{className:"flex items-center justify-center p-6",children:e.jsxs("div",{className:"flex flex-col items-center gap-2",children:[e.jsx(vs,{className:"h-8 w-8 text-red-500"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Failed to load statistics"})]})})})}):e.jsxs("div",{className:"grid gap-4 grid-cols-2 lg:grid-cols-4",children:[e.jsxs(P,{children:[e.jsxs(O,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(K,{className:"text-sm font-medium",children:"Total Users"}),e.jsx(ws,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs($,{children:[e.jsx("div",{className:"text-2xl font-bold",children:a?e.jsx(_,{className:"h-8 w-16"}):d.total}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"All registered users"})]})]}),e.jsxs(P,{children:[e.jsxs(O,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(K,{className:"text-sm font-medium",children:"Artists"}),e.jsx(Q,{className:"h-4 w-4 text-purple-600"})]}),e.jsxs($,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:a?e.jsx(_,{className:"h-8 w-16"}):d.artists}),!a&&d.total>0&&e.jsxs(V,{variant:"secondary",className:"text-xs",children:[Math.round(d.artists/d.total*100),"%"]})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Artist role users"})]})]}),e.jsxs(P,{children:[e.jsxs(O,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(K,{className:"text-sm font-medium",children:"Pending Users"}),e.jsx(Cs,{className:"h-4 w-4 text-yellow-600"})]}),e.jsxs($,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:a?e.jsx(_,{className:"h-8 w-16"}):d.pending}),!a&&d.total>0&&e.jsxs(V,{variant:"secondary",className:"text-xs",children:[Math.round(d.pending/d.total*100),"%"]})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Awaiting email verification"})]})]}),e.jsxs(P,{children:[e.jsxs(O,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(K,{className:"text-sm font-medium",children:"Banned Users"}),e.jsx(ks,{className:"h-4 w-4 text-red-600"})]}),e.jsxs($,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:a?e.jsx(_,{className:"h-8 w-16"}):d.banned}),!a&&d.total>0&&e.jsxs(V,{variant:"secondary",className:"text-xs",children:[Math.round(d.banned/d.total*100),"%"]})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Suspended accounts"})]})]})]})}const da=function(){const a=_e.useNavigate(),{data:i,isPending:d}=k.useSession();return m.useEffect(()=>{if(!i&&!d){a({to:"/auth"});return}if(i&&i.user&&i.user.role!=="admin"){b.error("Access denied. Admin role required."),a({to:"/dashboard"});return}},[i,d,a]),d?e.jsx(Se,{}):!i||!i.user||i.user.role!=="admin"?e.jsx(Se,{}):e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"pl-2 text-xl font-bold",children:"User Management"}),e.jsx("div",{className:"p-2",children:e.jsx(Es,{})}),e.jsx("div",{className:"p-2",children:e.jsx($s,{})})]})};export{da as component};
