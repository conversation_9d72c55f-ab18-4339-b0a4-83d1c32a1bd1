import { type ColumnDef } from "@tanstack/react-table";
import { MoreH<PERSON>zon<PERSON>, Edit, Trash2, Eye, Play, Download } from "lucide-react";
import { Link } from "@tanstack/react-router";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useTRPCClient } from "@/utils/trpc";
import { UpdateTrackDialog } from "@/routes/dashboard/track/components/update-track-dialog";
import { TrackStatusDialog } from "@/routes/dashboard/track/components/track-status-dialog";
import type { TrackTableItem } from "@/routes/dashboard/track/components/track-table";

export const createColumns = (
  onTrackDeleted: () => void,
  isAdmin: boolean
): ColumnDef<TrackTableItem>[] => {
  const baseColumns: ColumnDef<TrackTableItem>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "title",
      header: "Title",
      cell: ({ row }) => {
        const track = row.original;
        return (
          <div className="flex flex-col">
            <Link
              to="/dashboard/track/$id"
              params={{ id: track.id }}
              className="font-medium hover:underline"
            >
              {track.title}
            </Link>
            {track.isrc && (
              <span className="text-xs text-muted-foreground">
                ISRC: {track.isrc}
              </span>
            )}
          </div>
        );
      },
      size: 200,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <Badge
            variant={status === "READY" ? "default" : "secondary"}
            className="capitalize"
          >
            {status.toLowerCase()}
          </Badge>
        );
      },
      size: 80,
    },
    {
      accessorKey: "genre",
      header: "Genre",
      cell: ({ row }) => {
        const track = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">{track.genre}</span>
            {track.subGenre && (
              <span className="text-xs text-muted-foreground">
                {track.subGenre}
              </span>
            )}
          </div>
        );
      },
      size: 120,
    },
    {
      accessorKey: "artists",
      header: "Artists",
      cell: ({ row }) => {
        const artists = row.original.artists;
        if (!artists.length) return <span className="text-muted-foreground">-</span>;
        
        const primaryArtists = artists.filter(a => a.role === "PRIMARY");
        const featuringArtists = artists.filter(a => a.role === "FEATURING");
        
        return (
          <div className="flex flex-col space-y-1">
            {primaryArtists.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {primaryArtists.map((artist, index) => (
                  <span key={artist.artist.id} className="text-sm">
                    {artist.artist.name}
                    {index < primaryArtists.length - 1 && ", "}
                  </span>
                ))}
              </div>
            )}
            {featuringArtists.length > 0 && (
              <div className="flex flex-wrap gap-1">
                <span className="text-xs text-muted-foreground">feat. </span>
                {featuringArtists.map((artist, index) => (
                  <span key={artist.artist.id} className="text-xs text-muted-foreground">
                    {artist.artist.name}
                    {index < featuringArtists.length - 1 && ", "}
                  </span>
                ))}
              </div>
            )}
          </div>
        );
      },
      size: 180,
    },
    {
      accessorKey: "contributors",
      header: "Contributors",
      cell: ({ row }) => {
        const contributors = row.original.contributors;
        if (!contributors.length) return <span className="text-muted-foreground">-</span>;
        
        return (
          <div className="flex flex-col space-y-1">
            {contributors.slice(0, 2).map((contributor) => (
              <div key={contributor.contributor.id} className="text-sm">
                <span className="font-medium">{contributor.contributor.name}</span>
                <span className="text-xs text-muted-foreground ml-1">
                  ({contributor.role})
                </span>
              </div>
            ))}
            {contributors.length > 2 && (
              <span className="text-xs text-muted-foreground">
                +{contributors.length - 2} more
              </span>
            )}
          </div>
        );
      },
      size: 160,
    },
    {
      accessorKey: "recordingYear",
      header: "Recording",
      cell: ({ row }) => {
        const track = row.original;
        return (
          <div className="flex flex-col text-sm">
            <span>Rec: {track.recordingYear}</span>
            <span className="text-muted-foreground">Pub: {track.publishingYear}</span>
          </div>
        );
      },
      size: 90,
    },
    {
      accessorKey: "trackFiles",
      header: "Files",
      cell: ({ row }) => {
        const files = row.original.trackFiles;
        if (!files.length) return <span className="text-muted-foreground">No files</span>;
        
        const totalSize = files.reduce((acc, file) => acc + (file.fileSize || 0), 0);
        const formatSize = (bytes: number) => {
          if (bytes === 0) return "0 B";
          const k = 1024;
          const sizes = ["B", "KB", "MB", "GB"];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
        };
        
        return (
          <div className="flex flex-col text-sm">
            <span>{files.length} file{files.length > 1 ? "s" : ""}</span>
            {totalSize > 0 && (
              <span className="text-xs text-muted-foreground">
                {formatSize(totalSize)}
              </span>
            )}
          </div>
        );
      },
      size: 80,
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = row.getValue("createdAt") as Date;
        return (
          <div className="flex flex-col text-sm">
            <span>{date.toLocaleDateString()}</span>
            <span className="text-xs text-muted-foreground">
              {date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
            </span>
          </div>
        );
      },
      size: 100,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const track = row.original;
        const trpcClient = useTRPCClient();

        const deleteTrackMutation = useMutation({
          mutationFn: async () => {
            return trpcClient.track.delete.mutate({ id: track.id });
          },
          onSuccess: () => {
            toast.success("Track deleted successfully");
            onTrackDeleted();
          },
          onError: (error: any) => {
            toast.error(`Failed to delete track: ${error.message}`);
          },
        });

        return (
          <div className="flex items-center space-x-2">
            {/* Quick status toggle */}
            <TrackStatusDialog track={track} onStatusUpdated={onTrackDeleted}>
              <Button variant="ghost" size="sm">
                <Badge
                  variant={track.status === "READY" ? "default" : "secondary"}
                  className="cursor-pointer"
                >
                  {track.status}
                </Badge>
              </Button>
            </TrackStatusDialog>

            {/* Actions dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link to="/dashboard/track/$id" params={{ id: track.id }}>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </Link>
                </DropdownMenuItem>
                <UpdateTrackDialog track={track} onTrackUpdated={onTrackDeleted}>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Track
                  </DropdownMenuItem>
                </UpdateTrackDialog>
                <DropdownMenuSeparator />
                {track.trackFiles.length > 0 && (
                  <DropdownMenuItem
                    onClick={() => {
                      // Open first track file for preview/download
                      window.open(track.trackFiles[0].fileUrl, "_blank");
                    }}
                  >
                    <Play className="mr-2 h-4 w-4" />
                    Preview Audio
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem
                      onSelect={(e) => e.preventDefault()}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Track
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will permanently delete "{track.title}" and all its
                        associated files. This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => deleteTrackMutation.mutate()}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
      size: 120,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  return baseColumns;
};
