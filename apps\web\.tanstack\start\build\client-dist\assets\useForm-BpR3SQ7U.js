import{p as _,S as ce,D as J,w as de,r as P,j}from"./main-CiHxKC0e.js";function H(r,a){return typeof r=="function"?r(a):r}function C(r,a){return ee(a).reduce((e,t)=>{if(e===null)return null;if(typeof e<"u")return e[t]},r)}function $(r,a,i){const e=ee(a);function t(s){if(!e.length)return H(i,s);const n=e.shift();if(typeof n=="string"||typeof n=="number"&&!Array.isArray(s))return typeof s=="object"?(s===null&&(s={}),{...s,[n]:t(s[n])}):{[n]:t()};if(Array.isArray(s)&&typeof n=="number"){const o=s.slice(0,n);return[...o.length?o:new Array(n),t(s[n]),...s.slice(n+1)]}return[...new Array(n),t()]}return t(r)}function he(r,a){const i=ee(a);function e(t){if(!t)return;if(i.length===1){const n=i[0];if(Array.isArray(t)&&typeof n=="number")return t.filter((h,c)=>c!==n);const{[n]:o,...d}=t;return d}const s=i.shift();if(typeof s=="string"&&typeof t=="object")return{...t,[s]:e(t[s])};if(typeof s=="number"&&Array.isArray(t)){if(s>=t.length)return t;const n=t.slice(0,s);return[...n.length?n:new Array(s),e(t[s]),...t.slice(s+1)]}throw new Error("It seems we have created an infinite loop in deleteBy. ")}return e(r)}const fe=/^(\d+)$/gm,ve=/\.(\d+)(?=\.)/gm,me=/^(\d+)\./gm,Me=/\.(\d+$)/gm,pe=/\.{2,}/gm,Q="__int__",B=`${Q}$1`;function ee(r){if(Array.isArray(r))return[...r];if(typeof r!="string")throw new Error("Path must be a string.");return r.replace(/(^\[)|]/gm,"").replace(/\[/g,".").replace(fe,B).replace(ve,`.${B}.`).replace(me,`${B}.`).replace(Me,`.${B}`).replace(pe,".").split(".").map(a=>a.indexOf(Q)===0?parseInt(a.substring(Q.length),10):a)}function ge(r){return!(Array.isArray(r)&&r.length===0)}function X(r,a){const{asyncDebounceMs:i}=a,{onChangeAsync:e,onBlurAsync:t,onSubmitAsync:s,onBlurAsyncDebounceMs:n,onChangeAsyncDebounceMs:o}=a.validators||{},d=i??0,h={cause:"change",validate:e,debounceMs:o??d},c={cause:"blur",validate:t,debounceMs:n??d},u={cause:"submit",validate:s,debounceMs:0},l=v=>({...v,debounceMs:0});switch(r){case"submit":return[l(h),l(c),u];case"blur":return[c];case"change":return[h];case"server":default:return[]}}function Y(r,a){const{onChange:i,onBlur:e,onSubmit:t,onMount:s}=a.validators||{},n={cause:"change",validate:i},o={cause:"blur",validate:e},d={cause:"submit",validate:t},h={cause:"mount",validate:s},c={cause:"server",validate:()=>{}};switch(r){case"mount":return[h];case"submit":return[n,o,d,c];case"server":return[c];case"blur":return[o,c];case"change":default:return[n,c]}}const Z=r=>!!r&&typeof r=="object"&&"fields"in r;function I(r,a){if(Object.is(r,a))return!0;if(typeof r!="object"||r===null||typeof a!="object"||a===null)return!1;if(r instanceof Map&&a instanceof Map){if(r.size!==a.size)return!1;for(const[t,s]of r)if(!a.has(t)||!Object.is(s,a.get(t)))return!1;return!0}if(r instanceof Set&&a instanceof Set){if(r.size!==a.size)return!1;for(const t of r)if(!a.has(t))return!1;return!0}const i=Object.keys(r),e=Object.keys(a);if(i.length!==e.length)return!1;for(const t of i)if(!e.includes(t)||!I(r[t],a[t]))return!1;return!0}const ie=({newFormValidatorError:r,isPreviousErrorFromFormValidator:a,previousErrorValue:i})=>r?{newErrorValue:r,newSource:"form"}:a?{newErrorValue:void 0,newSource:void 0}:i?{newErrorValue:i,newSource:"field"}:{newErrorValue:void 0,newSource:void 0},se=({formLevelError:r,fieldLevelError:a})=>a?{newErrorValue:a,newSource:"field"}:r?{newErrorValue:r,newSource:"form"}:{newErrorValue:void 0,newSource:void 0};function ye(r){const a=new Map;for(const i of r){const e=[...i.path??[]].map(t=>{const s=typeof t=="object"?t.key:t;return typeof s=="number"?`[${s}]`:s}).join(".").replace(/\.\[/g,"[");a.set(e,(a.get(e)??[]).concat(i))}return Object.fromEntries(a)}const re=r=>{const a=ye(r);return{form:a,fields:a}},T={validate({value:r,validationSource:a},i){const e=i["~standard"].validate(r);if(e instanceof Promise)throw new Error("async function passed to sync validator");if(e.issues)return a==="field"?e.issues:re(e.issues)},async validateAsync({value:r,validationSource:a},i){const e=await i["~standard"].validate(r);if(e.issues)return a==="field"?e.issues:re(e.issues)}},ne=r=>!!r&&"~standard"in r,R={isValidating:!1,isTouched:!1,isBlurred:!1,isDirty:!1,isPristine:!0,isValid:!0,isDefaultValue:!0,errors:[],errorMap:{},errorSourceMap:{}};function W(r){function a(u,l,v,f){const m=e(u,l,v,f);({insert:()=>o(m,u,l),remove:()=>d(m),swap:()=>f!==void 0&&c(m,u,l,f),move:()=>f!==void 0&&h(m,u,l,f)})[v]()}function i(u,l){return`${u}[${l}]`}function e(u,l,v,f){const m=[i(u,l)];if(v==="swap")m.push(i(u,f));else if(v==="move"){const[p,M]=[Math.min(l,f),Math.max(l,f)];for(let g=p;g<=M;g++)m.push(i(u,g))}else{const p=r.getFieldValue(u),M=Array.isArray(p)?p.length:0;for(let g=l+1;g<M;g++)m.push(i(u,g))}return Object.keys(r.fieldInfo).filter(p=>m.some(M=>p.startsWith(M)))}function t(u,l){return u.replace(/\[(\d+)\]/,(v,f)=>{const m=parseInt(f,10);return`[${l==="up"?m+1:Math.max(0,m-1)}]`})}function s(u,l){(l==="up"?u:[...u].reverse()).forEach(f=>{const m=t(f.toString(),l),p=r.getFieldMeta(m);p?r.setFieldMeta(f,p):r.setFieldMeta(f,n())})}const n=()=>R,o=(u,l,v)=>{s(u,"down"),u.forEach(f=>{f.toString().startsWith(i(l,v))&&r.setFieldMeta(f,n())})},d=u=>{s(u,"up")},h=(u,l,v,f)=>{const m=new Map(Object.keys(r.fieldInfo).filter(p=>p.startsWith(i(l,v))).map(p=>[p,r.getFieldMeta(p)]));s(u,v<f?"up":"down"),Object.keys(r.fieldInfo).filter(p=>p.startsWith(i(l,f))).forEach(p=>{const M=p.replace(i(l,f),i(l,v)),g=m.get(M);g&&r.setFieldMeta(p,g)})},c=(u,l,v,f)=>{u.forEach(m=>{if(!m.toString().startsWith(i(l,v)))return;const p=m.toString().replace(i(l,v),i(l,f)),[M,g]=[r.getFieldMeta(m),r.getFieldMeta(p)];M&&r.setFieldMeta(p,M),g&&r.setFieldMeta(m,g)})};return{handleArrayFieldMetaShift:a}}function x(r){return{values:r.values??{},errorMap:r.errorMap??{},fieldMetaBase:r.fieldMetaBase??{},isSubmitted:r.isSubmitted??!1,isSubmitting:r.isSubmitting??!1,isValidating:r.isValidating??!1,submissionAttempts:r.submissionAttempts??0,isSubmitSuccessful:r.isSubmitSuccessful??!1,validationMetaMap:r.validationMetaMap??{onChange:void 0,onBlur:void 0,onSubmit:void 0,onMount:void 0,onServer:void 0}}}class Se{constructor(a){var i;this.options={},this.fieldInfo={},this.prevTransformArray=[],this.mount=()=>{var e,t;const s=this.fieldMetaDerived.mount(),n=this.store.mount(),o=()=>{s(),n()};(t=(e=this.options.listeners)==null?void 0:e.onMount)==null||t.call(e,{formApi:this});const{onMount:d}=this.options.validators||{};return d&&this.validateSync("mount"),o},this.update=e=>{var t,s;if(!e)return;const n=this.options;this.options=e;const o=!!((s=(t=e.transform)==null?void 0:t.deps)!=null&&s.some((c,u)=>c!==this.prevTransformArray[u])),d=e.defaultValues&&!I(e.defaultValues,n.defaultValues)&&!this.state.isTouched,h=!I(e.defaultState,n.defaultState)&&!this.state.isTouched;!d&&!h&&!o||_(()=>{this.baseStore.setState(()=>x(Object.assign({},this.state,h?e.defaultState:{},d?{values:e.defaultValues}:{},o?{_force_re_eval:!this.state._force_re_eval}:{})))})},this.reset=(e,t)=>{const{fieldMeta:s}=this.state,n=this.resetFieldMeta(s);e&&!(t!=null&&t.keepDefaultValues)&&(this.options={...this.options,defaultValues:e}),this.baseStore.setState(()=>{var o;return x({...this.options.defaultState,values:e??this.options.defaultValues??((o=this.options.defaultState)==null?void 0:o.values),fieldMetaBase:n})})},this.validateAllFields=async e=>{const t=[];return _(()=>{Object.values(this.fieldInfo).forEach(n=>{if(!n.instance)return;const o=n.instance;t.push(Promise.resolve().then(()=>o.validate(e,{skipFormValidation:!0}))),n.instance.state.meta.isTouched||n.instance.setMeta(d=>({...d,isTouched:!0}))})}),(await Promise.all(t)).flat()},this.validateArrayFieldsStartingFrom=async(e,t,s)=>{const n=this.getFieldValue(e),o=Array.isArray(n)?Math.max(n.length-1,0):null,d=[`${e}[${t}]`];for(let l=t+1;l<=(o??0);l++)d.push(`${e}[${l}]`);const h=Object.keys(this.fieldInfo).filter(l=>d.some(v=>l.startsWith(v))),c=[];return _(()=>{h.forEach(l=>{c.push(Promise.resolve().then(()=>this.validateField(l,s)))})}),(await Promise.all(c)).flat()},this.validateField=(e,t)=>{var s;const n=(s=this.fieldInfo[e])==null?void 0:s.instance;return n?(n.state.meta.isTouched||n.setMeta(o=>({...o,isTouched:!0})),n.validate(t)):[]},this.validateSync=e=>{const t=Y(e,this.options);let s=!1;const n={};return _(()=>{var o,d;for(const c of t){if(!c.validate)continue;const u=this.runValidator({validate:c.validate,value:{value:this.state.values,formApi:this,validationSource:"form"},type:"validate"}),{formError:l,fieldErrors:v}=U(u),f=z(c.cause);for(const m of Object.keys(this.state.fieldMeta)){const p=this.getFieldMeta(m);if(!p)continue;const{errorMap:M,errorSourceMap:g}=p,S=v==null?void 0:v[m],{newErrorValue:F,newSource:b}=ie({newFormValidatorError:S,isPreviousErrorFromFormValidator:(g==null?void 0:g[f])==="form",previousErrorValue:M==null?void 0:M[f]});b==="form"&&(n[m]={...n[m],[f]:S}),(M==null?void 0:M[f])!==F&&this.setFieldMeta(m,E=>({...E,errorMap:{...E.errorMap,[f]:F},errorSourceMap:{...E.errorSourceMap,[f]:b}}))}((o=this.state.errorMap)==null?void 0:o[f])!==l&&this.baseStore.setState(m=>({...m,errorMap:{...m.errorMap,[f]:l}})),(l||v)&&(s=!0)}const h=z("submit");(d=this.state.errorMap)!=null&&d[h]&&e!=="submit"&&!s&&this.baseStore.setState(c=>({...c,errorMap:{...c.errorMap,[h]:void 0}}))}),{hasErrored:s,fieldsErrorMap:n}},this.validateAsync=async e=>{const t=X(e,this.options);this.state.isFormValidating||this.baseStore.setState(h=>({...h,isFormValidating:!0}));const s=[];let n;for(const h of t){if(!h.validate)continue;const c=z(h.cause),u=this.state.validationMetaMap[c];u==null||u.lastAbortController.abort();const l=new AbortController;this.state.validationMetaMap[c]={lastAbortController:l},s.push(new Promise(async v=>{let f;try{f=await new Promise((g,S)=>{setTimeout(async()=>{if(l.signal.aborted)return g(void 0);try{g(await this.runValidator({validate:h.validate,value:{value:this.state.values,formApi:this,validationSource:"form",signal:l.signal},type:"validateAsync"}))}catch(F){S(F)}},h.debounceMs)})}catch(g){f=g}const{formError:m,fieldErrors:p}=U(f);p&&(n=n?{...n,...p}:p);const M=z(h.cause);for(const g of Object.keys(this.state.fieldMeta)){const S=this.getFieldMeta(g);if(!S)continue;const{errorMap:F,errorSourceMap:b}=S,E=n==null?void 0:n[g],{newErrorValue:w,newSource:O}=ie({newFormValidatorError:E,isPreviousErrorFromFormValidator:(b==null?void 0:b[M])==="form",previousErrorValue:F==null?void 0:F[M]});(F==null?void 0:F[M])!==w&&this.setFieldMeta(g,V=>({...V,errorMap:{...V.errorMap,[M]:w},errorSourceMap:{...V.errorSourceMap,[M]:O}}))}this.baseStore.setState(g=>({...g,errorMap:{...g.errorMap,[M]:m}})),v(n?{fieldErrors:n,errorMapKey:M}:void 0)}))}let o=[];const d={};if(s.length){o=await Promise.all(s);for(const h of o)if(h!=null&&h.fieldErrors){const{errorMapKey:c}=h;for(const[u,l]of Object.entries(h.fieldErrors)){const f={...d[u]||{},[c]:l};d[u]=f}}}return this.baseStore.setState(h=>({...h,isFormValidating:!1})),d},this.validate=e=>{const{hasErrored:t,fieldsErrorMap:s}=this.validateSync(e);return t&&!this.options.asyncAlways?s:this.validateAsync(e)},this.getFieldValue=e=>C(this.state.values,e),this.getFieldMeta=e=>this.state.fieldMeta[e],this.getFieldInfo=e=>{var t;return(t=this.fieldInfo)[e]||(t[e]={instance:null,validationMetaMap:{onChange:void 0,onBlur:void 0,onSubmit:void 0,onMount:void 0,onServer:void 0}})},this.setFieldMeta=(e,t)=>{this.baseStore.setState(s=>({...s,fieldMetaBase:{...s.fieldMetaBase,[e]:H(t,s.fieldMetaBase[e])}}))},this.resetFieldMeta=e=>Object.keys(e).reduce((t,s)=>{const n=s;return t[n]=R,t},{}),this.setFieldValue=(e,t,s)=>{const n=(s==null?void 0:s.dontUpdateMeta)??!1;_(()=>{n||this.setFieldMeta(e,o=>({...o,isTouched:!0,isDirty:!0,errorMap:{...o==null?void 0:o.errorMap,onMount:void 0}})),this.baseStore.setState(o=>({...o,values:$(o.values,e,t)}))})},this.deleteField=e=>{const s=[...Object.keys(this.fieldInfo).filter(n=>{const o=e.toString();return n!==o&&n.startsWith(o)}),e];this.baseStore.setState(n=>{const o={...n};return s.forEach(d=>{o.values=he(o.values,d),delete this.fieldInfo[d],delete o.fieldMetaBase[d]}),o})},this.pushFieldValue=(e,t,s)=>{this.setFieldValue(e,n=>[...Array.isArray(n)?n:[],t],s),this.validateField(e,"change")},this.insertFieldValue=async(e,t,s,n)=>{this.setFieldValue(e,o=>[...o.slice(0,t),s,...o.slice(t)],n),await this.validateField(e,"change"),W(this).handleArrayFieldMetaShift(e,t,"insert"),await this.validateArrayFieldsStartingFrom(e,t,"change")},this.replaceFieldValue=async(e,t,s,n)=>{this.setFieldValue(e,o=>o.map((d,h)=>h===t?s:d),n),await this.validateField(e,"change"),await this.validateArrayFieldsStartingFrom(e,t,"change")},this.removeFieldValue=async(e,t,s)=>{const n=this.getFieldValue(e),o=Array.isArray(n)?Math.max(n.length-1,0):null;if(this.setFieldValue(e,d=>d.filter((h,c)=>c!==t),s),W(this).handleArrayFieldMetaShift(e,t,"remove"),o!==null){const d=`${e}[${o}]`;this.deleteField(d)}await this.validateField(e,"change"),await this.validateArrayFieldsStartingFrom(e,t,"change")},this.swapFieldValues=(e,t,s,n)=>{this.setFieldValue(e,o=>{const d=o[t],h=o[s];return $($(o,`${t}`,h),`${s}`,d)},n),W(this).handleArrayFieldMetaShift(e,t,"swap",s),this.validateField(e,"change"),this.validateField(`${e}[${t}]`,"change"),this.validateField(`${e}[${s}]`,"change")},this.moveFieldValues=(e,t,s,n)=>{this.setFieldValue(e,o=>{const d=[...o];return d.splice(s,0,d.splice(t,1)[0]),d},n),W(this).handleArrayFieldMetaShift(e,t,"move",s),this.validateField(e,"change"),this.validateField(`${e}[${t}]`,"change"),this.validateField(`${e}[${s}]`,"change")},this.clearFieldValues=(e,t)=>{const s=this.getFieldValue(e),n=Array.isArray(s)?Math.max(s.length-1,0):null;if(this.setFieldValue(e,[],t),n!==null)for(let o=0;o<=n;o++){const d=`${e}[${o}]`;this.deleteField(d)}this.validateField(e,"change")},this.resetField=e=>{this.baseStore.setState(t=>({...t,fieldMetaBase:{...t.fieldMetaBase,[e]:R},values:this.options.defaultValues?$(t.values,e,C(this.options.defaultValues,e)):t.values}))},this.getAllErrors=()=>({form:{errors:this.state.errors,errorMap:this.state.errorMap},fields:Object.entries(this.state.fieldMeta).reduce((e,[t,s])=>(Object.keys(s).length&&s.errors.length&&(e[t]={errors:s.errors,errorMap:s.errorMap}),e),{})}),this.parseValuesWithSchema=e=>T.validate({value:this.state.values,validationSource:"form"},e),this.parseValuesWithSchemaAsync=e=>T.validateAsync({value:this.state.values,validationSource:"form"},e),this.baseStore=new ce(x({...a==null?void 0:a.defaultState,values:(a==null?void 0:a.defaultValues)??((i=a==null?void 0:a.defaultState)==null?void 0:i.values)})),this.fieldMetaDerived=new J({deps:[this.baseStore],fn:({prevDepVals:e,currDepVals:t,prevVal:s})=>{var n,o,d;const h=s,c=e==null?void 0:e[0],u=t[0];let l=0;const v={};for(const f of Object.keys(u.fieldMetaBase)){const m=u.fieldMetaBase[f],p=c==null?void 0:c.fieldMetaBase[f],M=h==null?void 0:h[f],g=C(u.values,f);let S=M==null?void 0:M.errors;if(!p||m.errorMap!==p.errorMap){S=Object.values(m.errorMap??{}).filter(O=>O!==void 0);const w=(n=this.getFieldInfo(f))==null?void 0:n.instance;w&&!w.options.disableErrorFlat&&(S=S==null?void 0:S.flat(1))}const F=!ge(S??[]),b=!m.isDirty,E=I(g,C(this.options.defaultValues,f))||I(g,(d=(o=this.getFieldInfo(f))==null?void 0:o.instance)==null?void 0:d.options.defaultValue);if(M&&M.isPristine===b&&M.isValid===F&&M.isDefaultValue===E&&M.errors===S&&m===p){v[f]=M,l++;continue}v[f]={...m,errors:S,isPristine:b,isValid:F,isDefaultValue:E}}return Object.keys(u.fieldMetaBase).length&&h&&l===Object.keys(u.fieldMetaBase).length?h:v}}),this.store=new J({deps:[this.baseStore,this.fieldMetaDerived],fn:({prevDepVals:e,currDepVals:t,prevVal:s})=>{var n,o,d,h;const c=s,u=e==null?void 0:e[0],l=t[0],v=t[1],f=Object.values(v).filter(Boolean),m=f.some(y=>y.isValidating),p=f.every(y=>y.isValid),M=f.some(y=>y.isTouched),g=f.some(y=>y.isBlurred),S=f.every(y=>y.isDefaultValue),F=M&&((n=l.errorMap)==null?void 0:n.onMount),b=f.some(y=>y.isDirty),E=!b,w=!!((o=l.errorMap)!=null&&o.onMount||f.some(y=>{var A;return(A=y==null?void 0:y.errorMap)==null?void 0:A.onMount})),O=!!m;let V=(c==null?void 0:c.errors)??[];(!u||l.errorMap!==u.errorMap)&&(V=Object.values(l.errorMap).reduce((y,A)=>A===void 0?y:A&&Z(A)?(y.push(A.form),y):(y.push(A),y),[]));const D=V.length===0,k=p&&D,ue=this.options.canSubmitWhenInvalid??!1,te=l.submissionAttempts===0&&!M&&!w||!O&&!l.isSubmitting&&k||ue;let L=l.errorMap;if(F&&(V=V.filter(y=>y!==l.errorMap.onMount),L=Object.assign(L,{onMount:void 0})),c&&u&&c.errorMap===L&&c.fieldMeta===this.fieldMetaDerived.state&&c.errors===V&&c.isFieldsValidating===m&&c.isFieldsValid===p&&c.isFormValid===D&&c.isValid===k&&c.canSubmit===te&&c.isTouched===M&&c.isBlurred===g&&c.isPristine===E&&c.isDefaultValue===S&&c.isDirty===b&&I(u,l))return c;let G={...l,errorMap:L,fieldMeta:this.fieldMetaDerived.state,errors:V,isFieldsValidating:m,isFieldsValid:p,isFormValid:D,isValid:k,canSubmit:te,isTouched:M,isBlurred:g,isPristine:E,isDefaultValue:S,isDirty:b};const q=((d=this.options.transform)==null?void 0:d.deps)??[];if(q.length!==this.prevTransformArray.length||q.some((y,A)=>y!==this.prevTransformArray[A])){const y=Object.assign({},this,{state:G});(h=this.options.transform)==null||h.fn(y),G=y.state,this.prevTransformArray=q}return G}}),this.handleSubmit=this.handleSubmit.bind(this),this.update(a||{})}get state(){return this.store.state}runValidator(a){return ne(a.validate)?T[a.type](a.value,a.validate):a.validate(a.value)}async handleSubmit(a){var i,e,t,s,n,o,d,h;if(this.baseStore.setState(u=>({...u,isSubmitted:!1,submissionAttempts:u.submissionAttempts+1,isSubmitSuccessful:!1})),_(()=>{Object.values(this.fieldInfo).forEach(u=>{u.instance&&(u.instance.state.meta.isTouched||u.instance.setMeta(l=>({...l,isTouched:!0})))})}),!this.state.canSubmit)return;this.baseStore.setState(u=>({...u,isSubmitting:!0}));const c=()=>{this.baseStore.setState(u=>({...u,isSubmitting:!1}))};if(await this.validateAllFields("submit"),!this.state.isFieldsValid){c(),(e=(i=this.options).onSubmitInvalid)==null||e.call(i,{value:this.state.values,formApi:this});return}if(await this.validate("submit"),!this.state.isValid){c(),(s=(t=this.options).onSubmitInvalid)==null||s.call(t,{value:this.state.values,formApi:this});return}_(()=>{Object.values(this.fieldInfo).forEach(u=>{var l,v,f;(f=(v=(l=u.instance)==null?void 0:l.options.listeners)==null?void 0:v.onSubmit)==null||f.call(v,{value:u.instance.state.value,fieldApi:u.instance})})}),(o=(n=this.options.listeners)==null?void 0:n.onSubmit)==null||o.call(n,{formApi:this});try{await((h=(d=this.options).onSubmit)==null?void 0:h.call(d,{value:this.state.values,formApi:this,meta:a??this.options.onSubmitMeta})),_(()=>{this.baseStore.setState(u=>({...u,isSubmitted:!0,isSubmitSuccessful:!0})),c()})}catch(u){throw this.baseStore.setState(l=>({...l,isSubmitSuccessful:!1})),c(),u}}setErrorMap(a){_(()=>{Object.entries(a).forEach(([i,e])=>{const t=i;if(Z(e)){const{formError:s,fieldErrors:n}=U(e);for(const o of Object.keys(this.fieldInfo))this.getFieldMeta(o)&&this.setFieldMeta(o,h=>({...h,errorMap:{...h.errorMap,[t]:n==null?void 0:n[o]},errorSourceMap:{...h.errorSourceMap,[t]:"form"}}));this.baseStore.setState(o=>({...o,errorMap:{...o.errorMap,[t]:s}}))}else this.baseStore.setState(s=>({...s,errorMap:{...s.errorMap,[t]:e}}))})})}}function U(r){if(r){if(Z(r)){const a=U(r.form).formError,i=r.fields;return{formError:a,fieldErrors:i}}return{formError:r}}return{formError:void 0}}function z(r){switch(r){case"submit":return"onSubmit";case"blur":return"onBlur";case"mount":return"onMount";case"server":return"onServer";case"change":default:return"onChange"}}class Ve{constructor(a){this.options={},this.mount=()=>{var i,e;const t=this.store.mount();this.options.defaultValue!==void 0&&this.form.setFieldValue(this.name,this.options.defaultValue,{dontUpdateMeta:!0});const s=this.getInfo();s.instance=this,this.update(this.options);const{onMount:n}=this.options.validators||{};if(n){const o=this.runValidator({validate:n,value:{value:this.state.value,fieldApi:this,validationSource:"field"},type:"validate"});o&&this.setMeta(d=>({...d,errorMap:{...d==null?void 0:d.errorMap,onMount:o},errorSourceMap:{...d==null?void 0:d.errorSourceMap,onMount:"field"}}))}return(e=(i=this.options.listeners)==null?void 0:i.onMount)==null||e.call(i,{value:this.state.value,fieldApi:this}),t},this.update=i=>{this.options=i;const e=this.name!==i.name;if(this.name=i.name,this.state.value===void 0){const t=C(i.form.options.defaultValues,i.name),s=i.defaultValue??t;e?this.setValue(n=>n||s,{dontUpdateMeta:!0}):s!==void 0&&this.setValue(s,{dontUpdateMeta:!0})}this.form.getFieldMeta(this.name)===void 0&&this.setMeta(this.state.meta)},this.getValue=()=>this.form.getFieldValue(this.name),this.setValue=(i,e)=>{this.form.setFieldValue(this.name,i,e),this.triggerOnChangeListener(),this.validate("change")},this.getMeta=()=>this.store.state.meta,this.setMeta=i=>this.form.setFieldMeta(this.name,i),this.getInfo=()=>this.form.getFieldInfo(this.name),this.pushValue=(i,e)=>{this.form.pushFieldValue(this.name,i,e),this.triggerOnChangeListener()},this.insertValue=(i,e,t)=>{this.form.insertFieldValue(this.name,i,e,t),this.triggerOnChangeListener()},this.replaceValue=(i,e,t)=>{this.form.replaceFieldValue(this.name,i,e,t),this.triggerOnChangeListener()},this.removeValue=(i,e)=>{this.form.removeFieldValue(this.name,i,e),this.triggerOnChangeListener()},this.swapValues=(i,e,t)=>{this.form.swapFieldValues(this.name,i,e,t),this.triggerOnChangeListener()},this.moveValue=(i,e,t)=>{this.form.moveFieldValues(this.name,i,e,t),this.triggerOnChangeListener()},this.clearValues=i=>{this.form.clearFieldValues(this.name,i),this.triggerOnChangeListener()},this.getLinkedFields=i=>{const e=Object.values(this.form.fieldInfo),t=[];for(const s of e){if(!s.instance)continue;const{onChangeListenTo:n,onBlurListenTo:o}=s.instance.options.validators||{};i==="change"&&(n!=null&&n.includes(this.name))&&t.push(s.instance),i==="blur"&&(o!=null&&o.includes(this.name))&&t.push(s.instance)}return t},this.validateSync=(i,e)=>{var t;const s=Y(i,this.options),o=this.getLinkedFields(i).reduce((c,u)=>{const l=Y(i,u.options);return l.forEach(v=>{v.field=u}),c.concat(l)},[]);let d=!1;_(()=>{const c=(u,l)=>{var v;const f=K(l.cause),m=l.validate?ae(u.runValidator({validate:l.validate,value:{value:u.store.state.value,validationSource:"field",fieldApi:u},type:"validate"})):void 0,p=e[f],{newErrorValue:M,newSource:g}=se({formLevelError:p,fieldLevelError:m});((v=u.state.meta.errorMap)==null?void 0:v[f])!==M&&u.setMeta(S=>({...S,errorMap:{...S.errorMap,[f]:M},errorSourceMap:{...S.errorSourceMap,[f]:g}})),M&&(d=!0)};for(const u of s)c(this,u);for(const u of o)u.validate&&c(u.field,u)});const h=K("submit");return(t=this.state.meta.errorMap)!=null&&t[h]&&i!=="submit"&&!d&&this.setMeta(c=>({...c,errorMap:{...c.errorMap,[h]:void 0},errorSourceMap:{...c.errorSourceMap,[h]:void 0}})),{hasErrored:d}},this.validateAsync=async(i,e)=>{const t=X(i,this.options),s=await e,n=this.getLinkedFields(i),o=n.reduce((l,v)=>{const f=X(i,v.options);return f.forEach(m=>{m.field=v}),l.concat(f)},[]);this.state.meta.isValidating||this.setMeta(l=>({...l,isValidating:!0}));for(const l of n)l.setMeta(v=>({...v,isValidating:!0}));const d=[],h=[],c=(l,v,f)=>{const m=K(v.cause),p=l.getInfo().validationMetaMap[m];p==null||p.lastAbortController.abort();const M=new AbortController;this.getInfo().validationMetaMap[m]={lastAbortController:M},f.push(new Promise(async g=>{var S;let F;try{F=await new Promise((V,D)=>{this.timeoutIds.validations[v.cause]&&clearTimeout(this.timeoutIds.validations[v.cause]),this.timeoutIds.validations[v.cause]=setTimeout(async()=>{if(M.signal.aborted)return V(void 0);try{V(await this.runValidator({validate:v.validate,value:{value:l.store.state.value,fieldApi:l,signal:M.signal,validationSource:"field"},type:"validateAsync"}))}catch(k){D(k)}},v.debounceMs)})}catch(V){F=V}if(M.signal.aborted)return g(void 0);const b=ae(F),E=(S=s[this.name])==null?void 0:S[m],{newErrorValue:w,newSource:O}=se({formLevelError:E,fieldLevelError:b});l.setMeta(V=>({...V,errorMap:{...V==null?void 0:V.errorMap,[m]:w},errorSourceMap:{...V.errorSourceMap,[m]:O}})),g(w)}))};for(const l of t)l.validate&&c(this,l,d);for(const l of o)l.validate&&c(l.field,l,h);let u=[];(d.length||h.length)&&(u=await Promise.all(d),await Promise.all(h)),this.setMeta(l=>({...l,isValidating:!1}));for(const l of n)l.setMeta(v=>({...v,isValidating:!1}));return u.filter(Boolean)},this.validate=(i,e)=>{var t;if(!this.state.meta.isTouched)return[];const{fieldsErrorMap:s}=e!=null&&e.skipFormValidation?{fieldsErrorMap:{}}:this.form.validateSync(i),{hasErrored:n}=this.validateSync(i,s[this.name]??{});if(n&&!this.options.asyncAlways)return(t=this.getInfo().validationMetaMap[K(i)])==null||t.lastAbortController.abort(),this.state.meta.errors;const o=e!=null&&e.skipFormValidation?Promise.resolve({}):this.form.validateAsync(i);return this.validateAsync(i,o)},this.handleChange=i=>{this.setValue(i)},this.handleBlur=()=>{this.state.meta.isTouched||this.setMeta(e=>({...e,isTouched:!0})),this.state.meta.isBlurred||this.setMeta(e=>({...e,isBlurred:!0})),this.validate("blur"),this.triggerOnBlurListener()},this.parseValueWithSchema=i=>T.validate({value:this.state.value,validationSource:"field"},i),this.parseValueWithSchemaAsync=i=>T.validateAsync({value:this.state.value,validationSource:"field"},i),this.form=a.form,this.name=a.name,this.timeoutIds={validations:{},listeners:{},formListeners:{}},this.store=new J({deps:[this.form.store],fn:()=>{const i=this.form.getFieldValue(this.name),e=this.form.getFieldMeta(this.name)??{...R,...a.defaultMeta};return{value:i,meta:e}}}),this.options=a}get state(){return this.store.state}runValidator(a){return ne(a.validate)?T[a.type](a.value,a.validate):a.validate(a.value)}setErrorMap(a){this.setMeta(i=>({...i,errorMap:{...i.errorMap,...a}}))}triggerOnBlurListener(){var a,i,e,t,s,n;const o=(a=this.form.options.listeners)==null?void 0:a.onBlurDebounceMs;o&&o>0?(this.timeoutIds.formListeners.blur&&clearTimeout(this.timeoutIds.formListeners.blur),this.timeoutIds.formListeners.blur=setTimeout(()=>{var h,c;(c=(h=this.form.options.listeners)==null?void 0:h.onBlur)==null||c.call(h,{formApi:this.form,fieldApi:this})},o)):(e=(i=this.form.options.listeners)==null?void 0:i.onBlur)==null||e.call(i,{formApi:this.form,fieldApi:this});const d=(t=this.options.listeners)==null?void 0:t.onBlurDebounceMs;d&&d>0?(this.timeoutIds.listeners.blur&&clearTimeout(this.timeoutIds.listeners.blur),this.timeoutIds.listeners.blur=setTimeout(()=>{var h,c;(c=(h=this.options.listeners)==null?void 0:h.onBlur)==null||c.call(h,{value:this.state.value,fieldApi:this})},d)):(n=(s=this.options.listeners)==null?void 0:s.onBlur)==null||n.call(s,{value:this.state.value,fieldApi:this})}triggerOnChangeListener(){var a,i,e,t,s,n;const o=(a=this.form.options.listeners)==null?void 0:a.onChangeDebounceMs;o&&o>0?(this.timeoutIds.formListeners.change&&clearTimeout(this.timeoutIds.formListeners.change),this.timeoutIds.formListeners.change=setTimeout(()=>{var h,c;(c=(h=this.form.options.listeners)==null?void 0:h.onChange)==null||c.call(h,{formApi:this.form,fieldApi:this})},o)):(e=(i=this.form.options.listeners)==null?void 0:i.onChange)==null||e.call(i,{formApi:this.form,fieldApi:this});const d=(t=this.options.listeners)==null?void 0:t.onChangeDebounceMs;d&&d>0?(this.timeoutIds.listeners.change&&clearTimeout(this.timeoutIds.listeners.change),this.timeoutIds.listeners.change=setTimeout(()=>{var h,c;(c=(h=this.options.listeners)==null?void 0:h.onChange)==null||c.call(h,{value:this.state.value,fieldApi:this})},d)):(n=(s=this.options.listeners)==null?void 0:s.onChange)==null||n.call(s,{value:this.state.value,fieldApi:this})}}function ae(r){if(r)return r}function K(r){switch(r){case"submit":return"onSubmit";case"blur":return"onBlur";case"mount":return"onMount";case"server":return"onServer";case"change":default:return"onChange"}}function oe(r,a=i=>i){return de.useSyncExternalStoreWithSelector(r.subscribe,()=>r.state,()=>r.state,a,Fe)}function Fe(r,a){if(Object.is(r,a))return!0;if(typeof r!="object"||r===null||typeof a!="object"||a===null)return!1;if(r instanceof Map&&a instanceof Map){if(r.size!==a.size)return!1;for(const[e,t]of r)if(!a.has(e)||!Object.is(t,a.get(e)))return!1;return!0}if(r instanceof Set&&a instanceof Set){if(r.size!==a.size)return!1;for(const e of r)if(!a.has(e))return!1;return!0}if(r instanceof Date&&a instanceof Date)return r.getTime()===a.getTime();const i=Object.keys(r);if(i.length!==Object.keys(a).length)return!1;for(let e=0;e<i.length;e++)if(!Object.prototype.hasOwnProperty.call(a,i[e])||!Object.is(r[i[e]],a[i[e]]))return!1;return!0}const N=typeof window<"u"?P.useLayoutEffect:P.useEffect;function be(r){const[a]=P.useState(()=>{const e=new Ve({...r,form:r.form,name:r.name});return e.Field=le,e});return N(a.mount,[a]),N(()=>{a.update(r)}),oe(a.store,r.mode==="array"?i=>[i.meta,Object.keys(i.value??[]).length]:void 0),a}const le=({children:r,...a})=>{const i=be(a),e=P.useMemo(()=>H(r,i),[r,i,i.state.value,i.state.meta]);return j.jsx(j.Fragment,{children:e})};function Ee({form:r,selector:a,children:i}){const e=oe(r.store,a);return H(i,e)}function _e(r){const[a]=P.useState(()=>{const i=new Se(r),e=i;return e.Field=function(s){return j.jsx(le,{...s,form:i})},e.Subscribe=function(s){return j.jsx(Ee,{form:i,selector:s.selector,children:s.children})},e});return N(a.mount,[]),N(()=>{a.update(r)}),a}export{_e as u};
