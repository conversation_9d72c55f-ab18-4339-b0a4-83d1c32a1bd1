import { createFileRout<PERSON>, <PERSON>, useNavigate } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import {
  ArrowLeft,
  Edit,
  Music,
  Play,
  Download,
  Calendar,
  User,
  Users,
  FileText,
  Settings,
} from "lucide-react";

import { useTRPCClient } from "@/utils/trpc";
import { authClient } from "@/lib/auth-client";
import Loader from "@/components/loader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { UpdateTrackDialog } from "@/routes/dashboard/track/components/update-track-dialog";
import { TrackStatusDialog } from "@/routes/dashboard/track/components/track-status-dialog";
import type { TrackTableItem } from "@/routes/dashboard/track/components/track-table";

export const Route = createFileRoute("/dashboard/track/$id")({
  component: TrackDetailsPage,
  head: () => ({
    meta: [
      {
        title: "Track Details - Soundmera App",
      },
    ],
  }),
});

function TrackDetailsPage() {
  const { id } = Route.useParams();
  const navigate = Route.useNavigate();
  const trpcClient = useTRPCClient();
  const { data: session, isPending: sessionPending } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";

  // Fetch track details
  const {
    data: track,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["track", id],
    queryFn: async () => {
      const response = await trpcClient.track.get.query({ id });
      // Transform the response to match TrackTableItem type
      return {
        ...response,
        createdAt: new Date(response.createdAt),
        updatedAt: new Date(response.updatedAt),
        submittedAt: response.submittedAt
          ? new Date(response.submittedAt)
          : null,
        readyAt: response.readyAt ? new Date(response.readyAt) : null,
      } as TrackTableItem;
    },
    enabled: !!id,
  });

  // Authentication check
  if (sessionPending) {
    return <Loader />;
  }

  if (!session?.user) {
    navigate({ to: "/auth" });
    return <Loader />;
  }

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="container mx-auto p-2 space-y-6">
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <Music className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-semibold text-foreground">
              Track not found
            </h3>
            <p className="mt-1 text-sm text-muted-foreground">
              The track you're looking for doesn't exist or you don't have
              permission to view it.
            </p>
            <div className="mt-6">
              <Link to="/dashboard/track">
                <Button>Back to Tracks</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!track) {
    return <Loader />;
  }

  const handleTrackUpdated = () => {
    refetch();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="container mx-auto p-2 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <Link
          to="/dashboard/track"
          className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground hover:underline mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Tracks
        </Link>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">{track.title}</h1>
            <p className="text-muted-foreground text-sm">Track Details</p>
          </div>
          <div className="flex items-center space-x-2">
            <TrackStatusDialog
              track={track}
              onStatusUpdated={handleTrackUpdated}
            >
              <Button variant="outline">
                <Badge
                  variant={track.status === "READY" ? "default" : "secondary"}
                  className="cursor-pointer"
                >
                  {track.status}
                </Badge>
              </Button>
            </TrackStatusDialog>
            <UpdateTrackDialog
              track={track}
              onTrackUpdated={handleTrackUpdated}
            >
              <Button>
                <Edit className="h-4 w-4" />
                Edit Track
              </Button>
            </UpdateTrackDialog>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Title
                  </p>
                  <p className="text-sm">{track.title}</p>
                </div>
                {track.isrc && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      ISRC
                    </p>
                    <p className="text-sm font-mono">{track.isrc}</p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Genre
                  </p>
                  <p className="text-sm">{track.genre}</p>
                </div>
                {track.subGenre && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Sub-genre
                    </p>
                    <p className="text-sm">{track.subGenre}</p>
                  </div>
                )}
              </div>

              {track.trackVersion && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Version
                  </p>
                  <p className="text-sm">{track.trackVersion}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Recording Year
                  </p>
                  <p className="text-sm">{track.recordingYear}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Publishing Year
                  </p>
                  <p className="text-sm">{track.publishingYear}</p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Publishing Holder
                </p>
                <p className="text-sm">{track.publishingHolder}</p>
              </div>
            </CardContent>
          </Card>

          {/* Content & Rights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Content & Rights</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Explicit Content
                  </p>
                  <Badge variant="outline" className="mt-1">
                    {track.explicit.replace("_", " ")}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Rights Claim
                  </p>
                  <Badge variant="outline" className="mt-1">
                    {track.rightsClaim.replace("_", " ")}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Status
                  </p>
                  <Badge
                    variant={track.status === "READY" ? "default" : "secondary"}
                    className="mt-1"
                  >
                    {track.status}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Metadata Language
                  </p>
                  <p className="text-sm uppercase">{track.metadataLanguage}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Audio Language
                  </p>
                  <p className="text-sm uppercase">{track.audioLanguage}</p>
                </div>
              </div>

              {(track.previewStart || track.previewLength) && (
                <div className="grid grid-cols-2 gap-4">
                  {track.previewStart && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Preview Start
                      </p>
                      <p className="text-sm font-mono">{track.previewStart}</p>
                    </div>
                  )}
                  {track.previewLength && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Preview Length
                      </p>
                      <p className="text-sm font-mono">{track.previewLength}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Lyrics */}
          {track.lyrics && (
            <Card>
              <CardHeader>
                <CardTitle>Lyrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="whitespace-pre-wrap text-sm">
                  {track.lyrics}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Track Files */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Music className="h-5 w-5" />
                <span>Track Files ({track.trackFiles.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {track.trackFiles.length > 0 ? (
                <div className="space-y-3">
                  {track.trackFiles.map((file, index) => (
                    <div
                      key={file.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-primary/10 rounded">
                          <Music className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">
                            {file.fileName || `Track File ${index + 1}`}
                          </p>
                          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                            {file.fileSize && (
                              <span>{formatFileSize(file.fileSize)}</span>
                            )}
                            {file.duration && (
                              <span>{formatDuration(file.duration)}</span>
                            )}
                            {file.mimeType && (
                              <span className="uppercase">
                                {file.mimeType.split("/")[1]}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(file.fileUrl, "_blank")}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const link = document.createElement("a");
                            link.href = file.fileUrl;
                            link.download = file.fileName || "track";
                            link.click();
                          }}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  No track files uploaded
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Artists */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Artists ({track.artists.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {track.artists.length > 0 ? (
                <div className="space-y-3">
                  {track.artists.map((artist, index) => (
                    <div
                      key={`${artist.artist.id}-${index}`}
                      className="flex items-center justify-between"
                    >
                      <div>
                        <p className="font-medium">{artist.artist.name}</p>
                        <Badge
                          variant={
                            artist.role === "PRIMARY" ? "default" : "secondary"
                          }
                          className="text-xs mt-1"
                        >
                          {artist.role === "PRIMARY" ? "Primary" : "Featuring"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  No artists assigned
                </p>
              )}
            </CardContent>
          </Card>

          {/* Contributors */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Contributors ({track.contributors.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {track.contributors.length > 0 ? (
                <div className="space-y-3">
                  {track.contributors.map((contributor, index) => (
                    <div key={`${contributor.contributor.id}-${index}`}>
                      <p className="font-medium">
                        {contributor.contributor.name}
                      </p>
                      <Badge variant="outline" className="text-xs mt-1">
                        {contributor.role}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  No contributors assigned
                </p>
              )}
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Metadata</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Created
                </p>
                <p className="text-sm">{track.createdAt.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Last Updated
                </p>
                <p className="text-sm">{track.updatedAt.toLocaleString()}</p>
              </div>
              {track.submittedAt && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Submitted
                  </p>
                  <p className="text-sm">
                    {track.submittedAt.toLocaleString()}
                  </p>
                </div>
              )}
              {track.readyAt && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Ready
                  </p>
                  <p className="text-sm">{track.readyAt.toLocaleString()}</p>
                </div>
              )}
              <Separator />
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Created By
                </p>
                <p className="text-sm">{track.user.name}</p>
                <p className="text-xs text-muted-foreground">
                  {track.user.email}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
