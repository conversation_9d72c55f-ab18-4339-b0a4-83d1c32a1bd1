import{r as p,j as s}from"./main-CiHxKC0e.js";import{u as A,e as $,a as L}from"./button-mzA_gOdo.js";import{u as z,c as G,a as H,b as P,P as O,f as K,v as U,x as V,w as W,F as Z,D as q}from"./dialog-B3qaM3HV.js";import{c as _,R as B,A as w,a as J,C as Q}from"./select-Be91QLyZ.js";import{P as j}from"./label-C9y0GF5L.js";var x="Popover",[b,he]=G(x,[_]),g=_(),[X,d]=b(x),E=e=>{const{__scopePopover:n,children:t,open:a,defaultOpen:o,onOpenChange:r,modal:c=!1}=e,i=g(n),l=p.useRef(null),[u,h]=p.useState(!1),[m,f]=z({prop:a,defaultProp:o??!1,onChange:r,caller:x});return s.jsx(B,{...i,children:s.jsx(X,{scope:n,contentId:H(),triggerRef:l,open:m,onOpenChange:f,onOpenToggle:p.useCallback(()=>f(C=>!C),[f]),hasCustomAnchor:u,onCustomAnchorAdd:p.useCallback(()=>h(!0),[]),onCustomAnchorRemove:p.useCallback(()=>h(!1),[]),modal:c,children:t})})};E.displayName=x;var N="PopoverAnchor",Y=p.forwardRef((e,n)=>{const{__scopePopover:t,...a}=e,o=d(N,t),r=g(t),{onCustomAnchorAdd:c,onCustomAnchorRemove:i}=o;return p.useEffect(()=>(c(),()=>i()),[c,i]),s.jsx(w,{...r,...a,ref:n})});Y.displayName=N;var F="PopoverTrigger",S=p.forwardRef((e,n)=>{const{__scopePopover:t,...a}=e,o=d(F,t),r=g(t),c=A(n,o.triggerRef),i=s.jsx(j.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":I(o.open),...a,ref:c,onClick:P(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?i:s.jsx(w,{asChild:!0,...r,children:i})});S.displayName=F;var R="PopoverPortal",[ee,oe]=b(R,{forceMount:void 0}),y=e=>{const{__scopePopover:n,forceMount:t,children:a,container:o}=e,r=d(R,n);return s.jsx(ee,{scope:n,forceMount:t,children:s.jsx(O,{present:t||r.open,children:s.jsx(K,{asChild:!0,container:o,children:a})})})};y.displayName=R;var v="PopoverContent",D=p.forwardRef((e,n)=>{const t=oe(v,e.__scopePopover),{forceMount:a=t.forceMount,...o}=e,r=d(v,e.__scopePopover);return s.jsx(O,{present:a||r.open,children:r.modal?s.jsx(re,{...o,ref:n}):s.jsx(ne,{...o,ref:n})})});D.displayName=v;var te=$("PopoverContent.RemoveScroll"),re=p.forwardRef((e,n)=>{const t=d(v,e.__scopePopover),a=p.useRef(null),o=A(n,a),r=p.useRef(!1);return p.useEffect(()=>{const c=a.current;if(c)return U(c)},[]),s.jsx(V,{as:te,allowPinchZoom:!0,children:s.jsx(M,{...e,ref:o,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:P(e.onCloseAutoFocus,c=>{var i;c.preventDefault(),r.current||(i=t.triggerRef.current)==null||i.focus()}),onPointerDownOutside:P(e.onPointerDownOutside,c=>{const i=c.detail.originalEvent,l=i.button===0&&i.ctrlKey===!0,u=i.button===2||l;r.current=u},{checkForDefaultPrevented:!1}),onFocusOutside:P(e.onFocusOutside,c=>c.preventDefault(),{checkForDefaultPrevented:!1})})})}),ne=p.forwardRef((e,n)=>{const t=d(v,e.__scopePopover),a=p.useRef(!1),o=p.useRef(!1);return s.jsx(M,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var c,i;(c=e.onCloseAutoFocus)==null||c.call(e,r),r.defaultPrevented||(a.current||(i=t.triggerRef.current)==null||i.focus(),r.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:r=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,r),r.defaultPrevented||(a.current=!0,r.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=r.target;((u=t.triggerRef.current)==null?void 0:u.contains(c))&&r.preventDefault(),r.detail.originalEvent.type==="focusin"&&o.current&&r.preventDefault()}})}),M=p.forwardRef((e,n)=>{const{__scopePopover:t,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:r,disableOutsidePointerEvents:c,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:h,...m}=e,f=d(v,t),C=g(t);return W(),s.jsx(Z,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:r,children:s.jsx(q,{asChild:!0,disableOutsidePointerEvents:c,onInteractOutside:h,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:u,onDismiss:()=>f.onOpenChange(!1),children:s.jsx(Q,{"data-state":I(f.open),role:"dialog",id:f.contentId,...C,...m,ref:n,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),T="PopoverClose",ae=p.forwardRef((e,n)=>{const{__scopePopover:t,...a}=e,o=d(T,t);return s.jsx(j.button,{type:"button",...a,ref:n,onClick:P(e.onClick,()=>o.onOpenChange(!1))})});ae.displayName=T;var se="PopoverArrow",k=p.forwardRef((e,n)=>{const{__scopePopover:t,...a}=e,o=g(t);return s.jsx(J,{...o,...a,ref:n})});k.displayName=se;function I(e){return e?"open":"closed"}var ce=E,ie=S,pe=y,le=D,ue=k;function me({...e}){return s.jsx(ce,{"data-slot":"popover",...e})}function xe({...e}){return s.jsx(ie,{"data-slot":"popover-trigger",...e})}function Ce({className:e,align:n="center",sideOffset:t=4,showArrow:a=!1,...o}){return s.jsx(pe,{children:s.jsxs(le,{"data-slot":"popover-content",align:n,sideOffset:t,className:L("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 rounded-md border p-4 shadow-md outline-hidden ",e),...o,children:[o.children,a&&s.jsx(ue,{className:"fill-popover -my-px drop-shadow-[0_1px_0_var(--border)]"})]})})}export{me as P,xe as a,Ce as b};
