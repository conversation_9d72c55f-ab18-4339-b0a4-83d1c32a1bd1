import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, X, Users, Search } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { useTRPCClient } from "@/utils/trpc";

type TrackContributor = {
  contributorId: string;
  role: string;
};

type Contributor = {
  id: string;
  name: string;
};

interface ContributorSelectorProps {
  contributors: TrackContributor[];
  onContributorsChange: (contributors: TrackContributor[]) => void;
}

// Common contributor roles
const CONTRIBUTOR_ROLES = [
  "Producer",
  "Songwriter",
  "Composer",
  "Lyricist",
  "<PERSON>rra<PERSON>",
  "Engineer",
  "Mixer",
  "Mastering Engineer",
  "Vocalist",
  "Backing Vocalist",
  "Guitarist",
  "Bassist",
  "Drummer",
  "Keyboardist",
  "Pianist",
  "Violinist",
  "Saxophonist",
  "Trumpeter",
  "DJ",
  "Remixer",
  "Co-Producer",
  "Executive Producer",
  "A&R",
  "Publisher",
  "Other",
];

export function ContributorSelector({ contributors, onContributorsChange }: ContributorSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [selectedContributorId, setSelectedContributorId] = useState<string>("");
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [customRole, setCustomRole] = useState<string>("");
  const trpcClient = useTRPCClient();

  // Fetch contributors for selection
  const { data: availableContributors, isLoading } = useQuery({
    queryKey: ["contributors", "select", searchQuery],
    queryFn: async () => {
      const response = await trpcClient.contributor.getAll.query({
        page: 1,
        limit: 50,
        search: searchQuery || undefined,
      });
      return response.contributors;
    },
    enabled: isOpen,
  });

  // Get contributor details for selected contributors
  const { data: selectedContributorDetails } = useQuery({
    queryKey: ["contributors", "details", contributors.map(c => c.contributorId)],
    queryFn: async () => {
      if (contributors.length === 0) return [];
      
      const contributorPromises = contributors.map(async (contributor) => {
        try {
          const response = await trpcClient.contributor.getById.query({ id: contributor.contributorId });
          return response;
        } catch (error) {
          console.error(`Failed to fetch contributor ${contributor.contributorId}:`, error);
          return null;
        }
      });
      
      const results = await Promise.all(contributorPromises);
      return results.filter((contributor): contributor is Contributor => contributor !== null);
    },
    enabled: contributors.length > 0,
  });

  const addContributor = () => {
    if (!selectedContributorId) {
      toast.error("Please select a contributor");
      return;
    }

    const role = selectedRole === "Other" ? customRole.trim() : selectedRole;
    if (!role) {
      toast.error("Please specify a role");
      return;
    }

    // Check if contributor with same role is already added
    if (contributors.some(c => c.contributorId === selectedContributorId && c.role === role)) {
      toast.error("This contributor with the same role is already added");
      return;
    }

    const newContributors = [...contributors, { contributorId: selectedContributorId, role }];
    onContributorsChange(newContributors);
    
    // Reset form
    setSelectedContributorId("");
    setSelectedRole("");
    setCustomRole("");
    setIsOpen(false);
    setSearchQuery("");
  };

  const removeContributor = (contributorId: string, role: string) => {
    const newContributors = contributors.filter(
      c => !(c.contributorId === contributorId && c.role === role)
    );
    onContributorsChange(newContributors);
  };

  const updateContributorRole = (contributorId: string, oldRole: string, newRole: string) => {
    const newContributors = contributors.map(c => 
      c.contributorId === contributorId && c.role === oldRole 
        ? { ...c, role: newRole } 
        : c
    );
    onContributorsChange(newContributors);
  };

  const getContributorName = (contributorId: string) => {
    const contributor = selectedContributorDetails?.find(c => c.id === contributorId);
    return contributor?.name || "Unknown Contributor";
  };

  return (
    <div className="space-y-4">
      {/* Add Contributor */}
      <div className="space-y-4">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="justify-start">
              <Plus className="mr-2 h-4 w-4" />
              Add Contributor
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-96 p-4" align="start">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Select Contributor</Label>
                <Command>
                  <CommandInput
                    placeholder="Search contributors..."
                    value={searchQuery}
                    onValueChange={setSearchQuery}
                  />
                  <CommandList className="max-h-32">
                    <CommandEmpty>
                      {isLoading ? "Loading..." : "No contributors found."}
                    </CommandEmpty>
                    <CommandGroup>
                      {availableContributors?.map((contributor) => (
                        <CommandItem
                          key={contributor.id}
                          onSelect={() => setSelectedContributorId(contributor.id)}
                          className={selectedContributorId === contributor.id ? "bg-accent" : ""}
                        >
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4" />
                            <span>{contributor.name}</span>
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </div>

              <div className="space-y-2">
                <Label>Role</Label>
                <Command>
                  <CommandInput
                    placeholder="Search roles..."
                    value={selectedRole === "Other" ? customRole : selectedRole}
                    onValueChange={(value) => {
                      if (CONTRIBUTOR_ROLES.includes(value)) {
                        setSelectedRole(value);
                        setCustomRole("");
                      } else {
                        setSelectedRole("Other");
                        setCustomRole(value);
                      }
                    }}
                  />
                  <CommandList className="max-h-32">
                    <CommandEmpty>Type to add custom role</CommandEmpty>
                    <CommandGroup>
                      {CONTRIBUTOR_ROLES
                        .filter(role => 
                          role.toLowerCase().includes(
                            (selectedRole === "Other" ? customRole : selectedRole).toLowerCase()
                          )
                        )
                        .map((role) => (
                          <CommandItem
                            key={role}
                            onSelect={() => {
                              setSelectedRole(role);
                              setCustomRole("");
                            }}
                            className={selectedRole === role ? "bg-accent" : ""}
                          >
                            {role}
                          </CommandItem>
                        ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </div>

              <Button 
                onClick={addContributor}
                disabled={!selectedContributorId || (!selectedRole && !customRole)}
                className="w-full"
              >
                Add Contributor
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Selected Contributors */}
      {contributors.length > 0 && (
        <div className="space-y-2">
          <Label>Selected Contributors ({contributors.length})</Label>
          <div className="space-y-2">
            {contributors.map((contributor, index) => (
              <Card key={`${contributor.contributorId}-${contributor.role}-${index}`}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-primary/10 rounded">
                        <Users className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{getContributorName(contributor.contributorId)}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {contributor.role}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeContributor(contributor.contributorId, contributor.role)}
                      className="text-destructive hover:text-destructive"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Validation Message */}
      {contributors.length === 0 && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Users className="h-4 w-4" />
          <span>At least one contributor is required</span>
        </div>
      )}

      {/* Role Summary */}
      {contributors.length > 0 && (
        <div className="text-xs text-muted-foreground">
          <p>Roles: {Array.from(new Set(contributors.map(c => c.role))).join(", ")}</p>
        </div>
      )}
    </div>
  );
}
