import{c as Xe,r as x,j as a,y as za,h as vi,z as fi,t as E,a as ye}from"./main-B9Fv5CdX.js";import{u as Oa}from"./useMutation-DGkS69KN.js";import{a as gi}from"./auth-client-C8WifPV2.js";import{c as hi,_ as G,X as Qe,j as Ra,k as Ta,l as Ia,m as _a,n as qa,o as La,p as Ma}from"./dialog-iGlJJq5Q.js";import{a as bi,B as q}from"./button-Ispz1G12.js";import{P as $a,L as F,I as K}from"./label-CNQvdrLZ.js";import{M as yi,C as Le,a as Me,b as $e,d as Ue,e as Ye,f as Ve,G as ji,T as wi}from"./genre-select-BveQTmBa.js";import{P as Ua,S as M,d as $,e as U,f as Y,g as b}from"./select-Cv6EF9My.js";import{C as Z,d as ee}from"./card-PyhbSuya.js";import{C as Ya}from"./circle-alert-DwqW4ucM.js";import{u as ve}from"./useQuery-CFAncLHa.js";import{P as Va,a as Ba,b as Ka}from"./popover-DzeimUGg.js";import{B as Ha}from"./badge-B7y-QlNI.js";import{U as ze}from"./user-DCDNZ7An.js";import{U as Oe}from"./users-uEpTONEC.js";import{C as ua}from"./circle-check-big-BA7hvIno.js";import{C as ki}from"./clock-BWWmp-9w.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ni=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]],Ci=Xe("File",Ni);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Di=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],on=Xe("Play",Di);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Si=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],Ai=Xe("Upload",Si);var Je="Progress",Ze=100,[Fi,rn]=hi(Je),[Ei,Pi]=Fi(Je),Ga=x.forwardRef((e,i)=>{const{__scopeProgress:t,value:n=null,max:r,getValueLabel:s=zi,...d}=e;(r||r===0)&&!xa(r)&&console.error(Oi(`${r}`,"Progress"));const l=xa(r)?r:Ze;n!==null&&!va(n,l)&&console.error(Ri(`${n}`,"Progress"));const f=va(n,l)?n:null,y=fe(f)?s(f,l):void 0;return a.jsx(Ei,{scope:t,value:f,max:l,children:a.jsx($a.div,{"aria-valuemax":l,"aria-valuemin":0,"aria-valuenow":fe(f)?f:void 0,"aria-valuetext":y,role:"progressbar","data-state":Qa(f,l),"data-value":f??void 0,"data-max":l,...d,ref:i})})});Ga.displayName=Je;var Wa="ProgressIndicator",Xa=x.forwardRef((e,i)=>{const{__scopeProgress:t,...n}=e,r=Pi(Wa,t);return a.jsx($a.div,{"data-state":Qa(r.value,r.max),"data-value":r.value??void 0,"data-max":r.max,...n,ref:i})});Xa.displayName=Wa;function zi(e,i){return`${Math.round(e/i*100)}%`}function Qa(e,i){return e==null?"indeterminate":e===i?"complete":"loading"}function fe(e){return typeof e=="number"}function xa(e){return fe(e)&&!isNaN(e)&&e>0}function va(e,i){return fe(e)&&!isNaN(e)&&e<=i&&e>=0}function Oi(e,i){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${i}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Ze}\`.`}function Ri(e,i){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${i}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Ze} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var Ti=Ga,Ii=Xa,Re={exports:{}},Te,fa;function _i(){if(fa)return Te;fa=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Te=e,Te}var Ie,ga;function qi(){if(ga)return Ie;ga=1;var e=_i();function i(){}function t(){}return t.resetWarningCache=i,Ie=function(){function n(d,l,f,y,N,v){if(v!==e){var D=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw D.name="Invariant Violation",D}}n.isRequired=n;function r(){return n}var s={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:r,element:n,elementType:n,instanceOf:r,node:n,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r,checkPropTypes:t,resetWarningCache:i};return s.PropTypes=s,s},Ie}var ha;function Li(){return ha||(ha=1,Re.exports=qi()()),Re.exports}var Mi=Li();const w=za(Mi),$i=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function ae(e,i,t){const n=Ui(e),{webkitRelativePath:r}=e,s=typeof i=="string"?i:typeof r=="string"&&r.length>0?r:`./${e.name}`;return typeof n.path!="string"&&ba(n,"path",s),ba(n,"relativePath",s),n}function Ui(e){const{name:i}=e;if(i&&i.lastIndexOf(".")!==-1&&!e.type){const n=i.split(".").pop().toLowerCase(),r=$i.get(n);r&&Object.defineProperty(e,"type",{value:r,writable:!1,configurable:!1,enumerable:!0})}return e}function ba(e,i,t){Object.defineProperty(e,i,{value:t,writable:!1,configurable:!1,enumerable:!0})}const Yi=[".DS_Store","Thumbs.db"];function Vi(e){return G(this,void 0,void 0,function*(){return ge(e)&&Bi(e.dataTransfer)?Wi(e.dataTransfer,e.type):Ki(e)?Hi(e):Array.isArray(e)&&e.every(i=>"getFile"in i&&typeof i.getFile=="function")?Gi(e):[]})}function Bi(e){return ge(e)}function Ki(e){return ge(e)&&ge(e.target)}function ge(e){return typeof e=="object"&&e!==null}function Hi(e){return Be(e.target.files).map(i=>ae(i))}function Gi(e){return G(this,void 0,void 0,function*(){return(yield Promise.all(e.map(t=>t.getFile()))).map(t=>ae(t))})}function Wi(e,i){return G(this,void 0,void 0,function*(){if(e.items){const t=Be(e.items).filter(r=>r.kind==="file");if(i!=="drop")return t;const n=yield Promise.all(t.map(Xi));return ya(Ja(n))}return ya(Be(e.files).map(t=>ae(t)))})}function ya(e){return e.filter(i=>Yi.indexOf(i.name)===-1)}function Be(e){if(e===null)return[];const i=[];for(let t=0;t<e.length;t++){const n=e[t];i.push(n)}return i}function Xi(e){if(typeof e.webkitGetAsEntry!="function")return ja(e);const i=e.webkitGetAsEntry();return i&&i.isDirectory?Za(i):ja(e,i)}function Ja(e){return e.reduce((i,t)=>[...i,...Array.isArray(t)?Ja(t):[t]],[])}function ja(e,i){return G(this,void 0,void 0,function*(){var t;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const s=yield e.getAsFileSystemHandle();if(s===null)throw new Error(`${e} is not a File`);if(s!==void 0){const d=yield s.getFile();return d.handle=s,ae(d)}}const n=e.getAsFile();if(!n)throw new Error(`${e} is not a File`);return ae(n,(t=i==null?void 0:i.fullPath)!==null&&t!==void 0?t:void 0)})}function Qi(e){return G(this,void 0,void 0,function*(){return e.isDirectory?Za(e):Ji(e)})}function Za(e){const i=e.createReader();return new Promise((t,n)=>{const r=[];function s(){i.readEntries(d=>G(this,void 0,void 0,function*(){if(d.length){const l=Promise.all(d.map(Qi));r.push(l),s()}else try{const l=yield Promise.all(r);t(l)}catch(l){n(l)}}),d=>{n(d)})}s()})}function Ji(e){return G(this,void 0,void 0,function*(){return new Promise((i,t)=>{e.file(n=>{const r=ae(n,e.fullPath);i(r)},n=>{t(n)})})})}var ue={},wa;function Zi(){return wa||(wa=1,ue.__esModule=!0,ue.default=function(e,i){if(e&&i){var t=Array.isArray(i)?i:i.split(",");if(t.length===0)return!0;var n=e.name||"",r=(e.type||"").toLowerCase(),s=r.replace(/\/.*$/,"");return t.some(function(d){var l=d.trim().toLowerCase();return l.charAt(0)==="."?n.toLowerCase().endsWith(l):l.endsWith("/*")?s===l.replace(/\/.*$/,""):r===l})}return!0}),ue}var et=Zi();const _e=za(et);function ka(e){return tt(e)||it(e)||ai(e)||at()}function at(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function it(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function tt(e){if(Array.isArray(e))return Ke(e)}function Na(e,i){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);i&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function Ca(e){for(var i=1;i<arguments.length;i++){var t=arguments[i]!=null?arguments[i]:{};i%2?Na(Object(t),!0).forEach(function(n){ei(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Na(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function ei(e,i,t){return i in e?Object.defineProperty(e,i,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[i]=t,e}function oe(e,i){return rt(e)||ot(e,i)||ai(e,i)||nt()}function nt(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ai(e,i){if(e){if(typeof e=="string")return Ke(e,i);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ke(e,i)}}function Ke(e,i){(i==null||i>e.length)&&(i=e.length);for(var t=0,n=new Array(i);t<i;t++)n[t]=e[t];return n}function ot(e,i){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],r=!0,s=!1,d,l;try{for(t=t.call(e);!(r=(d=t.next()).done)&&(n.push(d.value),!(i&&n.length===i));r=!0);}catch(f){s=!0,l=f}finally{try{!r&&t.return!=null&&t.return()}finally{if(s)throw l}}return n}}function rt(e){if(Array.isArray(e))return e}var lt=typeof _e=="function"?_e:_e.default,pt="file-invalid-type",st="file-too-large",ct="file-too-small",dt="too-many-files",mt=function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=i.split(","),n=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:pt,message:"File type must be ".concat(n)}},Da=function(i){return{code:st,message:"File is larger than ".concat(i," ").concat(i===1?"byte":"bytes")}},Sa=function(i){return{code:ct,message:"File is smaller than ".concat(i," ").concat(i===1?"byte":"bytes")}},ut={code:dt,message:"Too many files"};function ii(e,i){var t=e.type==="application/x-moz-file"||lt(e,i);return[t,t?null:mt(i)]}function ti(e,i,t){if(H(e.size))if(H(i)&&H(t)){if(e.size>t)return[!1,Da(t)];if(e.size<i)return[!1,Sa(i)]}else{if(H(i)&&e.size<i)return[!1,Sa(i)];if(H(t)&&e.size>t)return[!1,Da(t)]}return[!0,null]}function H(e){return e!=null}function xt(e){var i=e.files,t=e.accept,n=e.minSize,r=e.maxSize,s=e.multiple,d=e.maxFiles,l=e.validator;return!s&&i.length>1||s&&d>=1&&i.length>d?!1:i.every(function(f){var y=ii(f,t),N=oe(y,1),v=N[0],D=ti(f,n,r),z=oe(D,1),c=z[0],o=l?l(f):null;return v&&c&&!o})}function he(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function xe(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(i){return i==="Files"||i==="application/x-moz-file"}):!!e.target&&!!e.target.files}function Aa(e){e.preventDefault()}function vt(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function ft(e){return e.indexOf("Edge/")!==-1}function gt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return vt(e)||ft(e)}function L(){for(var e=arguments.length,i=new Array(e),t=0;t<e;t++)i[t]=arguments[t];return function(n){for(var r=arguments.length,s=new Array(r>1?r-1:0),d=1;d<r;d++)s[d-1]=arguments[d];return i.some(function(l){return!he(n)&&l&&l.apply(void 0,[n].concat(s)),he(n)})}}function ht(){return"showOpenFilePicker"in window}function bt(e){if(H(e)){var i=Object.entries(e).filter(function(t){var n=oe(t,2),r=n[0],s=n[1],d=!0;return ni(r)||(console.warn('Skipped "'.concat(r,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),d=!1),(!Array.isArray(s)||!s.every(oi))&&(console.warn('Skipped "'.concat(r,'" because an invalid file extension was provided.')),d=!1),d}).reduce(function(t,n){var r=oe(n,2),s=r[0],d=r[1];return Ca(Ca({},t),{},ei({},s,d))},{});return[{description:"Files",accept:i}]}return e}function yt(e){if(H(e))return Object.entries(e).reduce(function(i,t){var n=oe(t,2),r=n[0],s=n[1];return[].concat(ka(i),[r],ka(s))},[]).filter(function(i){return ni(i)||oi(i)}).join(",")}function jt(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function wt(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function ni(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function oi(e){return/^.*\.[\w]+$/.test(e)}var kt=["children"],Nt=["open"],Ct=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Dt=["refKey","onChange","onClick"];function St(e){return Et(e)||Ft(e)||ri(e)||At()}function At(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ft(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Et(e){if(Array.isArray(e))return He(e)}function qe(e,i){return Ot(e)||zt(e,i)||ri(e,i)||Pt()}function Pt(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ri(e,i){if(e){if(typeof e=="string")return He(e,i);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return He(e,i)}}function He(e,i){(i==null||i>e.length)&&(i=e.length);for(var t=0,n=new Array(i);t<i;t++)n[t]=e[t];return n}function zt(e,i){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],r=!0,s=!1,d,l;try{for(t=t.call(e);!(r=(d=t.next()).done)&&(n.push(d.value),!(i&&n.length===i));r=!0);}catch(f){s=!0,l=f}finally{try{!r&&t.return!=null&&t.return()}finally{if(s)throw l}}return n}}function Ot(e){if(Array.isArray(e))return e}function Fa(e,i){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);i&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function C(e){for(var i=1;i<arguments.length;i++){var t=arguments[i]!=null?arguments[i]:{};i%2?Fa(Object(t),!0).forEach(function(n){Ge(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Fa(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Ge(e,i,t){return i in e?Object.defineProperty(e,i,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[i]=t,e}function be(e,i){if(e==null)return{};var t=Rt(e,i),n,r;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],!(i.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(t[n]=e[n])}return t}function Rt(e,i){if(e==null)return{};var t={},n=Object.keys(e),r,s;for(s=0;s<n.length;s++)r=n[s],!(i.indexOf(r)>=0)&&(t[r]=e[r]);return t}var ea=x.forwardRef(function(e,i){var t=e.children,n=be(e,kt),r=pi(n),s=r.open,d=be(r,Nt);return x.useImperativeHandle(i,function(){return{open:s}},[s]),vi.createElement(x.Fragment,null,t(C(C({},d),{},{open:s})))});ea.displayName="Dropzone";var li={disabled:!1,getFilesFromEvent:Vi,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};ea.defaultProps=li;ea.propTypes={children:w.func,accept:w.objectOf(w.arrayOf(w.string)),multiple:w.bool,preventDropOnDocument:w.bool,noClick:w.bool,noKeyboard:w.bool,noDrag:w.bool,noDragEventsBubbling:w.bool,minSize:w.number,maxSize:w.number,maxFiles:w.number,disabled:w.bool,getFilesFromEvent:w.func,onFileDialogCancel:w.func,onFileDialogOpen:w.func,useFsAccessApi:w.bool,autoFocus:w.bool,onDragEnter:w.func,onDragLeave:w.func,onDragOver:w.func,onDrop:w.func,onDropAccepted:w.func,onDropRejected:w.func,onError:w.func,validator:w.func};var We={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function pi(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=C(C({},li),e),t=i.accept,n=i.disabled,r=i.getFilesFromEvent,s=i.maxSize,d=i.minSize,l=i.multiple,f=i.maxFiles,y=i.onDragEnter,N=i.onDragLeave,v=i.onDragOver,D=i.onDrop,z=i.onDropAccepted,c=i.onDropRejected,o=i.onFileDialogCancel,h=i.onFileDialogOpen,j=i.useFsAccessApi,S=i.autoFocus,u=i.preventDropOnDocument,g=i.noClick,k=i.noKeyboard,I=i.noDrag,V=i.noDragEventsBubbling,je=i.onError,ie=i.validator,te=x.useMemo(function(){return yt(t)},[t]),aa=x.useMemo(function(){return bt(t)},[t]),we=x.useMemo(function(){return typeof h=="function"?h:Ea},[h]),re=x.useMemo(function(){return typeof o=="function"?o:Ea},[o]),O=x.useRef(null),_=x.useRef(null),si=x.useReducer(Tt,We),ia=qe(si,2),ke=ia[0],R=ia[1],ci=ke.isFocused,ta=ke.isFileDialogActive,le=x.useRef(typeof window<"u"&&window.isSecureContext&&j&&ht()),na=function(){!le.current&&ta&&setTimeout(function(){if(_.current){var m=_.current.files;m.length||(R({type:"closeDialog"}),re())}},300)};x.useEffect(function(){return window.addEventListener("focus",na,!1),function(){window.removeEventListener("focus",na,!1)}},[_,ta,re,le]);var W=x.useRef([]),oa=function(m){O.current&&O.current.contains(m.target)||(m.preventDefault(),W.current=[])};x.useEffect(function(){return u&&(document.addEventListener("dragover",Aa,!1),document.addEventListener("drop",oa,!1)),function(){u&&(document.removeEventListener("dragover",Aa),document.removeEventListener("drop",oa))}},[O,u]),x.useEffect(function(){return!n&&S&&O.current&&O.current.focus(),function(){}},[O,S,n]);var B=x.useCallback(function(p){je?je(p):console.error(p)},[je]),ra=x.useCallback(function(p){p.preventDefault(),p.persist(),de(p),W.current=[].concat(St(W.current),[p.target]),xe(p)&&Promise.resolve(r(p)).then(function(m){if(!(he(p)&&!V)){var A=m.length,P=A>0&&xt({files:m,accept:te,minSize:d,maxSize:s,multiple:l,maxFiles:f,validator:ie}),T=A>0&&!P;R({isDragAccept:P,isDragReject:T,isDragActive:!0,type:"setDraggedFiles"}),y&&y(p)}}).catch(function(m){return B(m)})},[r,y,B,V,te,d,s,l,f,ie]),la=x.useCallback(function(p){p.preventDefault(),p.persist(),de(p);var m=xe(p);if(m&&p.dataTransfer)try{p.dataTransfer.dropEffect="copy"}catch{}return m&&v&&v(p),!1},[v,V]),pa=x.useCallback(function(p){p.preventDefault(),p.persist(),de(p);var m=W.current.filter(function(P){return O.current&&O.current.contains(P)}),A=m.indexOf(p.target);A!==-1&&m.splice(A,1),W.current=m,!(m.length>0)&&(R({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),xe(p)&&N&&N(p))},[O,N,V]),pe=x.useCallback(function(p,m){var A=[],P=[];p.forEach(function(T){var ne=ii(T,te),J=qe(ne,2),Ce=J[0],De=J[1],Se=ti(T,d,s),me=qe(Se,2),Ae=me[0],Fe=me[1],Ee=ie?ie(T):null;if(Ce&&Ae&&!Ee)A.push(T);else{var Pe=[De,Fe];Ee&&(Pe=Pe.concat(Ee)),P.push({file:T,errors:Pe.filter(function(xi){return xi})})}}),(!l&&A.length>1||l&&f>=1&&A.length>f)&&(A.forEach(function(T){P.push({file:T,errors:[ut]})}),A.splice(0)),R({acceptedFiles:A,fileRejections:P,isDragReject:P.length>0,type:"setFiles"}),D&&D(A,P,m),P.length>0&&c&&c(P,m),A.length>0&&z&&z(A,m)},[R,l,te,d,s,f,D,z,c,ie]),se=x.useCallback(function(p){p.preventDefault(),p.persist(),de(p),W.current=[],xe(p)&&Promise.resolve(r(p)).then(function(m){he(p)&&!V||pe(m,p)}).catch(function(m){return B(m)}),R({type:"reset"})},[r,pe,B,V]),X=x.useCallback(function(){if(le.current){R({type:"openDialog"}),we();var p={multiple:l,types:aa};window.showOpenFilePicker(p).then(function(m){return r(m)}).then(function(m){pe(m,null),R({type:"closeDialog"})}).catch(function(m){jt(m)?(re(m),R({type:"closeDialog"})):wt(m)?(le.current=!1,_.current?(_.current.value=null,_.current.click()):B(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):B(m)});return}_.current&&(R({type:"openDialog"}),we(),_.current.value=null,_.current.click())},[R,we,re,j,pe,B,aa,l]),sa=x.useCallback(function(p){!O.current||!O.current.isEqualNode(p.target)||(p.key===" "||p.key==="Enter"||p.keyCode===32||p.keyCode===13)&&(p.preventDefault(),X())},[O,X]),ca=x.useCallback(function(){R({type:"focus"})},[]),da=x.useCallback(function(){R({type:"blur"})},[]),ma=x.useCallback(function(){g||(gt()?setTimeout(X,0):X())},[g,X]),Q=function(m){return n?null:m},Ne=function(m){return k?null:Q(m)},ce=function(m){return I?null:Q(m)},de=function(m){V&&m.stopPropagation()},di=x.useMemo(function(){return function(){var p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},m=p.refKey,A=m===void 0?"ref":m,P=p.role,T=p.onKeyDown,ne=p.onFocus,J=p.onBlur,Ce=p.onClick,De=p.onDragEnter,Se=p.onDragOver,me=p.onDragLeave,Ae=p.onDrop,Fe=be(p,Ct);return C(C(Ge({onKeyDown:Ne(L(T,sa)),onFocus:Ne(L(ne,ca)),onBlur:Ne(L(J,da)),onClick:Q(L(Ce,ma)),onDragEnter:ce(L(De,ra)),onDragOver:ce(L(Se,la)),onDragLeave:ce(L(me,pa)),onDrop:ce(L(Ae,se)),role:typeof P=="string"&&P!==""?P:"presentation"},A,O),!n&&!k?{tabIndex:0}:{}),Fe)}},[O,sa,ca,da,ma,ra,la,pa,se,k,I,n]),mi=x.useCallback(function(p){p.stopPropagation()},[]),ui=x.useMemo(function(){return function(){var p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},m=p.refKey,A=m===void 0?"ref":m,P=p.onChange,T=p.onClick,ne=be(p,Dt),J=Ge({accept:te,multiple:l,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Q(L(P,se)),onClick:Q(L(T,mi)),tabIndex:-1},A,_);return C(C({},J),ne)}},[_,t,l,se,n]);return C(C({},ke),{},{isFocused:ci&&!n,getRootProps:di,getInputProps:ui,rootRef:O,inputRef:_,open:Q(X)})}function Tt(e,i){switch(i.type){case"focus":return C(C({},e),{},{isFocused:!0});case"blur":return C(C({},e),{},{isFocused:!1});case"openDialog":return C(C({},We),{},{isFileDialogActive:!0});case"closeDialog":return C(C({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return C(C({},e),{},{isDragActive:i.isDragActive,isDragAccept:i.isDragAccept,isDragReject:i.isDragReject});case"setFiles":return C(C({},e),{},{acceptedFiles:i.acceptedFiles,fileRejections:i.fileRejections,isDragReject:i.isDragReject});case"reset":return C({},We);default:return e}}function Ea(){}function It({className:e,value:i,...t}){return a.jsx(Ti,{"data-slot":"progress",className:bi("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...t,children:a.jsx(Ii,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(i||0)}%)`}})})}function _t({files:e,onFilesChange:i}){const[t,n]=x.useState(!1),[r,s]=x.useState(0),{edgestore:d}=fi(),l=x.useCallback(async c=>{if(c.length!==0){n(!0),s(0);try{const o=c.map(async S=>{if(!S.type.startsWith("audio/"))return E.error(`${S.name} is not a valid audio file`),null;const u=200*1024*1024;if(S.size>u)return E.error(`${S.name} is too large. Maximum size is 200MB.`),null;try{const g=await d.audio.upload({file:S,onProgressChange:I=>{s(I)}});let k;try{k=await qt(S)}catch(I){console.warn("Could not get audio duration:",I)}return{fileUrl:g.url,fileKey:g.url.split("/").pop()||"",fileName:S.name,fileSize:S.size,mimeType:S.type,duration:k}}catch(g){return console.error("Upload failed:",g),E.error(`Failed to upload ${S.name}`),null}}),j=(await Promise.all(o)).filter(S=>S!==null);j.length>0&&(i([...e,...j]),E.success(`Successfully uploaded ${j.length} file(s)`))}catch(o){console.error("Upload error:",o),E.error("Failed to upload files")}finally{n(!1),s(0)}}},[e,i,d]),{getRootProps:f,getInputProps:y,isDragActive:N}=pi({onDrop:l,accept:{"audio/*":[".mp3",".wav",".flac",".aac",".ogg",".m4a"]},multiple:!0,disabled:t}),v=c=>{const o=e.filter((h,j)=>j!==c);i(o)},D=c=>{if(c===0)return"0 B";const o=1024,h=["B","KB","MB","GB"],j=Math.floor(Math.log(c)/Math.log(o));return parseFloat((c/Math.pow(o,j)).toFixed(1))+" "+h[j]},z=c=>{const o=Math.floor(c/60),h=Math.floor(c%60);return`${o}:${h.toString().padStart(2,"0")}`};return a.jsxs("div",{className:"space-y-4",children:[a.jsx(Z,{children:a.jsxs(ee,{className:"p-6",children:[a.jsxs("div",{...f(),className:`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${N?"border-primary bg-primary/5":"border-muted-foreground/25"}
              ${t?"pointer-events-none opacity-50":"hover:border-primary hover:bg-primary/5"}
            `,children:[a.jsx("input",{...y()}),a.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[a.jsx("div",{className:"p-4 bg-muted rounded-full",children:a.jsx(Ai,{className:"h-8 w-8 text-muted-foreground"})}),a.jsxs("div",{className:"space-y-2",children:[a.jsx("h3",{className:"text-lg font-medium",children:N?"Drop audio files here":"Upload audio files"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Drag and drop audio files here, or click to browse"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Supported formats: MP3, WAV, FLAC, AAC, OGG, M4A (Max 200MB each)"})]})]})]}),t&&a.jsxs("div",{className:"mt-4 space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between text-sm",children:[a.jsx("span",{children:"Uploading..."}),a.jsxs("span",{children:[r,"%"]})]}),a.jsx(It,{value:r,className:"w-full"})]})]})}),e.length>0&&a.jsxs("div",{className:"space-y-2",children:[a.jsxs("h4",{className:"text-sm font-medium",children:["Uploaded Files (",e.length,")"]}),a.jsx("div",{className:"space-y-2",children:e.map((c,o)=>a.jsx(Z,{children:a.jsx(ee,{className:"p-4",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-primary/10 rounded",children:a.jsx(yi,{className:"h-4 w-4 text-primary"})}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-medium truncate",children:c.fileName||"Unknown file"}),a.jsxs("div",{className:"flex items-center space-x-4 text-xs text-muted-foreground",children:[c.fileSize&&a.jsx("span",{children:D(c.fileSize)}),c.duration&&a.jsx("span",{children:z(c.duration)}),c.mimeType&&a.jsx("span",{className:"uppercase",children:c.mimeType.split("/")[1]})]})]})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(q,{variant:"ghost",size:"sm",onClick:()=>window.open(c.fileUrl,"_blank"),children:a.jsx(Ci,{className:"h-4 w-4"})}),a.jsx(q,{variant:"ghost",size:"sm",onClick:()=>v(o),className:"text-destructive hover:text-destructive",children:a.jsx(Qe,{className:"h-4 w-4"})})]})]})})},o))})]}),e.length===0&&a.jsxs("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[a.jsx(Ya,{className:"h-4 w-4"}),a.jsx("span",{children:"At least one audio file is required"})]})]})}function qt(e){return new Promise((i,t)=>{const n=new Audio,r=URL.createObjectURL(e);n.addEventListener("loadedmetadata",()=>{URL.revokeObjectURL(r),i(n.duration)}),n.addEventListener("error",()=>{URL.revokeObjectURL(r),t(new Error("Failed to load audio metadata"))}),n.src=r})}function Lt({artists:e,onArtistsChange:i}){const[t,n]=x.useState(""),[r,s]=x.useState(!1),d=ye(),{data:l,isLoading:f}=ve({queryKey:["artists","select",t],queryFn:async()=>(await d.artist.getAll.query({page:1,limit:50,search:t||void 0})).artists,enabled:r}),{data:y}=ve({queryKey:["artists","details",e.map(c=>c.artistId)],queryFn:async()=>{if(e.length===0)return[];const c=e.map(async h=>{try{return await d.artist.getById.query({id:h.artistId})}catch(j){return console.error(`Failed to fetch artist ${h.artistId}:`,j),null}});return(await Promise.all(c)).filter(h=>h!==null)},enabled:e.length>0}),N=(c,o="PRIMARY")=>{if(e.some(j=>j.artistId===c)){E.error("Artist is already added to this track");return}const h=[...e,{artistId:c,role:o}];i(h),s(!1),n("")},v=c=>{const o=e.filter(h=>h.artistId!==c);i(o)},D=(c,o)=>{const h=e.map(j=>j.artistId===c?{...j,role:o}:j);i(h)},z=c=>{const o=y==null?void 0:y.find(h=>h.id===c);return(o==null?void 0:o.name)||"Unknown Artist"};return a.jsxs("div",{className:"space-y-4",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsxs(Va,{open:r,onOpenChange:s,children:[a.jsx(Ba,{asChild:!0,children:a.jsxs(q,{variant:"outline",className:"justify-start",children:[a.jsx(Ua,{className:"mr-2 h-4 w-4"}),"Add Artist"]})}),a.jsx(Ka,{className:"w-80 p-0",align:"start",children:a.jsxs(Le,{children:[a.jsx(Me,{placeholder:"Search artists...",value:t,onValueChange:n}),a.jsxs($e,{children:[a.jsx(Ue,{children:f?"Loading...":"No artists found."}),a.jsx(Ye,{children:l==null?void 0:l.map(c=>a.jsx(Ve,{onSelect:()=>N(c.id),className:"flex items-center justify-between",children:a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(ze,{className:"h-4 w-4"}),a.jsxs("div",{children:[a.jsx("p",{className:"font-medium",children:c.name}),c.genre&&a.jsx("p",{className:"text-xs text-muted-foreground",children:c.genre})]})]})},c.id))})]})]})})]})}),e.length>0&&a.jsxs("div",{className:"space-y-2",children:[a.jsxs(F,{children:["Selected Artists (",e.length,")"]}),a.jsx("div",{className:"space-y-2",children:e.map((c,o)=>a.jsx(Z,{children:a.jsx(ee,{className:"p-4",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-primary/10 rounded",children:a.jsx(ze,{className:"h-4 w-4 text-primary"})}),a.jsxs("div",{className:"flex-1",children:[a.jsx("p",{className:"font-medium",children:z(c.artistId)}),a.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx(F,{htmlFor:`role-${c.artistId}`,className:"text-xs",children:"Role:"}),a.jsxs(M,{value:c.role,onValueChange:h=>D(c.artistId,h),children:[a.jsx($,{className:"h-7 w-32",children:a.jsx(U,{})}),a.jsxs(Y,{children:[a.jsx(b,{value:"PRIMARY",children:"Primary"}),a.jsx(b,{value:"FEATURING",children:"Featuring"})]})]})]})]})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Ha,{variant:c.role==="PRIMARY"?"default":"secondary",className:"text-xs",children:c.role==="PRIMARY"?"Primary":"Featuring"}),a.jsx(q,{variant:"ghost",size:"sm",onClick:()=>v(c.artistId),className:"text-destructive hover:text-destructive",children:a.jsx(Qe,{className:"h-4 w-4"})})]})]})})},`${c.artistId}-${o}`))})]}),e.length===0&&a.jsxs("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[a.jsx(ze,{className:"h-4 w-4"}),a.jsx("span",{children:"At least one artist is required"})]}),e.length>0&&a.jsx("div",{className:"text-xs text-muted-foreground",children:a.jsxs("p",{children:["Primary: ",e.filter(c=>c.role==="PRIMARY").length," | Featuring: ",e.filter(c=>c.role==="FEATURING").length]})})]})}const Pa=["Producer","Songwriter","Composer","Lyricist","Arranger","Engineer","Mixer","Mastering Engineer","Vocalist","Backing Vocalist","Guitarist","Bassist","Drummer","Keyboardist","Pianist","Violinist","Saxophonist","Trumpeter","DJ","Remixer","Co-Producer","Executive Producer","A&R","Publisher","Other"];function Mt({contributors:e,onContributorsChange:i}){const[t,n]=x.useState(""),[r,s]=x.useState(!1),[d,l]=x.useState(""),[f,y]=x.useState(""),[N,v]=x.useState(""),D=ye(),{data:z,isLoading:c}=ve({queryKey:["contributors","select",t],queryFn:async()=>(await D.contributor.getAll.query({page:1,limit:50,search:t||void 0})).contributors,enabled:r}),{data:o}=ve({queryKey:["contributors","details",e.map(u=>u.contributorId)],queryFn:async()=>{if(e.length===0)return[];const u=e.map(async k=>{try{return await D.contributor.getById.query({id:k.contributorId})}catch(I){return console.error(`Failed to fetch contributor ${k.contributorId}:`,I),null}});return(await Promise.all(u)).filter(k=>k!==null)},enabled:e.length>0}),h=()=>{if(!d){E.error("Please select a contributor");return}const u=f==="Other"?N.trim():f;if(!u){E.error("Please specify a role");return}if(e.some(k=>k.contributorId===d&&k.role===u)){E.error("This contributor with the same role is already added");return}const g=[...e,{contributorId:d,role:u}];i(g),l(""),y(""),v(""),s(!1),n("")},j=(u,g)=>{const k=e.filter(I=>!(I.contributorId===u&&I.role===g));i(k)},S=u=>{const g=o==null?void 0:o.find(k=>(k==null?void 0:k.id)===u);return(g==null?void 0:g.name)||"Unknown Contributor"};return a.jsxs("div",{className:"space-y-4",children:[a.jsx("div",{className:"space-y-4",children:a.jsxs(Va,{open:r,onOpenChange:s,children:[a.jsx(Ba,{asChild:!0,children:a.jsxs(q,{variant:"outline",className:"justify-start",children:[a.jsx(Ua,{className:"mr-2 h-4 w-4"}),"Add Contributor"]})}),a.jsx(Ka,{className:"w-96 p-4",align:"start",children:a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{children:"Select Contributor"}),a.jsxs(Le,{children:[a.jsx(Me,{placeholder:"Search contributors...",value:t,onValueChange:n}),a.jsxs($e,{className:"max-h-32",children:[a.jsx(Ue,{children:c?"Loading...":"No contributors found."}),a.jsx(Ye,{children:z==null?void 0:z.map(u=>a.jsx(Ve,{onSelect:()=>l(u.id),className:d===u.id?"bg-accent":"",children:a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Oe,{className:"h-4 w-4"}),a.jsx("span",{children:u.name})]})},u.id))})]})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{children:"Role"}),a.jsxs(Le,{children:[a.jsx(Me,{placeholder:"Search roles...",value:f==="Other"?N:f,onValueChange:u=>{Pa.includes(u)?(y(u),v("")):(y("Other"),v(u))}}),a.jsxs($e,{className:"max-h-32",children:[a.jsx(Ue,{children:"Type to add custom role"}),a.jsx(Ye,{children:Pa.filter(u=>u.toLowerCase().includes((f==="Other"?N:f).toLowerCase())).map(u=>a.jsx(Ve,{onSelect:()=>{y(u),v("")},className:f===u?"bg-accent":"",children:u},u))})]})]})]}),a.jsx(q,{onClick:h,disabled:!d||!f&&!N,className:"w-full",children:"Add Contributor"})]})})]})}),e.length>0&&a.jsxs("div",{className:"space-y-2",children:[a.jsxs(F,{children:["Selected Contributors (",e.length,")"]}),a.jsx("div",{className:"space-y-2",children:e.map((u,g)=>a.jsx(Z,{children:a.jsx(ee,{className:"p-4",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-primary/10 rounded",children:a.jsx(Oe,{className:"h-4 w-4 text-primary"})}),a.jsxs("div",{className:"flex-1",children:[a.jsx("p",{className:"font-medium",children:S(u.contributorId)}),a.jsx("div",{className:"flex items-center space-x-2 mt-1",children:a.jsx(Ha,{variant:"outline",className:"text-xs",children:u.role})})]})]}),a.jsx(q,{variant:"ghost",size:"sm",onClick:()=>j(u.contributorId,u.role),className:"text-destructive hover:text-destructive",children:a.jsx(Qe,{className:"h-4 w-4"})})]})})},`${u.contributorId}-${u.role}-${g}`))})]}),e.length===0&&a.jsxs("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[a.jsx(Oe,{className:"h-4 w-4"}),a.jsx("span",{children:"At least one contributor is required"})]}),e.length>0&&a.jsx("div",{className:"text-xs text-muted-foreground",children:a.jsxs("p",{children:["Roles:"," ",Array.from(new Set(e.map(u=>u.role))).join(", ")]})})]})}function ln({children:e,track:i,onTrackUpdated:t}){var c;const[n,r]=x.useState(!1),s=ye(),{data:d}=gi.useSession();(c=d==null?void 0:d.user)==null||c.role;const[l,f]=x.useState({title:"",isrc:"",trackVersion:"",recordingYear:new Date().getFullYear(),publishingYear:new Date().getFullYear(),publishingHolder:"",genre:"",subGenre:"",lyrics:"",previewStart:"",previewLength:"",metadataLanguage:"en",explicit:"NOT_EXPLICIT",audioLanguage:"en",rightsClaim:"NO_CLAIM",status:"DRAFT",trackFiles:[],artists:[],contributors:[]});x.useEffect(()=>{n&&i&&f({title:i.title||"",isrc:i.isrc||"",trackVersion:i.trackVersion||"",recordingYear:i.recordingYear,publishingYear:i.publishingYear,publishingHolder:i.publishingHolder||"",genre:i.genre||"",subGenre:i.subGenre||"",lyrics:i.lyrics||"",previewStart:i.previewStart||"",previewLength:i.previewLength||"",metadataLanguage:i.metadataLanguage||"en",explicit:i.explicit,audioLanguage:i.audioLanguage||"en",rightsClaim:i.rightsClaim,status:i.status,trackFiles:i.trackFiles.map(o=>({fileUrl:o.fileUrl,fileKey:o.fileKey,fileName:o.fileName,fileSize:o.fileSize,mimeType:o.mimeType,duration:o.duration})),artists:i.artists.map(o=>({artistId:o.artist.id,role:o.role})),contributors:i.contributors.map(o=>({contributorId:o.contributor.id,role:o.role}))})},[n,i]);const y=Oa({mutationFn:async o=>s.track.update.mutate({id:i.id,...o}),onSuccess:()=>{E.success("Track updated successfully"),r(!1),t()},onError:o=>{console.error("Failed to update track:",o),E.error("Failed to update track: "+(o.message||"Unknown error"))}}),N=async o=>{if(o.preventDefault(),!l.title.trim()){E.error("Track title is required");return}if(!l.genre){E.error("Genre is required");return}if(!l.publishingHolder.trim()){E.error("Publishing holder is required");return}if(l.trackFiles.length===0){E.error("At least one track file is required");return}if(l.artists.length===0){E.error("At least one artist is required");return}if(l.contributors.length===0){E.error("At least one contributor is required");return}try{await y.mutateAsync(l)}catch{}},v=(o,h)=>{f(j=>({...j,[o]:h}))},D=new Date().getFullYear(),z=Array.from({length:D-1900+2},(o,h)=>D+1-h);return a.jsxs(Ra,{open:n,onOpenChange:r,children:[a.jsx(Ta,{asChild:!0,children:e}),a.jsxs(Ia,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:[a.jsxs(_a,{children:[a.jsx(qa,{children:"Edit Track"}),a.jsx(La,{children:"Update track information and metadata."})]}),a.jsxs("form",{onSubmit:N,className:"space-y-6",children:[a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Basic Information"}),a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"title",children:"Title *"}),a.jsx(K,{id:"title",value:l.title,onChange:o=>v("title",o.target.value),placeholder:"Enter track title"})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"isrc",children:"ISRC"}),a.jsx(K,{id:"isrc",value:l.isrc,onChange:o=>v("isrc",o.target.value),placeholder:"CC-XXX-YY-NNNNN"})]})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"genre",children:"Genre *"}),a.jsx(ji,{value:l.genre,onValueChange:o=>v("genre",o),placeholder:"Select genre"})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"subGenre",children:"Sub-genre"}),a.jsx(K,{id:"subGenre",value:l.subGenre,onChange:o=>v("subGenre",o.target.value),placeholder:"Enter sub-genre"})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"trackVersion",children:"Track Version"}),a.jsx(K,{id:"trackVersion",value:l.trackVersion,onChange:o=>v("trackVersion",o.target.value),placeholder:"e.g., Radio Edit, Extended Mix"})]})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Years & Publishing"}),a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"recordingYear",children:"Recording Year *"}),a.jsxs(M,{value:l.recordingYear.toString(),onValueChange:o=>v("recordingYear",parseInt(o)),children:[a.jsx($,{children:a.jsx(U,{placeholder:"Select year"})}),a.jsx(Y,{children:z.map(o=>a.jsx(b,{value:o.toString(),children:o},o))})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"publishingYear",children:"Publishing Year *"}),a.jsxs(M,{value:l.publishingYear.toString(),onValueChange:o=>v("publishingYear",parseInt(o)),children:[a.jsx($,{children:a.jsx(U,{placeholder:"Select year"})}),a.jsx(Y,{children:z.map(o=>a.jsx(b,{value:o.toString(),children:o},o))})]})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"publishingHolder",children:"Publishing Holder *"}),a.jsx(K,{id:"publishingHolder",value:l.publishingHolder,onChange:o=>v("publishingHolder",o.target.value),placeholder:"Enter publishing holder name"})]})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Content & Language"}),a.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"explicit",children:"Explicit Content"}),a.jsxs(M,{value:l.explicit,onValueChange:o=>v("explicit",o),children:[a.jsx($,{children:a.jsx(U,{})}),a.jsxs(Y,{children:[a.jsx(b,{value:"NOT_EXPLICIT",children:"Not Explicit"}),a.jsx(b,{value:"EXPLICIT",children:"Explicit"}),a.jsx(b,{value:"CLEAN",children:"Clean"})]})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"metadataLanguage",children:"Metadata Language"}),a.jsxs(M,{value:l.metadataLanguage,onValueChange:o=>v("metadataLanguage",o),children:[a.jsx($,{children:a.jsx(U,{})}),a.jsxs(Y,{children:[a.jsx(b,{value:"en",children:"English"}),a.jsx(b,{value:"es",children:"Spanish"}),a.jsx(b,{value:"fr",children:"French"}),a.jsx(b,{value:"de",children:"German"}),a.jsx(b,{value:"it",children:"Italian"}),a.jsx(b,{value:"pt",children:"Portuguese"}),a.jsx(b,{value:"ja",children:"Japanese"}),a.jsx(b,{value:"ko",children:"Korean"}),a.jsx(b,{value:"zh",children:"Chinese"})]})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"audioLanguage",children:"Audio Language"}),a.jsxs(M,{value:l.audioLanguage,onValueChange:o=>v("audioLanguage",o),children:[a.jsx($,{children:a.jsx(U,{})}),a.jsxs(Y,{children:[a.jsx(b,{value:"en",children:"English"}),a.jsx(b,{value:"es",children:"Spanish"}),a.jsx(b,{value:"fr",children:"French"}),a.jsx(b,{value:"de",children:"German"}),a.jsx(b,{value:"it",children:"Italian"}),a.jsx(b,{value:"pt",children:"Portuguese"}),a.jsx(b,{value:"ja",children:"Japanese"}),a.jsx(b,{value:"ko",children:"Korean"}),a.jsx(b,{value:"zh",children:"Chinese"})]})]})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"rightsClaim",children:"Rights Claim"}),a.jsxs(M,{value:l.rightsClaim,onValueChange:o=>v("rightsClaim",o),children:[a.jsx($,{children:a.jsx(U,{})}),a.jsxs(Y,{children:[a.jsx(b,{value:"NO_CLAIM",children:"No Claim"}),a.jsx(b,{value:"REPORT",children:"Report"}),a.jsx(b,{value:"MONETIZE",children:"Monetize"}),a.jsx(b,{value:"BLOCK",children:"Block"})]})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"lyrics",children:"Lyrics"}),a.jsx(wi,{id:"lyrics",value:l.lyrics,onChange:o=>v("lyrics",o.target.value),placeholder:"Enter track lyrics (optional)",rows:4})]})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Preview Settings"}),a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"previewStart",children:"Preview Start (MM:SS)"}),a.jsx(K,{id:"previewStart",value:l.previewStart,onChange:o=>v("previewStart",o.target.value),placeholder:"1:30"})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(F,{htmlFor:"previewLength",children:"Preview Length (MM:SS)"}),a.jsx(K,{id:"previewLength",value:l.previewLength,onChange:o=>v("previewLength",o.target.value),placeholder:"0:30"})]})]})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Track Files *"}),a.jsx(_t,{files:l.trackFiles,onFilesChange:o=>v("trackFiles",o)})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Artists *"}),a.jsx(Lt,{artists:l.artists,onArtistsChange:o=>v("artists",o)})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Contributors *"}),a.jsx(Mt,{contributors:l.contributors,onContributorsChange:o=>v("contributors",o)})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Status"}),a.jsxs(M,{value:l.status,onValueChange:o=>v("status",o),children:[a.jsx($,{children:a.jsx(U,{})}),a.jsxs(Y,{children:[a.jsx(b,{value:"DRAFT",children:"Draft"}),a.jsx(b,{value:"READY",children:"Ready"})]})]})]}),a.jsxs(Ma,{children:[a.jsx(q,{type:"button",variant:"outline",onClick:()=>r(!1),children:"Cancel"}),a.jsx(q,{type:"submit",disabled:y.isPending,children:y.isPending?"Updating...":"Update Track"})]})]})]})]})}function pn({children:e,track:i,onStatusUpdated:t}){var h,j,S,u;const[n,r]=x.useState(!1),s=ye(),d=Oa({mutationFn:async g=>s.track.updateStatus.mutate({id:i.id,status:g}),onSuccess:(g,k)=>{E.success(`Track status updated to ${k.toLowerCase()}`),r(!1),t()},onError:g=>{console.error("Failed to update track status:",g),E.error("Failed to update status: "+(g.message||"Unknown error"))}}),l=g=>{d.mutate(g)},f=g=>{switch(g){case"DRAFT":return{icon:a.jsx(ki,{className:"h-5 w-5 text-yellow-500"}),label:"Draft",description:"Track is in draft mode and not ready for distribution",color:"bg-yellow-50 border-yellow-200",textColor:"text-yellow-800"};case"READY":return{icon:a.jsx(ua,{className:"h-5 w-5 text-green-500"}),label:"Ready",description:"Track is ready for distribution and release",color:"bg-green-50 border-green-200",textColor:"text-green-800"}}},y=f(i.status),N=i.status==="DRAFT"?"READY":"DRAFT",v=f(N),D=[{label:"Track title",valid:!!((h=i.title)!=null&&h.trim()),required:!0},{label:"Genre",valid:!!((j=i.genre)!=null&&j.trim()),required:!0},{label:"Publishing holder",valid:!!((S=i.publishingHolder)!=null&&S.trim()),required:!0},{label:"Track files",valid:i.trackFiles.length>0,required:!0},{label:"Artists",valid:i.artists.length>0,required:!0},{label:"Contributors",valid:i.contributors.length>0,required:!0},{label:"ISRC",valid:!!((u=i.isrc)!=null&&u.trim()),required:!1},{label:"Preview settings",valid:!!i.previewStart&&!!i.previewLength,required:!1}],o=D.filter(g=>g.required).every(g=>g.valid);return a.jsxs(Ra,{open:n,onOpenChange:r,children:[a.jsx(Ta,{asChild:!0,children:e}),a.jsxs(Ia,{className:"sm:max-w-[500px]",children:[a.jsxs(_a,{children:[a.jsx(qa,{children:"Update Track Status"}),a.jsxs(La,{children:['Change the status of "',i.title,'" to control its availability for distribution.']})]}),a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Current Status"}),a.jsx(Z,{className:y.color,children:a.jsx(ee,{className:"p-4",children:a.jsxs("div",{className:"flex items-center space-x-3",children:[y.icon,a.jsxs("div",{children:[a.jsx("p",{className:`font-medium ${y.textColor}`,children:y.label}),a.jsx("p",{className:`text-sm ${y.textColor}`,children:y.description})]})]})})})]}),(i.submittedAt||i.readyAt)&&a.jsxs("div",{className:"space-y-2",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Status History"}),a.jsxs("div",{className:"text-sm text-muted-foreground space-y-1",children:[i.submittedAt&&a.jsxs("p",{children:["Submitted: ",new Date(i.submittedAt).toLocaleString()]}),i.readyAt&&a.jsxs("p",{children:["Ready: ",new Date(i.readyAt).toLocaleString()]})]})]}),i.status==="DRAFT"&&a.jsxs("div",{className:"space-y-2",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Readiness Checklist"}),a.jsx("div",{className:"space-y-2",children:D.map((g,k)=>a.jsxs("div",{className:"flex items-center space-x-2",children:[g.valid?a.jsx(ua,{className:"h-4 w-4 text-green-500"}):a.jsx(Ya,{className:"h-4 w-4 text-red-500"}),a.jsxs("span",{className:`text-sm ${g.valid?"text-green-700":"text-red-700"}`,children:[g.label,g.required&&" *"]})]},k))}),!o&&a.jsx("div",{className:"mt-2 p-3 bg-red-50 border border-red-200 rounded-md",children:a.jsx("p",{className:"text-sm text-red-800",children:"Please complete all required fields before setting track to Ready."})})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Change Status"}),a.jsx(Z,{className:v.color,children:a.jsx(ee,{className:"p-4",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-3",children:[v.icon,a.jsxs("div",{children:[a.jsxs("p",{className:`font-medium ${v.textColor}`,children:["Set to ",v.label]}),a.jsx("p",{className:`text-sm ${v.textColor}`,children:v.description})]})]}),a.jsx(q,{onClick:()=>l(N),disabled:d.isPending||N==="READY"&&!o,variant:N==="READY"?"default":"outline",children:d.isPending?"Updating...":`Set ${v.label}`})]})})})]})]}),a.jsx(Ma,{children:a.jsx(q,{variant:"outline",onClick:()=>r(!1),children:"Cancel"})})]})]})}export{Lt as A,Mt as C,on as P,_t as T,ln as U,pn as a};
