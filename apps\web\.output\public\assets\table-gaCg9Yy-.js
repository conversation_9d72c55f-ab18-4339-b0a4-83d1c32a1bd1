import{c as O,r as P,j as w}from"./main-B9Fv5CdX.js";import{u as de,c as Ne,a as x,d as ce}from"./button-Ispz1G12.js";import{r as fe,R as je,s as Te,W as Be,c as ke,C as qe,b as Ue,T as Xe,h as Ke,g as pe,i as We,O as Ye}from"./dialog-iGlJJq5Q.js";import{S as Je,a as ue}from"./scroll-area-BkIMWkxZ.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qe=[["path",{d:"m17 18-6-6 6-6",key:"1yerx2"}],["path",{d:"M7 6v12",key:"1p53r6"}]],Rn=O("ChevronFirst",Qe);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ze=[["path",{d:"m7 18 6-6-6-6",key:"lwmzdw"}],["path",{d:"M17 6v12",key:"1o0aio"}]],wn=O("ChevronLast",Ze);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const be=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],hn=O("ChevronLeft",be);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],_n=O("Columns3",et);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tt=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]],Fn=O("Ellipsis",tt);var me="AlertDialog",[nt,xn]=ke(me,[fe]),D=fe(),Se=e=>{const{__scopeAlertDialog:o,...t}=e,n=D(o);return w.jsx(je,{...n,...t,modal:!0})};Se.displayName=me;var ot="AlertDialogTrigger",Ce=P.forwardRef((e,o)=>{const{__scopeAlertDialog:t,...n}=e,r=D(t);return w.jsx(Te,{...r,...n,ref:o})});Ce.displayName=ot;var rt="AlertDialogPortal",ve=e=>{const{__scopeAlertDialog:o,...t}=e,n=D(o);return w.jsx(We,{...n,...t})};ve.displayName=rt;var it="AlertDialogOverlay",Re=P.forwardRef((e,o)=>{const{__scopeAlertDialog:t,...n}=e,r=D(t);return w.jsx(Ye,{...r,...n,ref:o})});Re.displayName=it;var H="AlertDialogContent",[lt,st]=nt(H),ut=Ne("AlertDialogContent"),we=P.forwardRef((e,o)=>{const{__scopeAlertDialog:t,children:n,...r}=e,i=D(t),l=P.useRef(null),u=de(o,l),a=P.useRef(null);return w.jsx(Be,{contentName:H,titleName:he,docsSlug:"alert-dialog",children:w.jsx(lt,{scope:t,cancelRef:a,children:w.jsxs(qe,{role:"alertdialog",...i,...r,ref:u,onOpenAutoFocus:Ue(r.onOpenAutoFocus,g=>{var c;g.preventDefault(),(c=a.current)==null||c.focus({preventScroll:!0})}),onPointerDownOutside:g=>g.preventDefault(),onInteractOutside:g=>g.preventDefault(),children:[w.jsx(ut,{children:n}),w.jsx(gt,{contentRef:l})]})})})});we.displayName=H;var he="AlertDialogTitle",_e=P.forwardRef((e,o)=>{const{__scopeAlertDialog:t,...n}=e,r=D(t);return w.jsx(Xe,{...r,...n,ref:o})});_e.displayName=he;var Fe="AlertDialogDescription",xe=P.forwardRef((e,o)=>{const{__scopeAlertDialog:t,...n}=e,r=D(t);return w.jsx(Ke,{...r,...n,ref:o})});xe.displayName=Fe;var at="AlertDialogAction",$e=P.forwardRef((e,o)=>{const{__scopeAlertDialog:t,...n}=e,r=D(t);return w.jsx(pe,{...r,...n,ref:o})});$e.displayName=at;var Pe="AlertDialogCancel",Me=P.forwardRef((e,o)=>{const{__scopeAlertDialog:t,...n}=e,{cancelRef:r}=st(Pe,t),i=D(t),l=de(o,r);return w.jsx(pe,{...i,...n,ref:l})});Me.displayName=Pe;var gt=({contentRef:e})=>{const o=`\`${H}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${H}\` by passing a \`${Fe}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${H}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return P.useEffect(()=>{var n;document.getElementById((n=e.current)==null?void 0:n.getAttribute("aria-describedby"))||console.warn(o)},[o,e]),null},dt=Se,ct=Ce,ft=ve,pt=Re,mt=we,St=$e,Ct=Me,vt=_e,Rt=xe;/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function A(e,o){return typeof e=="function"?e(o):e}function M(e,o){return t=>{o.setState(n=>({...n,[e]:A(t,n[e])}))}}function B(e){return e instanceof Function}function wt(e){return Array.isArray(e)&&e.every(o=>typeof o=="number")}function ht(e,o){const t=[],n=r=>{r.forEach(i=>{t.push(i);const l=o(i);l!=null&&l.length&&n(l)})};return n(e),t}function S(e,o,t){let n=[],r;return i=>{let l;t.key&&t.debug&&(l=Date.now());const u=e(i);if(!(u.length!==n.length||u.some((c,m)=>n[m]!==c)))return r;n=u;let g;if(t.key&&t.debug&&(g=Date.now()),r=o(...u),t==null||t.onChange==null||t.onChange(r),t.key&&t.debug&&t!=null&&t.debug()){const c=Math.round((Date.now()-l)*100)/100,m=Math.round((Date.now()-g)*100)/100,d=m/16,s=(f,p)=>{for(f=String(f);f.length<p;)f=" "+f;return f};console.info(`%c⏱ ${s(m,5)} /${s(c,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,t==null?void 0:t.key)}return r}}function C(e,o,t,n){return{debug:()=>{var r;return(r=e==null?void 0:e.debugAll)!=null?r:e[o]},key:!1,onChange:n}}function _t(e,o,t,n){const r=()=>{var l;return(l=i.getValue())!=null?l:e.options.renderFallbackValue},i={id:`${o.id}_${t.id}`,row:o,column:t,getValue:()=>o.getValue(n),renderValue:r,getContext:S(()=>[e,t,o,i],(l,u,a,g)=>({table:l,column:u,row:a,cell:g,getValue:g.getValue,renderValue:g.renderValue}),C(e.options,"debugCells"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(i,t,o,e)},{}),i}function Ft(e,o,t,n){var r,i;const u={...e._getDefaultColumnDef(),...o},a=u.accessorKey;let g=(r=(i=u.id)!=null?i:a?typeof String.prototype.replaceAll=="function"?a.replaceAll(".","_"):a.replace(/\./g,"_"):void 0)!=null?r:typeof u.header=="string"?u.header:void 0,c;if(u.accessorFn?c=u.accessorFn:a&&(a.includes(".")?c=d=>{let s=d;for(const p of a.split(".")){var f;s=(f=s)==null?void 0:f[p]}return s}:c=d=>d[u.accessorKey]),!g)throw new Error;let m={id:`${String(g)}`,accessorFn:c,parent:n,depth:t,columnDef:u,columns:[],getFlatColumns:S(()=>[!0],()=>{var d;return[m,...(d=m.columns)==null?void 0:d.flatMap(s=>s.getFlatColumns())]},C(e.options,"debugColumns")),getLeafColumns:S(()=>[e._getOrderColumnsFn()],d=>{var s;if((s=m.columns)!=null&&s.length){let f=m.columns.flatMap(p=>p.getLeafColumns());return d(f)}return[m]},C(e.options,"debugColumns"))};for(const d of e._features)d.createColumn==null||d.createColumn(m,e);return m}const F="debugHeaders";function ae(e,o,t){var n;let i={id:(n=t.id)!=null?n:o.id,column:o,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],u=a=>{a.subHeaders&&a.subHeaders.length&&a.subHeaders.map(u),l.push(a)};return u(i),l},getContext:()=>({table:e,header:i,column:o})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(i,e)}),i}const xt={createTable:e=>{e.getHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,r)=>{var i,l;const u=(i=n==null?void 0:n.map(m=>t.find(d=>d.id===m)).filter(Boolean))!=null?i:[],a=(l=r==null?void 0:r.map(m=>t.find(d=>d.id===m)).filter(Boolean))!=null?l:[],g=t.filter(m=>!(n!=null&&n.includes(m.id))&&!(r!=null&&r.includes(m.id)));return N(o,[...u,...g,...a],e)},C(e.options,F)),e.getCenterHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,r)=>(t=t.filter(i=>!(n!=null&&n.includes(i.id))&&!(r!=null&&r.includes(i.id))),N(o,t,e,"center")),C(e.options,F)),e.getLeftHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(o,t,n)=>{var r;const i=(r=n==null?void 0:n.map(l=>t.find(u=>u.id===l)).filter(Boolean))!=null?r:[];return N(o,i,e,"left")},C(e.options,F)),e.getRightHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(o,t,n)=>{var r;const i=(r=n==null?void 0:n.map(l=>t.find(u=>u.id===l)).filter(Boolean))!=null?r:[];return N(o,i,e,"right")},C(e.options,F)),e.getFooterGroups=S(()=>[e.getHeaderGroups()],o=>[...o].reverse(),C(e.options,F)),e.getLeftFooterGroups=S(()=>[e.getLeftHeaderGroups()],o=>[...o].reverse(),C(e.options,F)),e.getCenterFooterGroups=S(()=>[e.getCenterHeaderGroups()],o=>[...o].reverse(),C(e.options,F)),e.getRightFooterGroups=S(()=>[e.getRightHeaderGroups()],o=>[...o].reverse(),C(e.options,F)),e.getFlatHeaders=S(()=>[e.getHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,F)),e.getLeftFlatHeaders=S(()=>[e.getLeftHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,F)),e.getCenterFlatHeaders=S(()=>[e.getCenterHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,F)),e.getRightFlatHeaders=S(()=>[e.getRightHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,F)),e.getCenterLeafHeaders=S(()=>[e.getCenterFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,F)),e.getLeftLeafHeaders=S(()=>[e.getLeftFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,F)),e.getRightLeafHeaders=S(()=>[e.getRightFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,F)),e.getLeafHeaders=S(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(o,t,n)=>{var r,i,l,u,a,g;return[...(r=(i=o[0])==null?void 0:i.headers)!=null?r:[],...(l=(u=t[0])==null?void 0:u.headers)!=null?l:[],...(a=(g=n[0])==null?void 0:g.headers)!=null?a:[]].map(c=>c.getLeafHeaders()).flat()},C(e.options,F))}};function N(e,o,t,n){var r,i;let l=0;const u=function(d,s){s===void 0&&(s=1),l=Math.max(l,s),d.filter(f=>f.getIsVisible()).forEach(f=>{var p;(p=f.columns)!=null&&p.length&&u(f.columns,s+1)},0)};u(e);let a=[];const g=(d,s)=>{const f={depth:s,id:[n,`${s}`].filter(Boolean).join("_"),headers:[]},p=[];d.forEach(v=>{const R=[...p].reverse()[0],h=v.column.depth===f.depth;let _,y=!1;if(h&&v.column.parent?_=v.column.parent:(_=v.column,y=!0),R&&(R==null?void 0:R.column)===_)R.subHeaders.push(v);else{const $=ae(t,_,{id:[n,s,_.id,v==null?void 0:v.id].filter(Boolean).join("_"),isPlaceholder:y,placeholderId:y?`${p.filter(G=>G.column===_).length}`:void 0,depth:s,index:p.length});$.subHeaders.push(v),p.push($)}f.headers.push(v),v.headerGroup=f}),a.push(f),s>0&&g(p,s-1)},c=o.map((d,s)=>ae(t,d,{depth:l,index:s}));g(c,l-1),a.reverse();const m=d=>d.filter(f=>f.column.getIsVisible()).map(f=>{let p=0,v=0,R=[0];f.subHeaders&&f.subHeaders.length?(R=[],m(f.subHeaders).forEach(_=>{let{colSpan:y,rowSpan:$}=_;p+=y,R.push($)})):p=1;const h=Math.min(...R);return v=v+h,f.colSpan=p,f.rowSpan=v,{colSpan:p,rowSpan:v}});return m((r=(i=a[0])==null?void 0:i.headers)!=null?r:[]),a}const te=(e,o,t,n,r,i,l)=>{let u={id:o,index:n,original:t,depth:r,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:a=>{if(u._valuesCache.hasOwnProperty(a))return u._valuesCache[a];const g=e.getColumn(a);if(g!=null&&g.accessorFn)return u._valuesCache[a]=g.accessorFn(u.original,n),u._valuesCache[a]},getUniqueValues:a=>{if(u._uniqueValuesCache.hasOwnProperty(a))return u._uniqueValuesCache[a];const g=e.getColumn(a);if(g!=null&&g.accessorFn)return g.columnDef.getUniqueValues?(u._uniqueValuesCache[a]=g.columnDef.getUniqueValues(u.original,n),u._uniqueValuesCache[a]):(u._uniqueValuesCache[a]=[u.getValue(a)],u._uniqueValuesCache[a])},renderValue:a=>{var g;return(g=u.getValue(a))!=null?g:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>ht(u.subRows,a=>a.subRows),getParentRow:()=>u.parentId?e.getRow(u.parentId,!0):void 0,getParentRows:()=>{let a=[],g=u;for(;;){const c=g.getParentRow();if(!c)break;a.push(c),g=c}return a.reverse()},getAllCells:S(()=>[e.getAllLeafColumns()],a=>a.map(g=>_t(e,u,g,g.id)),C(e.options,"debugRows")),_getAllCellsByColumnId:S(()=>[u.getAllCells()],a=>a.reduce((g,c)=>(g[c.column.id]=c,g),{}),C(e.options,"debugRows"))};for(let a=0;a<e._features.length;a++){const g=e._features[a];g==null||g.createRow==null||g.createRow(u,e)}return u},$t={createColumn:(e,o)=>{e._getFacetedRowModel=o.options.getFacetedRowModel&&o.options.getFacetedRowModel(o,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():o.getPreFilteredRowModel(),e._getFacetedUniqueValues=o.options.getFacetedUniqueValues&&o.options.getFacetedUniqueValues(o,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=o.options.getFacetedMinMaxValues&&o.options.getFacetedMinMaxValues(o,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Ve=(e,o,t)=>{var n,r;const i=t==null||(n=t.toString())==null?void 0:n.toLowerCase();return!!(!((r=e.getValue(o))==null||(r=r.toString())==null||(r=r.toLowerCase())==null)&&r.includes(i))};Ve.autoRemove=e=>V(e);const ye=(e,o,t)=>{var n;return!!(!((n=e.getValue(o))==null||(n=n.toString())==null)&&n.includes(t))};ye.autoRemove=e=>V(e);const Ie=(e,o,t)=>{var n;return((n=e.getValue(o))==null||(n=n.toString())==null?void 0:n.toLowerCase())===(t==null?void 0:t.toLowerCase())};Ie.autoRemove=e=>V(e);const De=(e,o,t)=>{var n;return(n=e.getValue(o))==null?void 0:n.includes(t)};De.autoRemove=e=>V(e);const Ae=(e,o,t)=>!t.some(n=>{var r;return!((r=e.getValue(o))!=null&&r.includes(n))});Ae.autoRemove=e=>V(e)||!(e!=null&&e.length);const Ee=(e,o,t)=>t.some(n=>{var r;return(r=e.getValue(o))==null?void 0:r.includes(n)});Ee.autoRemove=e=>V(e)||!(e!=null&&e.length);const Ge=(e,o,t)=>e.getValue(o)===t;Ge.autoRemove=e=>V(e);const He=(e,o,t)=>e.getValue(o)==t;He.autoRemove=e=>V(e);const ne=(e,o,t)=>{let[n,r]=t;const i=e.getValue(o);return i>=n&&i<=r};ne.resolveFilterValue=e=>{let[o,t]=e,n=typeof o!="number"?parseFloat(o):o,r=typeof t!="number"?parseFloat(t):t,i=o===null||Number.isNaN(n)?-1/0:n,l=t===null||Number.isNaN(r)?1/0:r;if(i>l){const u=i;i=l,l=u}return[i,l]};ne.autoRemove=e=>V(e)||V(e[0])&&V(e[1]);const I={includesString:Ve,includesStringSensitive:ye,equalsString:Ie,arrIncludes:De,arrIncludesAll:Ae,arrIncludesSome:Ee,equals:Ge,weakEquals:He,inNumberRange:ne};function V(e){return e==null||e===""}const Pt={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:M("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,o)=>{e.getAutoFilterFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t==null?void 0:t.getValue(e.id);return typeof n=="string"?I.includesString:typeof n=="number"?I.inNumberRange:typeof n=="boolean"||n!==null&&typeof n=="object"?I.equals:Array.isArray(n)?I.arrIncludes:I.weakEquals},e.getFilterFn=()=>{var t,n;return B(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(n=o.options.filterFns)==null?void 0:n[e.columnDef.filterFn])!=null?t:I[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,n,r;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((n=o.options.enableColumnFilters)!=null?n:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=o.getState().columnFilters)==null||(t=t.find(n=>n.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,n;return(t=(n=o.getState().columnFilters)==null?void 0:n.findIndex(r=>r.id===e.id))!=null?t:-1},e.setFilterValue=t=>{o.setColumnFilters(n=>{const r=e.getFilterFn(),i=n==null?void 0:n.find(c=>c.id===e.id),l=A(t,i?i.value:void 0);if(ge(r,l,e)){var u;return(u=n==null?void 0:n.filter(c=>c.id!==e.id))!=null?u:[]}const a={id:e.id,value:l};if(i){var g;return(g=n==null?void 0:n.map(c=>c.id===e.id?a:c))!=null?g:[]}return n!=null&&n.length?[...n,a]:[a]})}},createRow:(e,o)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=o=>{const t=e.getAllLeafColumns(),n=r=>{var i;return(i=A(o,r))==null?void 0:i.filter(l=>{const u=t.find(a=>a.id===l.id);if(u){const a=u.getFilterFn();if(ge(a,l.value,u))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(n)},e.resetColumnFilters=o=>{var t,n;e.setColumnFilters(o?[]:(t=(n=e.initialState)==null?void 0:n.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function ge(e,o,t){return(e&&e.autoRemove?e.autoRemove(o,t):!1)||typeof o>"u"||typeof o=="string"&&!o}const Mt=(e,o,t)=>t.reduce((n,r)=>{const i=r.getValue(e);return n+(typeof i=="number"?i:0)},0),Vt=(e,o,t)=>{let n;return t.forEach(r=>{const i=r.getValue(e);i!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}),n},yt=(e,o,t)=>{let n;return t.forEach(r=>{const i=r.getValue(e);i!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}),n},It=(e,o,t)=>{let n,r;return t.forEach(i=>{const l=i.getValue(e);l!=null&&(n===void 0?l>=l&&(n=r=l):(n>l&&(n=l),r<l&&(r=l)))}),[n,r]},Dt=(e,o)=>{let t=0,n=0;if(o.forEach(r=>{let i=r.getValue(e);i!=null&&(i=+i)>=i&&(++t,n+=i)}),t)return n/t},At=(e,o)=>{if(!o.length)return;const t=o.map(i=>i.getValue(e));if(!wt(t))return;if(t.length===1)return t[0];const n=Math.floor(t.length/2),r=t.sort((i,l)=>i-l);return t.length%2!==0?r[n]:(r[n-1]+r[n])/2},Et=(e,o)=>Array.from(new Set(o.map(t=>t.getValue(e))).values()),Gt=(e,o)=>new Set(o.map(t=>t.getValue(e))).size,Ht=(e,o)=>o.length,k={sum:Mt,min:Vt,max:yt,extent:It,mean:Dt,median:At,unique:Et,uniqueCount:Gt,count:Ht},Lt={getDefaultColumnDef:()=>({aggregatedCell:e=>{var o,t;return(o=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?o:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:M("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,o)=>{e.toggleGrouping=()=>{o.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(n=>n!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,n;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((n=o.options.enableGrouping)!=null?n:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t==null?void 0:t.getValue(e.id);if(typeof n=="number")return k.sum;if(Object.prototype.toString.call(n)==="[object Date]")return k.extent},e.getAggregationFn=()=>{var t,n;if(!e)throw new Error;return B(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(n=o.options.aggregationFns)==null?void 0:n[e.columnDef.aggregationFn])!=null?t:k[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=o=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(o),e.resetGrouping=o=>{var t,n;e.setGrouping(o?[]:(t=(n=e.initialState)==null?void 0:n.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,o)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const n=o.getColumn(t);return n!=null&&n.columnDef.getGroupingValue?(e._groupingValuesCache[t]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,o,t,n)=>{e.getIsGrouped=()=>o.getIsGrouped()&&o.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&o.getIsGrouped(),e.getIsAggregated=()=>{var r;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((r=t.subRows)!=null&&r.length)}}};function zt(e,o,t){if(!(o!=null&&o.length)||!t)return e;const n=e.filter(i=>!o.includes(i.id));return t==="remove"?n:[...o.map(i=>e.find(l=>l.id===i)).filter(Boolean),...n]}const Ot={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:M("columnOrder",e)}),createColumn:(e,o)=>{e.getIndex=S(t=>[z(o,t)],t=>t.findIndex(n=>n.id===e.id),C(o.options,"debugColumns")),e.getIsFirstColumn=t=>{var n;return((n=z(o,t)[0])==null?void 0:n.id)===e.id},e.getIsLastColumn=t=>{var n;const r=z(o,t);return((n=r[r.length-1])==null?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=o=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(o),e.resetColumnOrder=o=>{var t;e.setColumnOrder(o?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=S(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(o,t,n)=>r=>{let i=[];if(!(o!=null&&o.length))i=r;else{const l=[...o],u=[...r];for(;u.length&&l.length;){const a=l.shift(),g=u.findIndex(c=>c.id===a);g>-1&&i.push(u.splice(g,1)[0])}i=[...i,...u]}return zt(i,t,n)},C(e.options,"debugTable"))}},q=()=>({left:[],right:[]}),Nt={getInitialState:e=>({columnPinning:q(),...e}),getDefaultOptions:e=>({onColumnPinningChange:M("columnPinning",e)}),createColumn:(e,o)=>{e.pin=t=>{const n=e.getLeafColumns().map(r=>r.id).filter(Boolean);o.setColumnPinning(r=>{var i,l;if(t==="right"){var u,a;return{left:((u=r==null?void 0:r.left)!=null?u:[]).filter(m=>!(n!=null&&n.includes(m))),right:[...((a=r==null?void 0:r.right)!=null?a:[]).filter(m=>!(n!=null&&n.includes(m))),...n]}}if(t==="left"){var g,c;return{left:[...((g=r==null?void 0:r.left)!=null?g:[]).filter(m=>!(n!=null&&n.includes(m))),...n],right:((c=r==null?void 0:r.right)!=null?c:[]).filter(m=>!(n!=null&&n.includes(m)))}}return{left:((i=r==null?void 0:r.left)!=null?i:[]).filter(m=>!(n!=null&&n.includes(m))),right:((l=r==null?void 0:r.right)!=null?l:[]).filter(m=>!(n!=null&&n.includes(m)))}})},e.getCanPin=()=>e.getLeafColumns().some(n=>{var r,i,l;return((r=n.columnDef.enablePinning)!=null?r:!0)&&((i=(l=o.options.enableColumnPinning)!=null?l:o.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(u=>u.id),{left:n,right:r}=o.getState().columnPinning,i=t.some(u=>n==null?void 0:n.includes(u)),l=t.some(u=>r==null?void 0:r.includes(u));return i?"left":l?"right":!1},e.getPinnedIndex=()=>{var t,n;const r=e.getIsPinned();return r?(t=(n=o.getState().columnPinning)==null||(n=n[r])==null?void 0:n.indexOf(e.id))!=null?t:-1:0}},createRow:(e,o)=>{e.getCenterVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left,o.getState().columnPinning.right],(t,n,r)=>{const i=[...n??[],...r??[]];return t.filter(l=>!i.includes(l.column.id))},C(o.options,"debugRows")),e.getLeftVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left],(t,n)=>(n??[]).map(i=>t.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),C(o.options,"debugRows")),e.getRightVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.right],(t,n)=>(n??[]).map(i=>t.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),C(o.options,"debugRows"))},createTable:e=>{e.setColumnPinning=o=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(o),e.resetColumnPinning=o=>{var t,n;return e.setColumnPinning(o?q():(t=(n=e.initialState)==null?void 0:n.columnPinning)!=null?t:q())},e.getIsSomeColumnsPinned=o=>{var t;const n=e.getState().columnPinning;if(!o){var r,i;return!!((r=n.left)!=null&&r.length||(i=n.right)!=null&&i.length)}return!!((t=n[o])!=null&&t.length)},e.getLeftLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(o,t)=>(t??[]).map(n=>o.find(r=>r.id===n)).filter(Boolean),C(e.options,"debugColumns")),e.getRightLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(o,t)=>(t??[]).map(n=>o.find(r=>r.id===n)).filter(Boolean),C(e.options,"debugColumns")),e.getCenterLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n)=>{const r=[...t??[],...n??[]];return o.filter(i=>!r.includes(i.id))},C(e.options,"debugColumns"))}};function jt(e){return e||(typeof document<"u"?document:null)}const j={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},U=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Tt={getDefaultColumnDef:()=>j,getInitialState:e=>({columnSizing:{},columnSizingInfo:U(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:M("columnSizing",e),onColumnSizingInfoChange:M("columnSizingInfo",e)}),createColumn:(e,o)=>{e.getSize=()=>{var t,n,r;const i=o.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:j.minSize,(n=i??e.columnDef.size)!=null?n:j.size),(r=e.columnDef.maxSize)!=null?r:j.maxSize)},e.getStart=S(t=>[t,z(o,t),o.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((r,i)=>r+i.getSize(),0),C(o.options,"debugColumns")),e.getAfter=S(t=>[t,z(o,t),o.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((r,i)=>r+i.getSize(),0),C(o.options,"debugColumns")),e.resetSize=()=>{o.setColumnSizing(t=>{let{[e.id]:n,...r}=t;return r})},e.getCanResize=()=>{var t,n;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((n=o.options.enableColumnResizing)!=null?n:!0)},e.getIsResizing=()=>o.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,o)=>{e.getSize=()=>{let t=0;const n=r=>{if(r.subHeaders.length)r.subHeaders.forEach(n);else{var i;t+=(i=r.column.getSize())!=null?i:0}};return n(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const n=o.getColumn(e.column.id),r=n==null?void 0:n.getCanResize();return i=>{if(!n||!r||(i.persist==null||i.persist(),X(i)&&i.touches&&i.touches.length>1))return;const l=e.getSize(),u=e?e.getLeafHeaders().map(R=>[R.column.id,R.column.getSize()]):[[n.id,n.getSize()]],a=X(i)?Math.round(i.touches[0].clientX):i.clientX,g={},c=(R,h)=>{typeof h=="number"&&(o.setColumnSizingInfo(_=>{var y,$;const G=o.options.columnResizeDirection==="rtl"?-1:1,ie=(h-((y=_==null?void 0:_.startOffset)!=null?y:0))*G,le=Math.max(ie/(($=_==null?void 0:_.startSize)!=null?$:0),-.999999);return _.columnSizingStart.forEach(ze=>{let[Oe,se]=ze;g[Oe]=Math.round(Math.max(se+se*le,0)*100)/100}),{..._,deltaOffset:ie,deltaPercentage:le}}),(o.options.columnResizeMode==="onChange"||R==="end")&&o.setColumnSizing(_=>({..._,...g})))},m=R=>c("move",R),d=R=>{c("end",R),o.setColumnSizingInfo(h=>({...h,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},s=jt(t),f={moveHandler:R=>m(R.clientX),upHandler:R=>{s==null||s.removeEventListener("mousemove",f.moveHandler),s==null||s.removeEventListener("mouseup",f.upHandler),d(R.clientX)}},p={moveHandler:R=>(R.cancelable&&(R.preventDefault(),R.stopPropagation()),m(R.touches[0].clientX),!1),upHandler:R=>{var h;s==null||s.removeEventListener("touchmove",p.moveHandler),s==null||s.removeEventListener("touchend",p.upHandler),R.cancelable&&(R.preventDefault(),R.stopPropagation()),d((h=R.touches[0])==null?void 0:h.clientX)}},v=Bt()?{passive:!1}:!1;X(i)?(s==null||s.addEventListener("touchmove",p.moveHandler,v),s==null||s.addEventListener("touchend",p.upHandler,v)):(s==null||s.addEventListener("mousemove",f.moveHandler,v),s==null||s.addEventListener("mouseup",f.upHandler,v)),o.setColumnSizingInfo(R=>({...R,startOffset:a,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:u,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=o=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(o),e.setColumnSizingInfo=o=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(o),e.resetColumnSizing=o=>{var t;e.setColumnSizing(o?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=o=>{var t;e.setColumnSizingInfo(o?U():(t=e.initialState.columnSizingInfo)!=null?t:U())},e.getTotalSize=()=>{var o,t;return(o=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getLeftTotalSize=()=>{var o,t;return(o=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getCenterTotalSize=()=>{var o,t;return(o=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getRightTotalSize=()=>{var o,t;return(o=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0}}};let T=null;function Bt(){if(typeof T=="boolean")return T;let e=!1;try{const o={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,o),window.removeEventListener("test",t)}catch{e=!1}return T=e,T}function X(e){return e.type==="touchstart"}const kt={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:M("columnVisibility",e)}),createColumn:(e,o)=>{e.toggleVisibility=t=>{e.getCanHide()&&o.setColumnVisibility(n=>({...n,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,n;const r=e.columns;return(t=r.length?r.some(i=>i.getIsVisible()):(n=o.getState().columnVisibility)==null?void 0:n[e.id])!=null?t:!0},e.getCanHide=()=>{var t,n;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((n=o.options.enableHiding)!=null?n:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,o)=>{e._getAllVisibleCells=S(()=>[e.getAllCells(),o.getState().columnVisibility],t=>t.filter(n=>n.column.getIsVisible()),C(o.options,"debugRows")),e.getVisibleCells=S(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,n,r)=>[...t,...n,...r],C(o.options,"debugRows"))},createTable:e=>{const o=(t,n)=>S(()=>[n(),n().filter(r=>r.getIsVisible()).map(r=>r.id).join("_")],r=>r.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),C(e.options,"debugColumns"));e.getVisibleFlatColumns=o("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=o("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=o("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=o("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=o("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:(n=e.initialState.columnVisibility)!=null?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=(n=t)!=null?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((r,i)=>({...r,[i.id]:t||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible((n=t.target)==null?void 0:n.checked)}}};function z(e,o){return o?o==="center"?e.getCenterVisibleLeafColumns():o==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const qt={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},Ut={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:M("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:o=>{var t;const n=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[o.id])==null?void 0:t.getValue();return typeof n=="string"||typeof n=="number"}}),createColumn:(e,o)=>{e.getCanGlobalFilter=()=>{var t,n,r,i;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((n=o.options.enableGlobalFilter)!=null?n:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&((i=o.options.getColumnCanGlobalFilter==null?void 0:o.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>I.includesString,e.getGlobalFilterFn=()=>{var o,t;const{globalFilterFn:n}=e.options;return B(n)?n:n==="auto"?e.getGlobalAutoFilterFn():(o=(t=e.options.filterFns)==null?void 0:t[n])!=null?o:I[n]},e.setGlobalFilter=o=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(o)},e.resetGlobalFilter=o=>{e.setGlobalFilter(o?void 0:e.initialState.globalFilter)}}},Xt={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:M("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let o=!1,t=!1;e._autoResetExpanded=()=>{var n,r;if(!o){e._queue(()=>{o=!0});return}if((n=(r=e.options.autoResetAll)!=null?r:e.options.autoResetExpanded)!=null?n:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=n=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(n),e.toggleAllRowsExpanded=n=>{n??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=n=>{var r,i;e.setExpanded(n?{}:(r=(i=e.initialState)==null?void 0:i.expanded)!=null?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(n=>n.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>n=>{n.persist==null||n.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const n=e.getState().expanded;return n===!0||Object.values(n).some(Boolean)},e.getIsAllRowsExpanded=()=>{const n=e.getState().expanded;return typeof n=="boolean"?n===!0:!(!Object.keys(n).length||e.getRowModel().flatRows.some(r=>!r.getIsExpanded()))},e.getExpandedDepth=()=>{let n=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const l=i.split(".");n=Math.max(n,l.length)}),n},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,o)=>{e.toggleExpanded=t=>{o.setExpanded(n=>{var r;const i=n===!0?!0:!!(n!=null&&n[e.id]);let l={};if(n===!0?Object.keys(o.getRowModel().rowsById).forEach(u=>{l[u]=!0}):l=n,t=(r=t)!=null?r:!i,!i&&t)return{...l,[e.id]:!0};if(i&&!t){const{[e.id]:u,...a}=l;return a}return n})},e.getIsExpanded=()=>{var t;const n=o.getState().expanded;return!!((t=o.options.getIsRowExpanded==null?void 0:o.options.getIsRowExpanded(e))!=null?t:n===!0||n!=null&&n[e.id])},e.getCanExpand=()=>{var t,n,r;return(t=o.options.getRowCanExpand==null?void 0:o.options.getRowCanExpand(e))!=null?t:((n=o.options.enableExpanding)!=null?n:!0)&&!!((r=e.subRows)!=null&&r.length)},e.getIsAllParentsExpanded=()=>{let t=!0,n=e;for(;t&&n.parentId;)n=o.getRow(n.parentId,!0),t=n.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},J=0,Q=10,K=()=>({pageIndex:J,pageSize:Q}),Kt={getInitialState:e=>({...e,pagination:{...K(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:M("pagination",e)}),createTable:e=>{let o=!1,t=!1;e._autoResetPageIndex=()=>{var n,r;if(!o){e._queue(()=>{o=!0});return}if((n=(r=e.options.autoResetAll)!=null?r:e.options.autoResetPageIndex)!=null?n:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=n=>{const r=i=>A(n,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(r)},e.resetPagination=n=>{var r;e.setPagination(n?K():(r=e.initialState.pagination)!=null?r:K())},e.setPageIndex=n=>{e.setPagination(r=>{let i=A(n,r.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,l)),{...r,pageIndex:i}})},e.resetPageIndex=n=>{var r,i;e.setPageIndex(n?J:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?r:J)},e.resetPageSize=n=>{var r,i;e.setPageSize(n?Q:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?r:Q)},e.setPageSize=n=>{e.setPagination(r=>{const i=Math.max(1,A(n,r.pageSize)),l=r.pageSize*r.pageIndex,u=Math.floor(l/i);return{...r,pageIndex:u,pageSize:i}})},e.setPageCount=n=>e.setPagination(r=>{var i;let l=A(n,(i=e.options.pageCount)!=null?i:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...r,pageCount:l}}),e.getPageOptions=S(()=>[e.getPageCount()],n=>{let r=[];return n&&n>0&&(r=[...new Array(n)].fill(null).map((i,l)=>l)),r},C(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:n}=e.getState().pagination,r=e.getPageCount();return r===-1?!0:r===0?!1:n<r-1},e.previousPage=()=>e.setPageIndex(n=>n-1),e.nextPage=()=>e.setPageIndex(n=>n+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var n;return(n=e.options.pageCount)!=null?n:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var n;return(n=e.options.rowCount)!=null?n:e.getPrePaginationRowModel().rows.length}}},W=()=>({top:[],bottom:[]}),Wt={getInitialState:e=>({rowPinning:W(),...e}),getDefaultOptions:e=>({onRowPinningChange:M("rowPinning",e)}),createRow:(e,o)=>{e.pin=(t,n,r)=>{const i=n?e.getLeafRows().map(a=>{let{id:g}=a;return g}):[],l=r?e.getParentRows().map(a=>{let{id:g}=a;return g}):[],u=new Set([...l,e.id,...i]);o.setRowPinning(a=>{var g,c;if(t==="bottom"){var m,d;return{top:((m=a==null?void 0:a.top)!=null?m:[]).filter(p=>!(u!=null&&u.has(p))),bottom:[...((d=a==null?void 0:a.bottom)!=null?d:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)]}}if(t==="top"){var s,f;return{top:[...((s=a==null?void 0:a.top)!=null?s:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)],bottom:((f=a==null?void 0:a.bottom)!=null?f:[]).filter(p=>!(u!=null&&u.has(p)))}}return{top:((g=a==null?void 0:a.top)!=null?g:[]).filter(p=>!(u!=null&&u.has(p))),bottom:((c=a==null?void 0:a.bottom)!=null?c:[]).filter(p=>!(u!=null&&u.has(p)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:n,enablePinning:r}=o.options;return typeof n=="function"?n(e):(t=n??r)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:n,bottom:r}=o.getState().rowPinning,i=t.some(u=>n==null?void 0:n.includes(u)),l=t.some(u=>r==null?void 0:r.includes(u));return i?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var t,n;const r=e.getIsPinned();if(!r)return-1;const i=(t=r==="top"?o.getTopRows():o.getBottomRows())==null?void 0:t.map(l=>{let{id:u}=l;return u});return(n=i==null?void 0:i.indexOf(e.id))!=null?n:-1}},createTable:e=>{e.setRowPinning=o=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(o),e.resetRowPinning=o=>{var t,n;return e.setRowPinning(o?W():(t=(n=e.initialState)==null?void 0:n.rowPinning)!=null?t:W())},e.getIsSomeRowsPinned=o=>{var t;const n=e.getState().rowPinning;if(!o){var r,i;return!!((r=n.top)!=null&&r.length||(i=n.bottom)!=null&&i.length)}return!!((t=n[o])!=null&&t.length)},e._getPinnedRows=(o,t,n)=>{var r;return((r=e.options.keepPinnedRows)==null||r?(t??[]).map(l=>{const u=e.getRow(l,!0);return u.getIsAllParentsExpanded()?u:null}):(t??[]).map(l=>o.find(u=>u.id===l))).filter(Boolean).map(l=>({...l,position:n}))},e.getTopRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(o,t)=>e._getPinnedRows(o,t,"top"),C(e.options,"debugRows")),e.getBottomRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(o,t)=>e._getPinnedRows(o,t,"bottom"),C(e.options,"debugRows")),e.getCenterRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(o,t,n)=>{const r=new Set([...t??[],...n??[]]);return o.filter(i=>!r.has(i.id))},C(e.options,"debugRows"))}},Yt={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:M("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=o=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(o),e.resetRowSelection=o=>{var t;return e.setRowSelection(o?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=o=>{e.setRowSelection(t=>{o=typeof o<"u"?o:!e.getIsAllRowsSelected();const n={...t},r=e.getPreGroupedRowModel().flatRows;return o?r.forEach(i=>{i.getCanSelect()&&(n[i.id]=!0)}):r.forEach(i=>{delete n[i.id]}),n})},e.toggleAllPageRowsSelected=o=>e.setRowSelection(t=>{const n=typeof o<"u"?o:!e.getIsAllPageRowsSelected(),r={...t};return e.getRowModel().rows.forEach(i=>{Z(r,i.id,n,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=S(()=>[e.getState().rowSelection,e.getCoreRowModel()],(o,t)=>Object.keys(o).length?Y(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getFilteredSelectedRowModel=S(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(o,t)=>Object.keys(o).length?Y(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getGroupedSelectedRowModel=S(()=>[e.getState().rowSelection,e.getSortedRowModel()],(o,t)=>Object.keys(o).length?Y(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const o=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let n=!!(o.length&&Object.keys(t).length);return n&&o.some(r=>r.getCanSelect()&&!t[r.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows.filter(r=>r.getCanSelect()),{rowSelection:t}=e.getState();let n=!!o.length;return n&&o.some(r=>!t[r.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var o;const t=Object.keys((o=e.getState().rowSelection)!=null?o:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:o.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>o=>{e.toggleAllRowsSelected(o.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>o=>{e.toggleAllPageRowsSelected(o.target.checked)}},createRow:(e,o)=>{e.toggleSelected=(t,n)=>{const r=e.getIsSelected();o.setRowSelection(i=>{var l;if(t=typeof t<"u"?t:!r,e.getCanSelect()&&r===t)return i;const u={...i};return Z(u,e.id,t,(l=n==null?void 0:n.selectChildren)!=null?l:!0,o),u})},e.getIsSelected=()=>{const{rowSelection:t}=o.getState();return oe(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=o.getState();return b(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=o.getState();return b(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof o.options.enableRowSelection=="function"?o.options.enableRowSelection(e):(t=o.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof o.options.enableSubRowSelection=="function"?o.options.enableSubRowSelection(e):(t=o.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof o.options.enableMultiRowSelection=="function"?o.options.enableMultiRowSelection(e):(t=o.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected((r=n.target)==null?void 0:r.checked)}}}},Z=(e,o,t,n,r)=>{var i;const l=r.getRow(o,!0);t?(l.getCanMultiSelect()||Object.keys(e).forEach(u=>delete e[u]),l.getCanSelect()&&(e[o]=!0)):delete e[o],n&&(i=l.subRows)!=null&&i.length&&l.getCanSelectSubRows()&&l.subRows.forEach(u=>Z(e,u.id,t,n,r))};function Y(e,o){const t=e.getState().rowSelection,n=[],r={},i=function(l,u){return l.map(a=>{var g;const c=oe(a,t);if(c&&(n.push(a),r[a.id]=a),(g=a.subRows)!=null&&g.length&&(a={...a,subRows:i(a.subRows)}),c)return a}).filter(Boolean)};return{rows:i(o.rows),flatRows:n,rowsById:r}}function oe(e,o){var t;return(t=o[e.id])!=null?t:!1}function b(e,o,t){var n;if(!((n=e.subRows)!=null&&n.length))return!1;let r=!0,i=!1;return e.subRows.forEach(l=>{if(!(i&&!r)&&(l.getCanSelect()&&(oe(l,o)?i=!0:r=!1),l.subRows&&l.subRows.length)){const u=b(l,o);u==="all"?i=!0:(u==="some"&&(i=!0),r=!1)}}),r?"all":i?"some":!1}const ee=/([0-9]+)/gm,Jt=(e,o,t)=>Le(E(e.getValue(t)).toLowerCase(),E(o.getValue(t)).toLowerCase()),Qt=(e,o,t)=>Le(E(e.getValue(t)),E(o.getValue(t))),Zt=(e,o,t)=>re(E(e.getValue(t)).toLowerCase(),E(o.getValue(t)).toLowerCase()),bt=(e,o,t)=>re(E(e.getValue(t)),E(o.getValue(t))),en=(e,o,t)=>{const n=e.getValue(t),r=o.getValue(t);return n>r?1:n<r?-1:0},tn=(e,o,t)=>re(e.getValue(t),o.getValue(t));function re(e,o){return e===o?0:e>o?1:-1}function E(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Le(e,o){const t=e.split(ee).filter(Boolean),n=o.split(ee).filter(Boolean);for(;t.length&&n.length;){const r=t.shift(),i=n.shift(),l=parseInt(r,10),u=parseInt(i,10),a=[l,u].sort();if(isNaN(a[0])){if(r>i)return 1;if(i>r)return-1;continue}if(isNaN(a[1]))return isNaN(l)?-1:1;if(l>u)return 1;if(u>l)return-1}return t.length-n.length}const L={alphanumeric:Jt,alphanumericCaseSensitive:Qt,text:Zt,textCaseSensitive:bt,datetime:en,basic:tn},nn={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:M("sorting",e),isMultiSortEvent:o=>o.shiftKey}),createColumn:(e,o)=>{e.getAutoSortingFn=()=>{const t=o.getFilteredRowModel().flatRows.slice(10);let n=!1;for(const r of t){const i=r==null?void 0:r.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return L.datetime;if(typeof i=="string"&&(n=!0,i.split(ee).length>1))return L.alphanumeric}return n?L.text:L.basic},e.getAutoSortDir=()=>{const t=o.getFilteredRowModel().flatRows[0];return typeof(t==null?void 0:t.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,n;if(!e)throw new Error;return B(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(n=o.options.sortingFns)==null?void 0:n[e.columnDef.sortingFn])!=null?t:L[e.columnDef.sortingFn]},e.toggleSorting=(t,n)=>{const r=e.getNextSortingOrder(),i=typeof t<"u"&&t!==null;o.setSorting(l=>{const u=l==null?void 0:l.find(s=>s.id===e.id),a=l==null?void 0:l.findIndex(s=>s.id===e.id);let g=[],c,m=i?t:r==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&n?u?c="toggle":c="add":l!=null&&l.length&&a!==l.length-1?c="replace":u?c="toggle":c="replace",c==="toggle"&&(i||r||(c="remove")),c==="add"){var d;g=[...l,{id:e.id,desc:m}],g.splice(0,g.length-((d=o.options.maxMultiSortColCount)!=null?d:Number.MAX_SAFE_INTEGER))}else c==="toggle"?g=l.map(s=>s.id===e.id?{...s,desc:m}:s):c==="remove"?g=l.filter(s=>s.id!==e.id):g=[{id:e.id,desc:m}];return g})},e.getFirstSortDir=()=>{var t,n;return((t=(n=e.columnDef.sortDescFirst)!=null?n:o.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var n,r;const i=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==i&&((n=o.options.enableSortingRemoval)==null||n)&&(!(t&&(r=o.options.enableMultiRemove)!=null)||r)?!1:l==="desc"?"asc":"desc":i},e.getCanSort=()=>{var t,n;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((n=o.options.enableSorting)!=null?n:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,n;return(t=(n=e.columnDef.enableMultiSort)!=null?n:o.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const n=(t=o.getState().sorting)==null?void 0:t.find(r=>r.id===e.id);return n?n.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,n;return(t=(n=o.getState().sorting)==null?void 0:n.findIndex(r=>r.id===e.id))!=null?t:-1},e.clearSorting=()=>{o.setSorting(t=>t!=null&&t.length?t.filter(n=>n.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return n=>{t&&(n.persist==null||n.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?o.options.isMultiSortEvent==null?void 0:o.options.isMultiSortEvent(n):!1))}}},createTable:e=>{e.setSorting=o=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(o),e.resetSorting=o=>{var t,n;e.setSorting(o?[]:(t=(n=e.initialState)==null?void 0:n.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},on=[xt,kt,Ot,Nt,$t,Pt,qt,Ut,nn,Lt,Xt,Kt,Wt,Yt,Tt];function rn(e){var o,t;const n=[...on,...(o=e._features)!=null?o:[]];let r={_features:n};const i=r._features.reduce((d,s)=>Object.assign(d,s.getDefaultOptions==null?void 0:s.getDefaultOptions(r)),{}),l=d=>r.options.mergeOptions?r.options.mergeOptions(i,d):{...i,...d};let a={...{},...(t=e.initialState)!=null?t:{}};r._features.forEach(d=>{var s;a=(s=d.getInitialState==null?void 0:d.getInitialState(a))!=null?s:a});const g=[];let c=!1;const m={_features:n,options:{...i,...e},initialState:a,_queue:d=>{g.push(d),c||(c=!0,Promise.resolve().then(()=>{for(;g.length;)g.shift()();c=!1}).catch(s=>setTimeout(()=>{throw s})))},reset:()=>{r.setState(r.initialState)},setOptions:d=>{const s=A(d,r.options);r.options=l(s)},getState:()=>r.options.state,setState:d=>{r.options.onStateChange==null||r.options.onStateChange(d)},_getRowId:(d,s,f)=>{var p;return(p=r.options.getRowId==null?void 0:r.options.getRowId(d,s,f))!=null?p:`${f?[f.id,s].join("."):s}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(d,s)=>{let f=(s?r.getPrePaginationRowModel():r.getRowModel()).rowsById[d];if(!f&&(f=r.getCoreRowModel().rowsById[d],!f))throw new Error;return f},_getDefaultColumnDef:S(()=>[r.options.defaultColumn],d=>{var s;return d=(s=d)!=null?s:{},{header:f=>{const p=f.header.column.columnDef;return p.accessorKey?p.accessorKey:p.accessorFn?p.id:null},cell:f=>{var p,v;return(p=(v=f.renderValue())==null||v.toString==null?void 0:v.toString())!=null?p:null},...r._features.reduce((f,p)=>Object.assign(f,p.getDefaultColumnDef==null?void 0:p.getDefaultColumnDef()),{}),...d}},C(e,"debugColumns")),_getColumnDefs:()=>r.options.columns,getAllColumns:S(()=>[r._getColumnDefs()],d=>{const s=function(f,p,v){return v===void 0&&(v=0),f.map(R=>{const h=Ft(r,R,v,p),_=R;return h.columns=_.columns?s(_.columns,h,v+1):[],h})};return s(d)},C(e,"debugColumns")),getAllFlatColumns:S(()=>[r.getAllColumns()],d=>d.flatMap(s=>s.getFlatColumns()),C(e,"debugColumns")),_getAllFlatColumnsById:S(()=>[r.getAllFlatColumns()],d=>d.reduce((s,f)=>(s[f.id]=f,s),{}),C(e,"debugColumns")),getAllLeafColumns:S(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(d,s)=>{let f=d.flatMap(p=>p.getLeafColumns());return s(f)},C(e,"debugColumns")),getColumn:d=>r._getAllFlatColumnsById()[d]};Object.assign(r,m);for(let d=0;d<r._features.length;d++){const s=r._features[d];s==null||s.createTable==null||s.createTable(r)}return r}function $n(){return e=>S(()=>[e.options.data],o=>{const t={rows:[],flatRows:[],rowsById:{}},n=function(r,i,l){i===void 0&&(i=0);const u=[];for(let g=0;g<r.length;g++){const c=te(e,e._getRowId(r[g],g,l),r[g],g,i,void 0,l==null?void 0:l.id);if(t.flatRows.push(c),t.rowsById[c.id]=c,u.push(c),e.options.getSubRows){var a;c.originalSubRows=e.options.getSubRows(r[g],g),(a=c.originalSubRows)!=null&&a.length&&(c.subRows=n(c.originalSubRows,i+1,c))}}return u};return t.rows=n(o),t},C(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function ln(e){const o=[],t=n=>{var r;o.push(n),(r=n.subRows)!=null&&r.length&&n.getIsExpanded()&&n.subRows.forEach(t)};return e.rows.forEach(t),{rows:o,flatRows:e.flatRows,rowsById:e.rowsById}}function sn(e,o,t){return t.options.filterFromLeafRows?un(e,o,t):an(e,o,t)}function un(e,o,t){var n;const r=[],i={},l=(n=t.options.maxLeafRowFilterDepth)!=null?n:100,u=function(a,g){g===void 0&&(g=0);const c=[];for(let d=0;d<a.length;d++){var m;let s=a[d];const f=te(t,s.id,s.original,s.index,s.depth,void 0,s.parentId);if(f.columnFilters=s.columnFilters,(m=s.subRows)!=null&&m.length&&g<l){if(f.subRows=u(s.subRows,g+1),s=f,o(s)&&!f.subRows.length){c.push(s),i[s.id]=s,r.push(s);continue}if(o(s)||f.subRows.length){c.push(s),i[s.id]=s,r.push(s);continue}}else s=f,o(s)&&(c.push(s),i[s.id]=s,r.push(s))}return c};return{rows:u(e),flatRows:r,rowsById:i}}function an(e,o,t){var n;const r=[],i={},l=(n=t.options.maxLeafRowFilterDepth)!=null?n:100,u=function(a,g){g===void 0&&(g=0);const c=[];for(let d=0;d<a.length;d++){let s=a[d];if(o(s)){var m;if((m=s.subRows)!=null&&m.length&&g<l){const p=te(t,s.id,s.original,s.index,s.depth,void 0,s.parentId);p.subRows=u(s.subRows,g+1),s=p}c.push(s),r.push(s),i[s.id]=s}}return c};return{rows:u(e),flatRows:r,rowsById:i}}function Pn(){return(e,o)=>S(()=>{var t;return[(t=e.getColumn(o))==null?void 0:t.getFacetedRowModel()]},t=>{if(!t)return new Map;let n=new Map;for(let i=0;i<t.flatRows.length;i++){const l=t.flatRows[i].getUniqueValues(o);for(let u=0;u<l.length;u++){const a=l[u];if(n.has(a)){var r;n.set(a,((r=n.get(a))!=null?r:0)+1)}else n.set(a,1)}}return n},C(e.options,"debugTable"))}function Mn(){return e=>S(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(o,t,n)=>{if(!o.rows.length||!(t!=null&&t.length)&&!n){for(let d=0;d<o.flatRows.length;d++)o.flatRows[d].columnFilters={},o.flatRows[d].columnFiltersMeta={};return o}const r=[],i=[];(t??[]).forEach(d=>{var s;const f=e.getColumn(d.id);if(!f)return;const p=f.getFilterFn();p&&r.push({id:d.id,filterFn:p,resolvedValue:(s=p.resolveFilterValue==null?void 0:p.resolveFilterValue(d.value))!=null?s:d.value})});const l=(t??[]).map(d=>d.id),u=e.getGlobalFilterFn(),a=e.getAllLeafColumns().filter(d=>d.getCanGlobalFilter());n&&u&&a.length&&(l.push("__global__"),a.forEach(d=>{var s;i.push({id:d.id,filterFn:u,resolvedValue:(s=u.resolveFilterValue==null?void 0:u.resolveFilterValue(n))!=null?s:n})}));let g,c;for(let d=0;d<o.flatRows.length;d++){const s=o.flatRows[d];if(s.columnFilters={},r.length)for(let f=0;f<r.length;f++){g=r[f];const p=g.id;s.columnFilters[p]=g.filterFn(s,p,g.resolvedValue,v=>{s.columnFiltersMeta[p]=v})}if(i.length){for(let f=0;f<i.length;f++){c=i[f];const p=c.id;if(c.filterFn(s,p,c.resolvedValue,v=>{s.columnFiltersMeta[p]=v})){s.columnFilters.__global__=!0;break}}s.columnFilters.__global__!==!0&&(s.columnFilters.__global__=!1)}}const m=d=>{for(let s=0;s<l.length;s++)if(d.columnFilters[l[s]]===!1)return!1;return!0};return sn(o.rows,m,e)},C(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function Vn(e){return o=>S(()=>[o.getState().pagination,o.getPrePaginationRowModel(),o.options.paginateExpandedRows?void 0:o.getState().expanded],(t,n)=>{if(!n.rows.length)return n;const{pageSize:r,pageIndex:i}=t;let{rows:l,flatRows:u,rowsById:a}=n;const g=r*i,c=g+r;l=l.slice(g,c);let m;o.options.paginateExpandedRows?m={rows:l,flatRows:u,rowsById:a}:m=ln({rows:l,flatRows:u,rowsById:a}),m.flatRows=[];const d=s=>{m.flatRows.push(s),s.subRows.length&&s.subRows.forEach(d)};return m.rows.forEach(d),m},C(o.options,"debugTable"))}function yn(){return e=>S(()=>[e.getState().sorting,e.getPreSortedRowModel()],(o,t)=>{if(!t.rows.length||!(o!=null&&o.length))return t;const n=e.getState().sorting,r=[],i=n.filter(a=>{var g;return(g=e.getColumn(a.id))==null?void 0:g.getCanSort()}),l={};i.forEach(a=>{const g=e.getColumn(a.id);g&&(l[a.id]={sortUndefined:g.columnDef.sortUndefined,invertSorting:g.columnDef.invertSorting,sortingFn:g.getSortingFn()})});const u=a=>{const g=a.map(c=>({...c}));return g.sort((c,m)=>{for(let s=0;s<i.length;s+=1){var d;const f=i[s],p=l[f.id],v=p.sortUndefined,R=(d=f==null?void 0:f.desc)!=null?d:!1;let h=0;if(v){const _=c.getValue(f.id),y=m.getValue(f.id),$=_===void 0,G=y===void 0;if($||G){if(v==="first")return $?-1:1;if(v==="last")return $?1:-1;h=$&&G?0:$?v:-v}}if(h===0&&(h=p.sortingFn(c,m,f.id)),h!==0)return R&&(h*=-1),p.invertSorting&&(h*=-1),h}return c.index-m.index}),g.forEach(c=>{var m;r.push(c),(m=c.subRows)!=null&&m.length&&(c.subRows=u(c.subRows))}),g};return{rows:u(t.rows),flatRows:r,rowsById:t.rowsById}},C(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function In(e,o){return e?gn(e)?P.createElement(e,o):e:null}function gn(e){return dn(e)||typeof e=="function"||cn(e)}function dn(e){return typeof e=="function"&&(()=>{const o=Object.getPrototypeOf(e);return o.prototype&&o.prototype.isReactComponent})()}function cn(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function Dn(e){const o={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=P.useState(()=>({current:rn(o)})),[n,r]=P.useState(()=>t.current.initialState);return t.current.setOptions(i=>({...i,...e,state:{...n,...e.state},onStateChange:l=>{r(l),e.onStateChange==null||e.onStateChange(l)}})),t.current}function An({...e}){return w.jsx(dt,{"data-slot":"alert-dialog",...e})}function En({...e}){return w.jsx(ct,{"data-slot":"alert-dialog-trigger",...e})}function fn({...e}){return w.jsx(ft,{"data-slot":"alert-dialog-portal",...e})}function pn({className:e,...o}){return w.jsx(pt,{"data-slot":"alert-dialog-overlay",className:x("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...o})}function Gn({className:e,...o}){return w.jsxs(fn,{children:[w.jsx(pn,{}),w.jsx(mt,{"data-slot":"alert-dialog-content",className:x("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-1/2 left-1/2 z-50 grid max-h-[calc(100%-2rem)] w-full max-w-[calc(100%-2rem)] -translate-x-1/2 -translate-y-1/2 gap-4 overflow-y-auto rounded-xl border p-6 shadow-lg duration-200 sm:max-w-100",e),...o})]})}function Hn({className:e,...o}){return w.jsx("div",{"data-slot":"alert-dialog-header",className:x("flex flex-col gap-1 text-center sm:text-left",e),...o})}function Ln({className:e,...o}){return w.jsx("div",{"data-slot":"alert-dialog-footer",className:x("flex flex-col-reverse gap-3 sm:flex-row sm:justify-end",e),...o})}function zn({className:e,...o}){return w.jsx(vt,{"data-slot":"alert-dialog-title",className:x("text-lg font-semibold",e),...o})}function On({className:e,...o}){return w.jsx(Rt,{"data-slot":"alert-dialog-description",className:x("text-muted-foreground text-sm",e),...o})}function Nn({className:e,...o}){return w.jsx(St,{className:x(ce(),e),...o})}function jn({className:e,...o}){return w.jsx(Ct,{className:x(ce({variant:"outline"}),e),...o})}function Tn({className:e,...o}){return w.jsx("nav",{role:"navigation","aria-label":"pagination","data-slot":"pagination",className:x("mx-auto flex w-full justify-center",e),...o})}function Bn({className:e,...o}){return w.jsx("ul",{"data-slot":"pagination-content",className:x("flex flex-row items-center gap-1",e),...o})}function kn({...e}){return w.jsx("li",{"data-slot":"pagination-item",...e})}function qn({className:e,...o}){return w.jsxs(Je,{className:"grid h-full w-full grid-cols-1",children:[w.jsx("table",{"data-slot":"table",className:x("w-full caption-bottom text-sm",e),...o}),w.jsx(ue,{orientation:"horizontal"}),w.jsx(ue,{orientation:"vertical"})]})}function Un({className:e,...o}){return w.jsx("thead",{"data-slot":"table-header",className:x(e),...o})}function Xn({className:e,...o}){return w.jsx("tbody",{"data-slot":"table-body",className:x("sticky top-0 [&_tr:last-child]:border-0",e),...o})}function Kn({className:e,...o}){return w.jsx("tr",{"data-slot":"table-row",className:x("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...o})}function Wn({className:e,...o}){return w.jsx("th",{"data-slot":"table-head",className:x("text-muted-foreground h-12 px-3 text-left align-middle font-medium has-[role=checkbox]:w-px [&:has([role=checkbox])]:pr-0",e),...o})}function Yn({className:e,...o}){return w.jsx("td",{"data-slot":"table-cell",className:x("p-3 align-middle [&:has([role=checkbox])]:pr-0",e),...o})}export{An as A,_n as C,Fn as E,Tn as P,qn as T,En as a,Gn as b,Hn as c,zn as d,On as e,Ln as f,jn as g,Nn as h,Un as i,Kn as j,Wn as k,In as l,Xn as m,Yn as n,Bn as o,kn as p,Rn as q,hn as r,wn as s,Pn as t,Dn as u,Mn as v,Vn as w,yn as x,$n as y};
