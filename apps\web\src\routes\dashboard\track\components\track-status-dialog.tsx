import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { CheckCircle, Clock, AlertCircle } from "lucide-react";

import { useTRPCClient } from "@/utils/trpc";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import type { TrackTableItem } from "@/routes/dashboard/track/components/track-table";

interface TrackStatusDialogProps {
  children: React.ReactNode;
  track: TrackTableItem;
  onStatusUpdated: () => void;
}

export function TrackStatusDialog({
  children,
  track,
  onStatusUpdated,
}: TrackStatusDialogProps) {
  const [open, setOpen] = useState(false);
  const trpcClient = useTRPCClient();

  const updateStatusMutation = useMutation({
    mutationFn: async (status: "DRAFT" | "READY") => {
      return trpcClient.track.updateStatus.mutate({
        id: track.id,
        status,
      });
    },
    onSuccess: (data, variables) => {
      toast.success(
        `Track status updated to ${variables.toLowerCase()}`
      );
      setOpen(false);
      onStatusUpdated();
    },
    onError: (error: any) => {
      console.error("Failed to update track status:", error);
      toast.error(
        "Failed to update status: " + (error.message || "Unknown error")
      );
    },
  });

  const handleStatusChange = (newStatus: "DRAFT" | "READY") => {
    updateStatusMutation.mutate(newStatus);
  };

  const getStatusInfo = (status: "DRAFT" | "READY") => {
    switch (status) {
      case "DRAFT":
        return {
          icon: <Clock className="h-5 w-5 text-yellow-500" />,
          label: "Draft",
          description: "Track is in draft mode and not ready for distribution",
          color: "bg-yellow-50 border-yellow-200",
          textColor: "text-yellow-800",
        };
      case "READY":
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          label: "Ready",
          description: "Track is ready for distribution and release",
          color: "bg-green-50 border-green-200",
          textColor: "text-green-800",
        };
    }
  };

  const currentStatusInfo = getStatusInfo(track.status);
  const oppositeStatus = track.status === "DRAFT" ? "READY" : "DRAFT";
  const oppositeStatusInfo = getStatusInfo(oppositeStatus);

  // Validation checks for READY status
  const validationChecks = [
    {
      label: "Track title",
      valid: !!track.title?.trim(),
      required: true,
    },
    {
      label: "Genre",
      valid: !!track.genre?.trim(),
      required: true,
    },
    {
      label: "Publishing holder",
      valid: !!track.publishingHolder?.trim(),
      required: true,
    },
    {
      label: "Track files",
      valid: track.trackFiles.length > 0,
      required: true,
    },
    {
      label: "Artists",
      valid: track.artists.length > 0,
      required: true,
    },
    {
      label: "Contributors",
      valid: track.contributors.length > 0,
      required: true,
    },
    {
      label: "ISRC",
      valid: !!track.isrc?.trim(),
      required: false,
    },
    {
      label: "Preview settings",
      valid: !!track.previewStart && !!track.previewLength,
      required: false,
    },
  ];

  const requiredChecks = validationChecks.filter(check => check.required);
  const allRequiredValid = requiredChecks.every(check => check.valid);
  const canSetReady = allRequiredValid;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Update Track Status</DialogTitle>
          <DialogDescription>
            Change the status of "{track.title}" to control its availability for distribution.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Current Status</h4>
            <Card className={currentStatusInfo.color}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  {currentStatusInfo.icon}
                  <div>
                    <p className={`font-medium ${currentStatusInfo.textColor}`}>
                      {currentStatusInfo.label}
                    </p>
                    <p className={`text-sm ${currentStatusInfo.textColor}`}>
                      {currentStatusInfo.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Status Timestamps */}
          {(track.submittedAt || track.readyAt) && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Status History</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                {track.submittedAt && (
                  <p>
                    Submitted: {new Date(track.submittedAt).toLocaleString()}
                  </p>
                )}
                {track.readyAt && (
                  <p>
                    Ready: {new Date(track.readyAt).toLocaleString()}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Validation Checks (when trying to set to READY) */}
          {track.status === "DRAFT" && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Readiness Checklist</h4>
              <div className="space-y-2">
                {validationChecks.map((check, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    {check.valid ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className={`text-sm ${check.valid ? "text-green-700" : "text-red-700"}`}>
                      {check.label}
                      {check.required && " *"}
                    </span>
                  </div>
                ))}
              </div>
              {!canSetReady && (
                <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-800">
                    Please complete all required fields before setting track to Ready.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Action Section */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Change Status</h4>
            <Card className={oppositeStatusInfo.color}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {oppositeStatusInfo.icon}
                    <div>
                      <p className={`font-medium ${oppositeStatusInfo.textColor}`}>
                        Set to {oppositeStatusInfo.label}
                      </p>
                      <p className={`text-sm ${oppositeStatusInfo.textColor}`}>
                        {oppositeStatusInfo.description}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleStatusChange(oppositeStatus)}
                    disabled={
                      updateStatusMutation.isPending ||
                      (oppositeStatus === "READY" && !canSetReady)
                    }
                    variant={oppositeStatus === "READY" ? "default" : "outline"}
                  >
                    {updateStatusMutation.isPending
                      ? "Updating..."
                      : `Set ${oppositeStatusInfo.label}`}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
