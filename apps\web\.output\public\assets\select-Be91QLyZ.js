import{c as $e,r as u,j as y,B as it}from"./main-CiHxKC0e.js";import{u as G,e as Tn,a as ye}from"./button-mzA_gOdo.js";import{d as J,c as Tt,e as Et,q as En,u as xt,a as st,t as On,b as W,f as In,v as Nn,w as _n,x as Mn,F as Dn,D as Ln,y as wt}from"./dialog-B3qaM3HV.js";import{P as z}from"./label-C9y0GF5L.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jn=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],kn=$e("Check",jn);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hn=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Ot=$e("ChevronDown",Hn);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bn=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Vn=$e("ChevronUp",Bn);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fn=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],zr=$e("Plus",Fn);var It=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),$n="VisuallyHidden",Nt=u.forwardRef((e,t)=>y.jsx(z.span,{...e,ref:t,style:{...It,...e.style}}));Nt.displayName=$n;var Ur=Nt;function Wn(e){const t=u.useRef({value:e,previous:e});return u.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function zn(e){const[t,n]=u.useState(void 0);return J(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let i,c;if("borderBoxSize"in s){const l=s.borderBoxSize,a=Array.isArray(l)?l[0]:l;i=a.inlineSize,c=a.blockSize}else i=e.offsetWidth,c=e.offsetHeight;n({width:i,height:c})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}const Un=["top","right","bottom","left"],fe=Math.min,X=Math.max,ke=Math.round,Le=Math.floor,oe=e=>({x:e,y:e}),Kn={left:"right",right:"left",bottom:"top",top:"bottom"},Yn={start:"end",end:"start"};function Ze(e,t,n){return X(e,fe(t,n))}function ce(e,t){return typeof e=="function"?e(t):e}function le(e){return e.split("-")[0]}function be(e){return e.split("-")[1]}function ct(e){return e==="x"?"y":"x"}function lt(e){return e==="y"?"height":"width"}function ne(e){return["top","bottom"].includes(le(e))?"y":"x"}function at(e){return ct(ne(e))}function Xn(e,t,n){n===void 0&&(n=!1);const o=be(e),r=at(e),s=lt(r);let i=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=He(i)),[i,He(i)]}function qn(e){const t=He(e);return[Je(e),t,Je(t)]}function Je(e){return e.replace(/start|end/g,t=>Yn[t])}function Gn(e,t,n){const o=["left","right"],r=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?s:i;default:return[]}}function Zn(e,t,n,o){const r=be(e);let s=Gn(le(e),n==="start",o);return r&&(s=s.map(i=>i+"-"+r),t&&(s=s.concat(s.map(Je)))),s}function He(e){return e.replace(/left|right|bottom|top/g,t=>Kn[t])}function Jn(e){return{top:0,right:0,bottom:0,left:0,...e}}function _t(e){return typeof e!="number"?Jn(e):{top:e,right:e,bottom:e,left:e}}function Be(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function vt(e,t,n){let{reference:o,floating:r}=e;const s=ne(t),i=at(t),c=lt(i),l=le(t),a=s==="y",f=o.x+o.width/2-r.width/2,p=o.y+o.height/2-r.height/2,w=o[c]/2-r[c]/2;let h;switch(l){case"top":h={x:f,y:o.y-r.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:p};break;case"left":h={x:o.x-r.width,y:p};break;default:h={x:o.x,y:o.y}}switch(be(t)){case"start":h[i]-=w*(n&&a?-1:1);break;case"end":h[i]+=w*(n&&a?-1:1);break}return h}const Qn=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:s=[],platform:i}=n,c=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let a=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:p}=vt(a,o,l),w=o,h={},g=0;for(let d=0;d<c.length;d++){const{name:x,fn:S}=c[d],{x:m,y:v,data:b,reset:C}=await S({x:f,y:p,initialPlacement:o,placement:w,strategy:r,middlewareData:h,rects:a,platform:i,elements:{reference:e,floating:t}});f=m??f,p=v??p,h={...h,[x]:{...h[x],...b}},C&&g<=50&&(g++,typeof C=="object"&&(C.placement&&(w=C.placement),C.rects&&(a=C.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:r}):C.rects),{x:f,y:p}=vt(a,w,l)),d=-1)}return{x:f,y:p,placement:w,strategy:r,middlewareData:h}};async function Ne(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:s,rects:i,elements:c,strategy:l}=e,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:w=!1,padding:h=0}=ce(t,e),g=_t(h),x=c[w?p==="floating"?"reference":"floating":p],S=Be(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(x)))==null||n?x:x.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:a,rootBoundary:f,strategy:l})),m=p==="floating"?{x:o,y:r,width:i.floating.width,height:i.floating.height}:i.reference,v=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),b=await(s.isElement==null?void 0:s.isElement(v))?await(s.getScale==null?void 0:s.getScale(v))||{x:1,y:1}:{x:1,y:1},C=Be(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:m,offsetParent:v,strategy:l}):m);return{top:(S.top-C.top+g.top)/b.y,bottom:(C.bottom-S.bottom+g.bottom)/b.y,left:(S.left-C.left+g.left)/b.x,right:(C.right-S.right+g.right)/b.x}}const eo=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:s,platform:i,elements:c,middlewareData:l}=t,{element:a,padding:f=0}=ce(e,t)||{};if(a==null)return{};const p=_t(f),w={x:n,y:o},h=at(r),g=lt(h),d=await i.getDimensions(a),x=h==="y",S=x?"top":"left",m=x?"bottom":"right",v=x?"clientHeight":"clientWidth",b=s.reference[g]+s.reference[h]-w[h]-s.floating[g],C=w[h]-s.reference[h],T=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a));let R=T?T[v]:0;(!R||!await(i.isElement==null?void 0:i.isElement(T)))&&(R=c.floating[v]||s.floating[g]);const N=b/2-C/2,F=R/2-d[g]/2-1,L=fe(p[S],F),k=fe(p[m],F),H=L,_=R-d[g]-k,O=R/2-d[g]/2+N,$=Ze(H,O,_),E=!l.arrow&&be(r)!=null&&O!==$&&s.reference[g]/2-(O<H?L:k)-d[g]/2<0,I=E?O<H?O-H:O-_:0;return{[h]:w[h]+I,data:{[h]:$,centerOffset:O-$-I,...E&&{alignmentOffset:I}},reset:E}}}),to=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:s,rects:i,initialPlacement:c,platform:l,elements:a}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:w,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:d=!0,...x}=ce(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const S=le(r),m=ne(c),v=le(c)===c,b=await(l.isRTL==null?void 0:l.isRTL(a.floating)),C=w||(v||!d?[He(c)]:qn(c)),T=g!=="none";!w&&T&&C.push(...Zn(c,d,g,b));const R=[c,...C],N=await Ne(t,x),F=[];let L=((o=s.flip)==null?void 0:o.overflows)||[];if(f&&F.push(N[S]),p){const O=Xn(r,i,b);F.push(N[O[0]],N[O[1]])}if(L=[...L,{placement:r,overflows:F}],!F.every(O=>O<=0)){var k,H;const O=(((k=s.flip)==null?void 0:k.index)||0)+1,$=R[O];if($&&(!(p==="alignment"?m!==ne($):!1)||L.every(A=>A.overflows[0]>0&&ne(A.placement)===m)))return{data:{index:O,overflows:L},reset:{placement:$}};let E=(H=L.filter(I=>I.overflows[0]<=0).sort((I,A)=>I.overflows[1]-A.overflows[1])[0])==null?void 0:H.placement;if(!E)switch(h){case"bestFit":{var _;const I=(_=L.filter(A=>{if(T){const B=ne(A.placement);return B===m||B==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(B=>B>0).reduce((B,K)=>B+K,0)]).sort((A,B)=>A[1]-B[1])[0])==null?void 0:_[0];I&&(E=I);break}case"initialPlacement":E=c;break}if(r!==E)return{reset:{placement:E}}}return{}}}};function yt(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function St(e){return Un.some(t=>e[t]>=0)}const no=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=ce(e,t);switch(o){case"referenceHidden":{const s=await Ne(t,{...r,elementContext:"reference"}),i=yt(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:St(i)}}}case"escaped":{const s=await Ne(t,{...r,altBoundary:!0}),i=yt(s,n.floating);return{data:{escapedOffsets:i,escaped:St(i)}}}default:return{}}}}};async function oo(e,t){const{placement:n,platform:o,elements:r}=e,s=await(o.isRTL==null?void 0:o.isRTL(r.floating)),i=le(n),c=be(n),l=ne(n)==="y",a=["left","top"].includes(i)?-1:1,f=s&&l?-1:1,p=ce(t,e);let{mainAxis:w,crossAxis:h,alignmentAxis:g}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return c&&typeof g=="number"&&(h=c==="end"?g*-1:g),l?{x:h*f,y:w*a}:{x:w*a,y:h*f}}const ro=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:s,placement:i,middlewareData:c}=t,l=await oo(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:i}}}}},io=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:c={fn:x=>{let{x:S,y:m}=x;return{x:S,y:m}}},...l}=ce(e,t),a={x:n,y:o},f=await Ne(t,l),p=ne(le(r)),w=ct(p);let h=a[w],g=a[p];if(s){const x=w==="y"?"top":"left",S=w==="y"?"bottom":"right",m=h+f[x],v=h-f[S];h=Ze(m,h,v)}if(i){const x=p==="y"?"top":"left",S=p==="y"?"bottom":"right",m=g+f[x],v=g-f[S];g=Ze(m,g,v)}const d=c.fn({...t,[w]:h,[p]:g});return{...d,data:{x:d.x-n,y:d.y-o,enabled:{[w]:s,[p]:i}}}}}},so=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:s,middlewareData:i}=t,{offset:c=0,mainAxis:l=!0,crossAxis:a=!0}=ce(e,t),f={x:n,y:o},p=ne(r),w=ct(p);let h=f[w],g=f[p];const d=ce(c,t),x=typeof d=="number"?{mainAxis:d,crossAxis:0}:{mainAxis:0,crossAxis:0,...d};if(l){const v=w==="y"?"height":"width",b=s.reference[w]-s.floating[v]+x.mainAxis,C=s.reference[w]+s.reference[v]-x.mainAxis;h<b?h=b:h>C&&(h=C)}if(a){var S,m;const v=w==="y"?"width":"height",b=["top","left"].includes(le(r)),C=s.reference[p]-s.floating[v]+(b&&((S=i.offset)==null?void 0:S[p])||0)+(b?0:x.crossAxis),T=s.reference[p]+s.reference[v]+(b?0:((m=i.offset)==null?void 0:m[p])||0)-(b?x.crossAxis:0);g<C?g=C:g>T&&(g=T)}return{[w]:h,[p]:g}}}},co=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:s,platform:i,elements:c}=t,{apply:l=()=>{},...a}=ce(e,t),f=await Ne(t,a),p=le(r),w=be(r),h=ne(r)==="y",{width:g,height:d}=s.floating;let x,S;p==="top"||p==="bottom"?(x=p,S=w===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(S=p,x=w==="end"?"top":"bottom");const m=d-f.top-f.bottom,v=g-f.left-f.right,b=fe(d-f[x],m),C=fe(g-f[S],v),T=!t.middlewareData.shift;let R=b,N=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(N=v),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(R=m),T&&!w){const L=X(f.left,0),k=X(f.right,0),H=X(f.top,0),_=X(f.bottom,0);h?N=g-2*(L!==0||k!==0?L+k:X(f.left,f.right)):R=d-2*(H!==0||_!==0?H+_:X(f.top,f.bottom))}await l({...t,availableWidth:N,availableHeight:R});const F=await i.getDimensions(c.floating);return g!==F.width||d!==F.height?{reset:{rects:!0}}:{}}}};function We(){return typeof window<"u"}function Ce(e){return Mt(e)?(e.nodeName||"").toLowerCase():"#document"}function q(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ie(e){var t;return(t=(Mt(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Mt(e){return We()?e instanceof Node||e instanceof q(e).Node:!1}function Q(e){return We()?e instanceof Element||e instanceof q(e).Element:!1}function re(e){return We()?e instanceof HTMLElement||e instanceof q(e).HTMLElement:!1}function bt(e){return!We()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof q(e).ShadowRoot}function Me(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function lo(e){return["table","td","th"].includes(Ce(e))}function ze(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function ft(e){const t=dt(),n=Q(e)?ee(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function ao(e){let t=de(e);for(;re(t)&&!Se(t);){if(ft(t))return t;if(ze(t))return null;t=de(t)}return null}function dt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Se(e){return["html","body","#document"].includes(Ce(e))}function ee(e){return q(e).getComputedStyle(e)}function Ue(e){return Q(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function de(e){if(Ce(e)==="html")return e;const t=e.assignedSlot||e.parentNode||bt(e)&&e.host||ie(e);return bt(t)?t.host:t}function Dt(e){const t=de(e);return Se(t)?e.ownerDocument?e.ownerDocument.body:e.body:re(t)&&Me(t)?t:Dt(t)}function _e(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Dt(e),s=r===((o=e.ownerDocument)==null?void 0:o.body),i=q(r);if(s){const c=Qe(i);return t.concat(i,i.visualViewport||[],Me(r)?r:[],c&&n?_e(c):[])}return t.concat(r,_e(r,[],n))}function Qe(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Lt(e){const t=ee(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=re(e),s=r?e.offsetWidth:n,i=r?e.offsetHeight:o,c=ke(n)!==s||ke(o)!==i;return c&&(n=s,o=i),{width:n,height:o,$:c}}function ut(e){return Q(e)?e:e.contextElement}function ve(e){const t=ut(e);if(!re(t))return oe(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:s}=Lt(t);let i=(s?ke(n.width):n.width)/o,c=(s?ke(n.height):n.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const fo=oe(0);function jt(e){const t=q(e);return!dt()||!t.visualViewport?fo:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function uo(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==q(e)?!1:t}function me(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=ut(e);let i=oe(1);t&&(o?Q(o)&&(i=ve(o)):i=ve(e));const c=uo(s,n,o)?jt(s):oe(0);let l=(r.left+c.x)/i.x,a=(r.top+c.y)/i.y,f=r.width/i.x,p=r.height/i.y;if(s){const w=q(s),h=o&&Q(o)?q(o):o;let g=w,d=Qe(g);for(;d&&o&&h!==g;){const x=ve(d),S=d.getBoundingClientRect(),m=ee(d),v=S.left+(d.clientLeft+parseFloat(m.paddingLeft))*x.x,b=S.top+(d.clientTop+parseFloat(m.paddingTop))*x.y;l*=x.x,a*=x.y,f*=x.x,p*=x.y,l+=v,a+=b,g=q(d),d=Qe(g)}}return Be({width:f,height:p,x:l,y:a})}function pt(e,t){const n=Ue(e).scrollLeft;return t?t.left+n:me(ie(e)).left+n}function kt(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:pt(e,o)),s=o.top+t.scrollTop;return{x:r,y:s}}function po(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const s=r==="fixed",i=ie(o),c=t?ze(t.floating):!1;if(o===i||c&&s)return n;let l={scrollLeft:0,scrollTop:0},a=oe(1);const f=oe(0),p=re(o);if((p||!p&&!s)&&((Ce(o)!=="body"||Me(i))&&(l=Ue(o)),re(o))){const h=me(o);a=ve(o),f.x=h.x+o.clientLeft,f.y=h.y+o.clientTop}const w=i&&!p&&!s?kt(i,l,!0):oe(0);return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-l.scrollLeft*a.x+f.x+w.x,y:n.y*a.y-l.scrollTop*a.y+f.y+w.y}}function ho(e){return Array.from(e.getClientRects())}function mo(e){const t=ie(e),n=Ue(e),o=e.ownerDocument.body,r=X(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=X(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let i=-n.scrollLeft+pt(e);const c=-n.scrollTop;return ee(o).direction==="rtl"&&(i+=X(t.clientWidth,o.clientWidth)-r),{width:r,height:s,x:i,y:c}}function go(e,t){const n=q(e),o=ie(e),r=n.visualViewport;let s=o.clientWidth,i=o.clientHeight,c=0,l=0;if(r){s=r.width,i=r.height;const a=dt();(!a||a&&t==="fixed")&&(c=r.offsetLeft,l=r.offsetTop)}return{width:s,height:i,x:c,y:l}}function xo(e,t){const n=me(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,s=re(e)?ve(e):oe(1),i=e.clientWidth*s.x,c=e.clientHeight*s.y,l=r*s.x,a=o*s.y;return{width:i,height:c,x:l,y:a}}function Ct(e,t,n){let o;if(t==="viewport")o=go(e,n);else if(t==="document")o=mo(ie(e));else if(Q(t))o=xo(t,n);else{const r=jt(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return Be(o)}function Ht(e,t){const n=de(e);return n===t||!Q(n)||Se(n)?!1:ee(n).position==="fixed"||Ht(n,t)}function wo(e,t){const n=t.get(e);if(n)return n;let o=_e(e,[],!1).filter(c=>Q(c)&&Ce(c)!=="body"),r=null;const s=ee(e).position==="fixed";let i=s?de(e):e;for(;Q(i)&&!Se(i);){const c=ee(i),l=ft(i);!l&&c.position==="fixed"&&(r=null),(s?!l&&!r:!l&&c.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||Me(i)&&!l&&Ht(e,i))?o=o.filter(f=>f!==i):r=c,i=de(i)}return t.set(e,o),o}function vo(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const i=[...n==="clippingAncestors"?ze(t)?[]:wo(t,this._c):[].concat(n),o],c=i[0],l=i.reduce((a,f)=>{const p=Ct(t,f,r);return a.top=X(p.top,a.top),a.right=fe(p.right,a.right),a.bottom=fe(p.bottom,a.bottom),a.left=X(p.left,a.left),a},Ct(t,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function yo(e){const{width:t,height:n}=Lt(e);return{width:t,height:n}}function So(e,t,n){const o=re(t),r=ie(t),s=n==="fixed",i=me(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const l=oe(0);function a(){l.x=pt(r)}if(o||!o&&!s)if((Ce(t)!=="body"||Me(r))&&(c=Ue(t)),o){const h=me(t,!0,s,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else r&&a();s&&!o&&r&&a();const f=r&&!o&&!s?kt(r,c):oe(0),p=i.left+c.scrollLeft-l.x-f.x,w=i.top+c.scrollTop-l.y-f.y;return{x:p,y:w,width:i.width,height:i.height}}function qe(e){return ee(e).position==="static"}function At(e,t){if(!re(e)||ee(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ie(e)===n&&(n=n.ownerDocument.body),n}function Bt(e,t){const n=q(e);if(ze(e))return n;if(!re(e)){let r=de(e);for(;r&&!Se(r);){if(Q(r)&&!qe(r))return r;r=de(r)}return n}let o=At(e,t);for(;o&&lo(o)&&qe(o);)o=At(o,t);return o&&Se(o)&&qe(o)&&!ft(o)?n:o||ao(e)||n}const bo=async function(e){const t=this.getOffsetParent||Bt,n=this.getDimensions,o=await n(e.floating);return{reference:So(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Co(e){return ee(e).direction==="rtl"}const Ao={convertOffsetParentRelativeRectToViewportRelativeRect:po,getDocumentElement:ie,getClippingRect:vo,getOffsetParent:Bt,getElementRects:bo,getClientRects:ho,getDimensions:yo,getScale:ve,isElement:Q,isRTL:Co};function Vt(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Po(e,t){let n=null,o;const r=ie(e);function s(){var c;clearTimeout(o),(c=n)==null||c.disconnect(),n=null}function i(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),s();const a=e.getBoundingClientRect(),{left:f,top:p,width:w,height:h}=a;if(c||t(),!w||!h)return;const g=Le(p),d=Le(r.clientWidth-(f+w)),x=Le(r.clientHeight-(p+h)),S=Le(f),v={rootMargin:-g+"px "+-d+"px "+-x+"px "+-S+"px",threshold:X(0,fe(1,l))||1};let b=!0;function C(T){const R=T[0].intersectionRatio;if(R!==l){if(!b)return i();R?i(!1,R):o=setTimeout(()=>{i(!1,1e-7)},1e3)}R===1&&!Vt(a,e.getBoundingClientRect())&&i(),b=!1}try{n=new IntersectionObserver(C,{...v,root:r.ownerDocument})}catch{n=new IntersectionObserver(C,v)}n.observe(e)}return i(!0),s}function Ro(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,a=ut(e),f=r||s?[...a?_e(a):[],..._e(t)]:[];f.forEach(S=>{r&&S.addEventListener("scroll",n,{passive:!0}),s&&S.addEventListener("resize",n)});const p=a&&c?Po(a,n):null;let w=-1,h=null;i&&(h=new ResizeObserver(S=>{let[m]=S;m&&m.target===a&&h&&(h.unobserve(t),cancelAnimationFrame(w),w=requestAnimationFrame(()=>{var v;(v=h)==null||v.observe(t)})),n()}),a&&!l&&h.observe(a),h.observe(t));let g,d=l?me(e):null;l&&x();function x(){const S=me(e);d&&!Vt(d,S)&&n(),d=S,g=requestAnimationFrame(x)}return n(),()=>{var S;f.forEach(m=>{r&&m.removeEventListener("scroll",n),s&&m.removeEventListener("resize",n)}),p==null||p(),(S=h)==null||S.disconnect(),h=null,l&&cancelAnimationFrame(g)}}const To=ro,Eo=io,Oo=to,Io=co,No=no,Pt=eo,_o=so,Mo=(e,t,n)=>{const o=new Map,r={platform:Ao,...n},s={...r.platform,_c:o};return Qn(e,t,{...r,platform:s})};var Do=typeof document<"u",Lo=function(){},je=Do?u.useLayoutEffect:Lo;function Ve(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!Ve(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const s=r[o];if(!(s==="_owner"&&e.$$typeof)&&!Ve(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Ft(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Rt(e,t){const n=Ft(e);return Math.round(t*n)/n}function Ge(e){const t=u.useRef(e);return je(()=>{t.current=e}),t}function jo(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:s,floating:i}={},transform:c=!0,whileElementsMounted:l,open:a}=e,[f,p]=u.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[w,h]=u.useState(o);Ve(w,o)||h(o);const[g,d]=u.useState(null),[x,S]=u.useState(null),m=u.useCallback(A=>{A!==T.current&&(T.current=A,d(A))},[]),v=u.useCallback(A=>{A!==R.current&&(R.current=A,S(A))},[]),b=s||g,C=i||x,T=u.useRef(null),R=u.useRef(null),N=u.useRef(f),F=l!=null,L=Ge(l),k=Ge(r),H=Ge(a),_=u.useCallback(()=>{if(!T.current||!R.current)return;const A={placement:t,strategy:n,middleware:w};k.current&&(A.platform=k.current),Mo(T.current,R.current,A).then(B=>{const K={...B,isPositioned:H.current!==!1};O.current&&!Ve(N.current,K)&&(N.current=K,it.flushSync(()=>{p(K)}))})},[w,t,n,k,H]);je(()=>{a===!1&&N.current.isPositioned&&(N.current.isPositioned=!1,p(A=>({...A,isPositioned:!1})))},[a]);const O=u.useRef(!1);je(()=>(O.current=!0,()=>{O.current=!1}),[]),je(()=>{if(b&&(T.current=b),C&&(R.current=C),b&&C){if(L.current)return L.current(b,C,_);_()}},[b,C,_,L,F]);const $=u.useMemo(()=>({reference:T,floating:R,setReference:m,setFloating:v}),[m,v]),E=u.useMemo(()=>({reference:b,floating:C}),[b,C]),I=u.useMemo(()=>{const A={position:n,left:0,top:0};if(!E.floating)return A;const B=Rt(E.floating,f.x),K=Rt(E.floating,f.y);return c?{...A,transform:"translate("+B+"px, "+K+"px)",...Ft(E.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:B,top:K}},[n,c,E.floating,f.x,f.y]);return u.useMemo(()=>({...f,update:_,refs:$,elements:E,floatingStyles:I}),[f,_,$,E,I])}const ko=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?Pt({element:o.current,padding:r}).fn(n):{}:o?Pt({element:o,padding:r}).fn(n):{}}}},Ho=(e,t)=>({...To(e),options:[e,t]}),Bo=(e,t)=>({...Eo(e),options:[e,t]}),Vo=(e,t)=>({..._o(e),options:[e,t]}),Fo=(e,t)=>({...Oo(e),options:[e,t]}),$o=(e,t)=>({...Io(e),options:[e,t]}),Wo=(e,t)=>({...No(e),options:[e,t]}),zo=(e,t)=>({...ko(e),options:[e,t]});var Uo="Arrow",$t=u.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...s}=e;return y.jsx(z.svg,{...s,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:y.jsx("polygon",{points:"0,0 30,0 15,10"})})});$t.displayName=Uo;var Ko=$t,ht="Popper",[Wt,zt]=Tt(ht),[Yo,Ut]=Wt(ht),Kt=e=>{const{__scopePopper:t,children:n}=e,[o,r]=u.useState(null);return y.jsx(Yo,{scope:t,anchor:o,onAnchorChange:r,children:n})};Kt.displayName=ht;var Yt="PopperAnchor",Xt=u.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,s=Ut(Yt,n),i=u.useRef(null),c=G(t,i);return u.useEffect(()=>{s.onAnchorChange((o==null?void 0:o.current)||i.current)}),o?null:y.jsx(z.div,{...r,ref:c})});Xt.displayName=Yt;var mt="PopperContent",[Xo,qo]=Wt(mt),qt=u.forwardRef((e,t)=>{var P,V,U,j,M,D;const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:a=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:w=!1,updatePositionStrategy:h="optimized",onPlaced:g,...d}=e,x=Ut(mt,n),[S,m]=u.useState(null),v=G(t,Y=>m(Y)),[b,C]=u.useState(null),T=zn(b),R=(T==null?void 0:T.width)??0,N=(T==null?void 0:T.height)??0,F=o+(s!=="center"?"-"+s:""),L=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},k=Array.isArray(a)?a:[a],H=k.length>0,_={padding:L,boundary:k.filter(Zo),altBoundary:H},{refs:O,floatingStyles:$,placement:E,isPositioned:I,middlewareData:A}=jo({strategy:"fixed",placement:F,whileElementsMounted:(...Y)=>Ro(...Y,{animationFrame:h==="always"}),elements:{reference:x.anchor},middleware:[Ho({mainAxis:r+N,alignmentAxis:i}),l&&Bo({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?Vo():void 0,..._}),l&&Fo({..._}),$o({..._,apply:({elements:Y,rects:te,availableWidth:Te,availableHeight:Ee})=>{const{width:Oe,height:Rn}=te.reference,De=Y.floating.style;De.setProperty("--radix-popper-available-width",`${Te}px`),De.setProperty("--radix-popper-available-height",`${Ee}px`),De.setProperty("--radix-popper-anchor-width",`${Oe}px`),De.setProperty("--radix-popper-anchor-height",`${Rn}px`)}}),b&&zo({element:b,padding:c}),Jo({arrowWidth:R,arrowHeight:N}),w&&Wo({strategy:"referenceHidden",..._})]}),[B,K]=Jt(E),se=Et(g);J(()=>{I&&(se==null||se())},[I,se]);const Pe=(P=A.arrow)==null?void 0:P.x,Re=(V=A.arrow)==null?void 0:V.y,ae=((U=A.arrow)==null?void 0:U.centerOffset)!==0,[we,he]=u.useState();return J(()=>{S&&he(window.getComputedStyle(S).zIndex)},[S]),y.jsx("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:I?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:we,"--radix-popper-transform-origin":[(j=A.transformOrigin)==null?void 0:j.x,(M=A.transformOrigin)==null?void 0:M.y].join(" "),...((D=A.hide)==null?void 0:D.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:y.jsx(Xo,{scope:n,placedSide:B,onArrowChange:C,arrowX:Pe,arrowY:Re,shouldHideArrow:ae,children:y.jsx(z.div,{"data-side":B,"data-align":K,...d,ref:v,style:{...d.style,animation:I?void 0:"none"}})})})});qt.displayName=mt;var Gt="PopperArrow",Go={top:"bottom",right:"left",bottom:"top",left:"right"},Zt=u.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,s=qo(Gt,o),i=Go[s.placedSide];return y.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:y.jsx(Ko,{...r,ref:n,style:{...r.style,display:"block"}})})});Zt.displayName=Gt;function Zo(e){return e!==null}var Jo=e=>({name:"transformOrigin",options:e,fn(t){var x,S,m;const{placement:n,rects:o,middlewareData:r}=t,i=((x=r.arrow)==null?void 0:x.centerOffset)!==0,c=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[a,f]=Jt(n),p={start:"0%",center:"50%",end:"100%"}[f],w=(((S=r.arrow)==null?void 0:S.x)??0)+c/2,h=(((m=r.arrow)==null?void 0:m.y)??0)+l/2;let g="",d="";return a==="bottom"?(g=i?p:`${w}px`,d=`${-l}px`):a==="top"?(g=i?p:`${w}px`,d=`${o.floating.height+l}px`):a==="right"?(g=`${-l}px`,d=i?p:`${h}px`):a==="left"&&(g=`${o.floating.width+l}px`,d=i?p:`${h}px`),{data:{x:g,y:d}}}});function Jt(e){const[t,n="center"]=e.split("-");return[t,n]}var Qo=Kt,er=Xt,tr=qt,nr=Zt,or=[" ","Enter","ArrowUp","ArrowDown"],rr=[" ","Enter"],ge="Select",[Ke,Ye,ir]=On(ge),[Ae,Kr]=Tt(ge,[ir,zt]),Xe=zt(),[sr,ue]=Ae(ge),[cr,lr]=Ae(ge),Qt=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:r,onOpenChange:s,value:i,defaultValue:c,onValueChange:l,dir:a,name:f,autoComplete:p,disabled:w,required:h,form:g}=e,d=Xe(t),[x,S]=u.useState(null),[m,v]=u.useState(null),[b,C]=u.useState(!1),T=En(a),[R,N]=xt({prop:o,defaultProp:r??!1,onChange:s,caller:ge}),[F,L]=xt({prop:i,defaultProp:c,onChange:l,caller:ge}),k=u.useRef(null),H=x?g||!!x.closest("form"):!0,[_,O]=u.useState(new Set),$=Array.from(_).map(E=>E.props.value).join(";");return y.jsx(Qo,{...d,children:y.jsxs(sr,{required:h,scope:t,trigger:x,onTriggerChange:S,valueNode:m,onValueNodeChange:v,valueNodeHasChildren:b,onValueNodeHasChildrenChange:C,contentId:st(),value:F,onValueChange:L,open:R,onOpenChange:N,dir:T,triggerPointerDownPosRef:k,disabled:w,children:[y.jsx(Ke.Provider,{scope:t,children:y.jsx(cr,{scope:e.__scopeSelect,onNativeOptionAdd:u.useCallback(E=>{O(I=>new Set(I).add(E))},[]),onNativeOptionRemove:u.useCallback(E=>{O(I=>{const A=new Set(I);return A.delete(E),A})},[]),children:n})}),H?y.jsxs(bn,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:F,onChange:E=>L(E.target.value),disabled:w,form:g,children:[F===void 0?y.jsx("option",{value:""}):null,Array.from(_)]},$):null]})})};Qt.displayName=ge;var en="SelectTrigger",tn=u.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...r}=e,s=Xe(n),i=ue(en,n),c=i.disabled||o,l=G(t,i.onTriggerChange),a=Ye(n),f=u.useRef("touch"),[p,w,h]=An(d=>{const x=a().filter(v=>!v.disabled),S=x.find(v=>v.value===i.value),m=Pn(x,d,S);m!==void 0&&i.onValueChange(m.value)}),g=d=>{c||(i.onOpenChange(!0),h()),d&&(i.triggerPointerDownPosRef.current={x:Math.round(d.pageX),y:Math.round(d.pageY)})};return y.jsx(er,{asChild:!0,...s,children:y.jsx(z.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":Cn(i.value)?"":void 0,...r,ref:l,onClick:W(r.onClick,d=>{d.currentTarget.focus(),f.current!=="mouse"&&g(d)}),onPointerDown:W(r.onPointerDown,d=>{f.current=d.pointerType;const x=d.target;x.hasPointerCapture(d.pointerId)&&x.releasePointerCapture(d.pointerId),d.button===0&&d.ctrlKey===!1&&d.pointerType==="mouse"&&(g(d),d.preventDefault())}),onKeyDown:W(r.onKeyDown,d=>{const x=p.current!=="";!(d.ctrlKey||d.altKey||d.metaKey)&&d.key.length===1&&w(d.key),!(x&&d.key===" ")&&or.includes(d.key)&&(g(),d.preventDefault())})})})});tn.displayName=en;var nn="SelectValue",on=u.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,children:s,placeholder:i="",...c}=e,l=ue(nn,n),{onValueNodeHasChildrenChange:a}=l,f=s!==void 0,p=G(t,l.onValueNodeChange);return J(()=>{a(f)},[a,f]),y.jsx(z.span,{...c,ref:p,style:{pointerEvents:"none"},children:Cn(l.value)?y.jsx(y.Fragment,{children:i}):s})});on.displayName=nn;var ar="SelectIcon",rn=u.forwardRef((e,t)=>{const{__scopeSelect:n,children:o,...r}=e;return y.jsx(z.span,{"aria-hidden":!0,...r,ref:t,children:o||"▼"})});rn.displayName=ar;var fr="SelectPortal",sn=e=>y.jsx(In,{asChild:!0,...e});sn.displayName=fr;var xe="SelectContent",cn=u.forwardRef((e,t)=>{const n=ue(xe,e.__scopeSelect),[o,r]=u.useState();if(J(()=>{r(new DocumentFragment)},[]),!n.open){const s=o;return s?it.createPortal(y.jsx(ln,{scope:e.__scopeSelect,children:y.jsx(Ke.Slot,{scope:e.__scopeSelect,children:y.jsx("div",{children:e.children})})}),s):null}return y.jsx(an,{...e,ref:t})});cn.displayName=xe;var Z=10,[ln,pe]=Ae(xe),dr="SelectContentImpl",ur=Tn("SelectContent.RemoveScroll"),an=u.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:s,onPointerDownOutside:i,side:c,sideOffset:l,align:a,alignOffset:f,arrowPadding:p,collisionBoundary:w,collisionPadding:h,sticky:g,hideWhenDetached:d,avoidCollisions:x,...S}=e,m=ue(xe,n),[v,b]=u.useState(null),[C,T]=u.useState(null),R=G(t,P=>b(P)),[N,F]=u.useState(null),[L,k]=u.useState(null),H=Ye(n),[_,O]=u.useState(!1),$=u.useRef(!1);u.useEffect(()=>{if(v)return Nn(v)},[v]),_n();const E=u.useCallback(P=>{const[V,...U]=H().map(D=>D.ref.current),[j]=U.slice(-1),M=document.activeElement;for(const D of P)if(D===M||(D==null||D.scrollIntoView({block:"nearest"}),D===V&&C&&(C.scrollTop=0),D===j&&C&&(C.scrollTop=C.scrollHeight),D==null||D.focus(),document.activeElement!==M))return},[H,C]),I=u.useCallback(()=>E([N,v]),[E,N,v]);u.useEffect(()=>{_&&I()},[_,I]);const{onOpenChange:A,triggerPointerDownPosRef:B}=m;u.useEffect(()=>{if(v){let P={x:0,y:0};const V=j=>{var M,D;P={x:Math.abs(Math.round(j.pageX)-(((M=B.current)==null?void 0:M.x)??0)),y:Math.abs(Math.round(j.pageY)-(((D=B.current)==null?void 0:D.y)??0))}},U=j=>{P.x<=10&&P.y<=10?j.preventDefault():v.contains(j.target)||A(!1),document.removeEventListener("pointermove",V),B.current=null};return B.current!==null&&(document.addEventListener("pointermove",V),document.addEventListener("pointerup",U,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",V),document.removeEventListener("pointerup",U,{capture:!0})}}},[v,A,B]),u.useEffect(()=>{const P=()=>A(!1);return window.addEventListener("blur",P),window.addEventListener("resize",P),()=>{window.removeEventListener("blur",P),window.removeEventListener("resize",P)}},[A]);const[K,se]=An(P=>{const V=H().filter(M=>!M.disabled),U=V.find(M=>M.ref.current===document.activeElement),j=Pn(V,P,U);j&&setTimeout(()=>j.ref.current.focus())}),Pe=u.useCallback((P,V,U)=>{const j=!$.current&&!U;(m.value!==void 0&&m.value===V||j)&&(F(P),j&&($.current=!0))},[m.value]),Re=u.useCallback(()=>v==null?void 0:v.focus(),[v]),ae=u.useCallback((P,V,U)=>{const j=!$.current&&!U;(m.value!==void 0&&m.value===V||j)&&k(P)},[m.value]),we=o==="popper"?et:fn,he=we===et?{side:c,sideOffset:l,align:a,alignOffset:f,arrowPadding:p,collisionBoundary:w,collisionPadding:h,sticky:g,hideWhenDetached:d,avoidCollisions:x}:{};return y.jsx(ln,{scope:n,content:v,viewport:C,onViewportChange:T,itemRefCallback:Pe,selectedItem:N,onItemLeave:Re,itemTextRefCallback:ae,focusSelectedItem:I,selectedItemText:L,position:o,isPositioned:_,searchRef:K,children:y.jsx(Mn,{as:ur,allowPinchZoom:!0,children:y.jsx(Dn,{asChild:!0,trapped:m.open,onMountAutoFocus:P=>{P.preventDefault()},onUnmountAutoFocus:W(r,P=>{var V;(V=m.trigger)==null||V.focus({preventScroll:!0}),P.preventDefault()}),children:y.jsx(Ln,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:P=>P.preventDefault(),onDismiss:()=>m.onOpenChange(!1),children:y.jsx(we,{role:"listbox",id:m.contentId,"data-state":m.open?"open":"closed",dir:m.dir,onContextMenu:P=>P.preventDefault(),...S,...he,onPlaced:()=>O(!0),ref:R,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:W(S.onKeyDown,P=>{const V=P.ctrlKey||P.altKey||P.metaKey;if(P.key==="Tab"&&P.preventDefault(),!V&&P.key.length===1&&se(P.key),["ArrowUp","ArrowDown","Home","End"].includes(P.key)){let j=H().filter(M=>!M.disabled).map(M=>M.ref.current);if(["ArrowUp","End"].includes(P.key)&&(j=j.slice().reverse()),["ArrowUp","ArrowDown"].includes(P.key)){const M=P.target,D=j.indexOf(M);j=j.slice(D+1)}setTimeout(()=>E(j)),P.preventDefault()}})})})})})})});an.displayName=dr;var pr="SelectItemAlignedPosition",fn=u.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...r}=e,s=ue(xe,n),i=pe(xe,n),[c,l]=u.useState(null),[a,f]=u.useState(null),p=G(t,R=>f(R)),w=Ye(n),h=u.useRef(!1),g=u.useRef(!0),{viewport:d,selectedItem:x,selectedItemText:S,focusSelectedItem:m}=i,v=u.useCallback(()=>{if(s.trigger&&s.valueNode&&c&&a&&d&&x&&S){const R=s.trigger.getBoundingClientRect(),N=a.getBoundingClientRect(),F=s.valueNode.getBoundingClientRect(),L=S.getBoundingClientRect();if(s.dir!=="rtl"){const M=L.left-N.left,D=F.left-M,Y=R.left-D,te=R.width+Y,Te=Math.max(te,N.width),Ee=window.innerWidth-Z,Oe=wt(D,[Z,Math.max(Z,Ee-Te)]);c.style.minWidth=te+"px",c.style.left=Oe+"px"}else{const M=N.right-L.right,D=window.innerWidth-F.right-M,Y=window.innerWidth-R.right-D,te=R.width+Y,Te=Math.max(te,N.width),Ee=window.innerWidth-Z,Oe=wt(D,[Z,Math.max(Z,Ee-Te)]);c.style.minWidth=te+"px",c.style.right=Oe+"px"}const k=w(),H=window.innerHeight-Z*2,_=d.scrollHeight,O=window.getComputedStyle(a),$=parseInt(O.borderTopWidth,10),E=parseInt(O.paddingTop,10),I=parseInt(O.borderBottomWidth,10),A=parseInt(O.paddingBottom,10),B=$+E+_+A+I,K=Math.min(x.offsetHeight*5,B),se=window.getComputedStyle(d),Pe=parseInt(se.paddingTop,10),Re=parseInt(se.paddingBottom,10),ae=R.top+R.height/2-Z,we=H-ae,he=x.offsetHeight/2,P=x.offsetTop+he,V=$+E+P,U=B-V;if(V<=ae){const M=k.length>0&&x===k[k.length-1].ref.current;c.style.bottom="0px";const D=a.clientHeight-d.offsetTop-d.offsetHeight,Y=Math.max(we,he+(M?Re:0)+D+I),te=V+Y;c.style.height=te+"px"}else{const M=k.length>0&&x===k[0].ref.current;c.style.top="0px";const Y=Math.max(ae,$+d.offsetTop+(M?Pe:0)+he)+U;c.style.height=Y+"px",d.scrollTop=V-ae+d.offsetTop}c.style.margin=`${Z}px 0`,c.style.minHeight=K+"px",c.style.maxHeight=H+"px",o==null||o(),requestAnimationFrame(()=>h.current=!0)}},[w,s.trigger,s.valueNode,c,a,d,x,S,s.dir,o]);J(()=>v(),[v]);const[b,C]=u.useState();J(()=>{a&&C(window.getComputedStyle(a).zIndex)},[a]);const T=u.useCallback(R=>{R&&g.current===!0&&(v(),m==null||m(),g.current=!1)},[v,m]);return y.jsx(mr,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:T,children:y.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:b},children:y.jsx(z.div,{...r,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});fn.displayName=pr;var hr="SelectPopperPosition",et=u.forwardRef((e,t)=>{const{__scopeSelect:n,align:o="start",collisionPadding:r=Z,...s}=e,i=Xe(n);return y.jsx(tr,{...i,...s,ref:t,align:o,collisionPadding:r,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});et.displayName=hr;var[mr,gt]=Ae(xe,{}),tt="SelectViewport",dn=u.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...r}=e,s=pe(tt,n),i=gt(tt,n),c=G(t,s.onViewportChange),l=u.useRef(0);return y.jsxs(y.Fragment,{children:[y.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),y.jsx(Ke.Slot,{scope:n,children:y.jsx(z.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:W(r.onScroll,a=>{const f=a.currentTarget,{contentWrapper:p,shouldExpandOnScrollRef:w}=i;if(w!=null&&w.current&&p){const h=Math.abs(l.current-f.scrollTop);if(h>0){const g=window.innerHeight-Z*2,d=parseFloat(p.style.minHeight),x=parseFloat(p.style.height),S=Math.max(d,x);if(S<g){const m=S+h,v=Math.min(g,m),b=m-v;p.style.height=v+"px",p.style.bottom==="0px"&&(f.scrollTop=b>0?b:0,p.style.justifyContent="flex-end")}}}l.current=f.scrollTop})})})]})});dn.displayName=tt;var un="SelectGroup",[gr,xr]=Ae(un),wr=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=st();return y.jsx(gr,{scope:n,id:r,children:y.jsx(z.div,{role:"group","aria-labelledby":r,...o,ref:t})})});wr.displayName=un;var pn="SelectLabel",vr=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=xr(pn,n);return y.jsx(z.div,{id:r.id,...o,ref:t})});vr.displayName=pn;var Fe="SelectItem",[yr,hn]=Ae(Fe),mn=u.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:r=!1,textValue:s,...i}=e,c=ue(Fe,n),l=pe(Fe,n),a=c.value===o,[f,p]=u.useState(s??""),[w,h]=u.useState(!1),g=G(t,m=>{var v;return(v=l.itemRefCallback)==null?void 0:v.call(l,m,o,r)}),d=st(),x=u.useRef("touch"),S=()=>{r||(c.onValueChange(o),c.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return y.jsx(yr,{scope:n,value:o,disabled:r,textId:d,isSelected:a,onItemTextChange:u.useCallback(m=>{p(v=>v||((m==null?void 0:m.textContent)??"").trim())},[]),children:y.jsx(Ke.ItemSlot,{scope:n,value:o,disabled:r,textValue:f,children:y.jsx(z.div,{role:"option","aria-labelledby":d,"data-highlighted":w?"":void 0,"aria-selected":a&&w,"data-state":a?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...i,ref:g,onFocus:W(i.onFocus,()=>h(!0)),onBlur:W(i.onBlur,()=>h(!1)),onClick:W(i.onClick,()=>{x.current!=="mouse"&&S()}),onPointerUp:W(i.onPointerUp,()=>{x.current==="mouse"&&S()}),onPointerDown:W(i.onPointerDown,m=>{x.current=m.pointerType}),onPointerMove:W(i.onPointerMove,m=>{var v;x.current=m.pointerType,r?(v=l.onItemLeave)==null||v.call(l):x.current==="mouse"&&m.currentTarget.focus({preventScroll:!0})}),onPointerLeave:W(i.onPointerLeave,m=>{var v;m.currentTarget===document.activeElement&&((v=l.onItemLeave)==null||v.call(l))}),onKeyDown:W(i.onKeyDown,m=>{var b;((b=l.searchRef)==null?void 0:b.current)!==""&&m.key===" "||(rr.includes(m.key)&&S(),m.key===" "&&m.preventDefault())})})})})});mn.displayName=Fe;var Ie="SelectItemText",gn=u.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,...s}=e,i=ue(Ie,n),c=pe(Ie,n),l=hn(Ie,n),a=lr(Ie,n),[f,p]=u.useState(null),w=G(t,S=>p(S),l.onItemTextChange,S=>{var m;return(m=c.itemTextRefCallback)==null?void 0:m.call(c,S,l.value,l.disabled)}),h=f==null?void 0:f.textContent,g=u.useMemo(()=>y.jsx("option",{value:l.value,disabled:l.disabled,children:h},l.value),[l.disabled,l.value,h]),{onNativeOptionAdd:d,onNativeOptionRemove:x}=a;return J(()=>(d(g),()=>x(g)),[d,x,g]),y.jsxs(y.Fragment,{children:[y.jsx(z.span,{id:l.textId,...s,ref:w}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?it.createPortal(s.children,i.valueNode):null]})});gn.displayName=Ie;var xn="SelectItemIndicator",wn=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return hn(xn,n).isSelected?y.jsx(z.span,{"aria-hidden":!0,...o,ref:t}):null});wn.displayName=xn;var nt="SelectScrollUpButton",vn=u.forwardRef((e,t)=>{const n=pe(nt,e.__scopeSelect),o=gt(nt,e.__scopeSelect),[r,s]=u.useState(!1),i=G(t,o.onScrollButtonChange);return J(()=>{if(n.viewport&&n.isPositioned){let c=function(){const a=l.scrollTop>0;s(a)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?y.jsx(Sn,{...e,ref:i,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop-l.offsetHeight)}}):null});vn.displayName=nt;var ot="SelectScrollDownButton",yn=u.forwardRef((e,t)=>{const n=pe(ot,e.__scopeSelect),o=gt(ot,e.__scopeSelect),[r,s]=u.useState(!1),i=G(t,o.onScrollButtonChange);return J(()=>{if(n.viewport&&n.isPositioned){let c=function(){const a=l.scrollHeight-l.clientHeight,f=Math.ceil(l.scrollTop)<a;s(f)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?y.jsx(Sn,{...e,ref:i,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop+l.offsetHeight)}}):null});yn.displayName=ot;var Sn=u.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...r}=e,s=pe("SelectScrollButton",n),i=u.useRef(null),c=Ye(n),l=u.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return u.useEffect(()=>()=>l(),[l]),J(()=>{var f;const a=c().find(p=>p.ref.current===document.activeElement);(f=a==null?void 0:a.ref.current)==null||f.scrollIntoView({block:"nearest"})},[c]),y.jsx(z.div,{"aria-hidden":!0,...r,ref:t,style:{flexShrink:0,...r.style},onPointerDown:W(r.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(o,50))}),onPointerMove:W(r.onPointerMove,()=>{var a;(a=s.onItemLeave)==null||a.call(s),i.current===null&&(i.current=window.setInterval(o,50))}),onPointerLeave:W(r.onPointerLeave,()=>{l()})})}),Sr="SelectSeparator",br=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return y.jsx(z.div,{"aria-hidden":!0,...o,ref:t})});br.displayName=Sr;var rt="SelectArrow",Cr=u.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=Xe(n),s=ue(rt,n),i=pe(rt,n);return s.open&&i.position==="popper"?y.jsx(nr,{...r,...o,ref:t}):null});Cr.displayName=rt;var Ar="SelectBubbleInput",bn=u.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{const r=u.useRef(null),s=G(o,r),i=Wn(t);return u.useEffect(()=>{const c=r.current;if(!c)return;const l=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==t&&f){const p=new Event("change",{bubbles:!0});f.call(c,t),c.dispatchEvent(p)}},[i,t]),y.jsx(z.select,{...n,style:{...It,...n.style},ref:s,defaultValue:t})});bn.displayName=Ar;function Cn(e){return e===""||e===void 0}function An(e){const t=Et(e),n=u.useRef(""),o=u.useRef(0),r=u.useCallback(i=>{const c=n.current+i;t(c),function l(a){n.current=a,window.clearTimeout(o.current),a!==""&&(o.current=window.setTimeout(()=>l(""),1e3))}(c)},[t]),s=u.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return u.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,s]}function Pn(e,t,n){const r=t.length>1&&Array.from(t).every(a=>a===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=Pr(e,Math.max(s,0));r.length===1&&(i=i.filter(a=>a!==n));const l=i.find(a=>a.textValue.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function Pr(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var Rr=Qt,Tr=tn,Er=on,Or=rn,Ir=sn,Nr=cn,_r=dn,Mr=mn,Dr=gn,Lr=wn,jr=vn,kr=yn;function Yr({...e}){return y.jsx(Rr,{"data-slot":"select",...e})}function Xr({...e}){return y.jsx(Er,{"data-slot":"select-value",...e})}function qr({className:e,children:t,...n}){return y.jsxs(Tr,{"data-slot":"select-trigger",className:ye("border-input focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&>span]:line-clamp-1 cursor-pointer",e),...n,children:[t,y.jsx(Or,{asChild:!0,children:y.jsx(Ot,{size:16,className:"text-muted-foreground/80 in-aria-invalid:text-destructive/80 shrink-0"})})]})}function Gr({className:e,children:t,position:n="popper",...o}){return y.jsx(Ir,{children:y.jsxs(Nr,{"data-slot":"select-content",className:ye("border-input bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-[min(24rem,var(--radix-select-content-available-height))] min-w-32 overflow-hidden rounded-md border shadow-lg [&_[role=group]]:py-1",n==="popper"&&"w-full min-w-[var(--radix-select-trigger-width)] data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...o,children:[y.jsx(Hr,{}),y.jsx(_r,{className:ye("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)]"),children:t}),y.jsx(Br,{})]})})}function Zr({className:e,children:t,...n}){return y.jsxs(Mr,{"data-slot":"select-item",className:ye("focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default items-center rounded py-1.5 ps-8 pe-2 text-sm outline-hidden select-none data-disabled:pointer-events-none data-disabled:opacity-50 cursor-pointer",e),...n,children:[y.jsx("span",{className:"absolute start-2 flex size-3.5 items-center justify-center",children:y.jsx(Lr,{children:y.jsx(kn,{size:16})})}),y.jsx(Dr,{children:t})]})}function Hr({className:e,...t}){return y.jsx(jr,{"data-slot":"select-scroll-up-button",className:ye("text-muted-foreground/80 flex cursor-default items-center justify-center py-1",e),...t,children:y.jsx(Vn,{size:16})})}function Br({className:e,...t}){return y.jsx(kr,{"data-slot":"select-scroll-down-button",className:ye("text-muted-foreground/80 flex cursor-default items-center justify-center py-1",e),...t,children:y.jsx(Ot,{size:16})})}export{er as A,tr as C,zr as P,Qo as R,Yr as S,nr as a,Ur as b,zt as c,qr as d,Xr as e,Gr as f,Zr as g,Ot as h,Vn as i,kn as j,zn as k,Wn as u};
