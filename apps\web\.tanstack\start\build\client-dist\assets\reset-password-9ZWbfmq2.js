import{u as g,R as N,a as v,r as l,t as u,j as e,L as y}from"./main-CiHxKC0e.js";import{u as b}from"./useForm-BpR3SQ7U.js";import{B as d}from"./button-mzA_gOdo.js";import{L as x,I as h}from"./label-C9y0GF5L.js";import{C as P,a as C,b as k,c as S,d as R}from"./card-BeF5Jhpw.js";import{u as B}from"./useMutation-C9Oj6nVm.js";import{C as F}from"./circle-check-big-DYr6Kv7b.js";import{C as L}from"./circle-x-6nZ_0hIQ.js";import{o as E,s as p}from"./types-BF4s_UBG.js";const z=function(){const m=g(),{token:n}=N.useSearch(),w=v(),[o,t]=l.useState("form"),[j,i]=l.useState(""),f=B({mutationFn:s=>w.email.resetPassword.mutate(s),onSuccess:()=>{t("success"),u.success("Password reset successfully!"),setTimeout(()=>{m({to:"/auth"})},2e3)},onError:s=>{t("error"),i(s.message||"Failed to reset password. The link may be expired or invalid."),u.error("Password reset failed")}}),r=b({defaultValues:{newPassword:"",confirmPassword:""},onSubmit:async({value:s})=>{if(!n){t("error"),i("Invalid reset link. No token provided.");return}t("loading"),f.mutate({token:n,newPassword:s.newPassword})},validators:{onSubmit:E({newPassword:p().min(8,"Password must be at least 8 characters"),confirmPassword:p()}).refine(s=>s.newPassword===s.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]})}});l.useEffect(()=>{n||(t("error"),i("Invalid reset link. No token provided."))},[n]);const c=()=>{m({to:"/auth"})};return o==="loading"?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:e.jsx("div",{className:"max-w-md w-full mx-auto p-6",children:e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx(y,{className:"h-12 w-12 animate-spin text-primary"})}),e.jsx("h1",{className:"text-2xl font-bold",children:"Resetting your password..."}),e.jsx("p",{className:"text-muted-foreground",children:"Please wait while we update your password."})]})})}):o==="success"?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:e.jsx("div",{className:"max-w-md w-full mx-auto p-6",children:e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx(F,{className:"h-12 w-12 text-green-500"})}),e.jsx("h1",{className:"text-2xl font-bold text-green-600",children:"Password Reset Successfully!"}),e.jsx("p",{className:"text-muted-foreground",children:"Your password has been updated. You can now sign in with your new password."}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"You will be redirected to the login page shortly..."}),e.jsx(d,{onClick:c,className:"w-full",children:"Go to Login"})]})})}):o==="error"?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:e.jsx("div",{className:"max-w-md w-full mx-auto p-6",children:e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx(L,{className:"h-12 w-12 text-red-500"})}),e.jsx("h1",{className:"text-2xl font-bold text-red-600",children:"Password Reset Failed"}),e.jsx("p",{className:"text-muted-foreground",children:j}),e.jsx("div",{className:"space-y-3",children:e.jsx(d,{onClick:c,className:"w-full",children:"Back to Login"})})]})})}):e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:e.jsxs(P,{className:"mx-auto w-full mt-10 max-w-md",children:[e.jsxs(C,{children:[e.jsx(k,{className:"text-2xl",children:"Reset Your Password"}),e.jsx(S,{children:"Enter your new password below"})]}),e.jsx(R,{children:e.jsxs("form",{onSubmit:s=>{s.preventDefault(),s.stopPropagation(),r.handleSubmit()},className:"space-y-6",children:[e.jsx("div",{children:e.jsx(r.Field,{name:"newPassword",children:s=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(x,{htmlFor:s.name,children:"New Password"}),e.jsx(h,{id:s.name,name:s.name,type:"password",placeholder:"Enter your new password",value:s.state.value,onBlur:s.handleBlur,onChange:a=>s.handleChange(a.target.value)}),s.state.meta.errors.map(a=>e.jsx("p",{className:"text-destructive text-sm",children:a==null?void 0:a.message},a==null?void 0:a.message))]})})}),e.jsx("div",{children:e.jsx(r.Field,{name:"confirmPassword",children:s=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(x,{htmlFor:s.name,children:"Confirm New Password"}),e.jsx(h,{id:s.name,name:s.name,type:"password",placeholder:"Confirm your new password",value:s.state.value,onBlur:s.handleBlur,onChange:a=>s.handleChange(a.target.value)}),s.state.meta.errors.map(a=>e.jsx("p",{className:"text-destructive text-sm",children:a==null?void 0:a.message},a==null?void 0:a.message))]})})}),e.jsx(r.Subscribe,{children:s=>e.jsx(d,{type:"submit",className:"w-full",disabled:!s.canSubmit||s.isSubmitting,children:s.isSubmitting?"Resetting Password...":"Reset Password"})}),e.jsx("div",{className:"text-center",children:e.jsx("button",{type:"button",onClick:c,className:"text-sm text-muted-foreground hover:text-primary underline",children:"Back to Login"})})]})})]})})};export{z as component};
