import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, X, User, Search } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { useTRPCClient } from "@/utils/trpc";

type TrackArtist = {
  artistId: string;
  role: "PRIMARY" | "FEATURING";
};

type Artist = {
  id: string;
  name: string;
  genre?: string;
};

interface ArtistSelectorProps {
  artists: TrackArtist[];
  onArtistsChange: (artists: TrackArtist[]) => void;
}

export function ArtistSelector({ artists, onArtistsChange }: ArtistSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const trpcClient = useTRPCClient();

  // Fetch artists for selection
  const { data: availableArtists, isLoading } = useQuery({
    queryKey: ["artists", "select", searchQuery],
    queryFn: async () => {
      const response = await trpcClient.artist.getAll.query({
        page: 1,
        limit: 50,
        search: searchQuery || undefined,
      });
      return response.artists;
    },
    enabled: isOpen,
  });

  // Get artist details for selected artists
  const { data: selectedArtistDetails } = useQuery({
    queryKey: ["artists", "details", artists.map(a => a.artistId)],
    queryFn: async () => {
      if (artists.length === 0) return [];
      
      const artistPromises = artists.map(async (artist) => {
        try {
          const response = await trpcClient.artist.getById.query({ id: artist.artistId });
          return response;
        } catch (error) {
          console.error(`Failed to fetch artist ${artist.artistId}:`, error);
          return null;
        }
      });
      
      const results = await Promise.all(artistPromises);
      return results.filter((artist): artist is Artist => artist !== null);
    },
    enabled: artists.length > 0,
  });

  const addArtist = (artistId: string, role: "PRIMARY" | "FEATURING" = "PRIMARY") => {
    // Check if artist is already added
    if (artists.some(a => a.artistId === artistId)) {
      toast.error("Artist is already added to this track");
      return;
    }

    const newArtists = [...artists, { artistId, role }];
    onArtistsChange(newArtists);
    setIsOpen(false);
    setSearchQuery("");
  };

  const removeArtist = (artistId: string) => {
    const newArtists = artists.filter(a => a.artistId !== artistId);
    onArtistsChange(newArtists);
  };

  const updateArtistRole = (artistId: string, role: "PRIMARY" | "FEATURING") => {
    const newArtists = artists.map(a => 
      a.artistId === artistId ? { ...a, role } : a
    );
    onArtistsChange(newArtists);
  };

  const getArtistName = (artistId: string) => {
    const artist = selectedArtistDetails?.find(a => a.id === artistId);
    return artist?.name || "Unknown Artist";
  };

  return (
    <div className="space-y-4">
      {/* Add Artist */}
      <div className="flex items-center space-x-2">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="justify-start">
              <Plus className="mr-2 h-4 w-4" />
              Add Artist
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="start">
            <Command>
              <CommandInput
                placeholder="Search artists..."
                value={searchQuery}
                onValueChange={setSearchQuery}
              />
              <CommandList>
                <CommandEmpty>
                  {isLoading ? "Loading..." : "No artists found."}
                </CommandEmpty>
                <CommandGroup>
                  {availableArtists?.map((artist) => (
                    <CommandItem
                      key={artist.id}
                      onSelect={() => addArtist(artist.id)}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <div>
                          <p className="font-medium">{artist.name}</p>
                          {artist.genre && (
                            <p className="text-xs text-muted-foreground">
                              {artist.genre}
                            </p>
                          )}
                        </div>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      {/* Selected Artists */}
      {artists.length > 0 && (
        <div className="space-y-2">
          <Label>Selected Artists ({artists.length})</Label>
          <div className="space-y-2">
            {artists.map((artist, index) => (
              <Card key={`${artist.artistId}-${index}`}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-primary/10 rounded">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{getArtistName(artist.artistId)}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Label htmlFor={`role-${artist.artistId}`} className="text-xs">
                            Role:
                          </Label>
                          <Select
                            value={artist.role}
                            onValueChange={(value: "PRIMARY" | "FEATURING") =>
                              updateArtistRole(artist.artistId, value)
                            }
                          >
                            <SelectTrigger className="h-7 w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="PRIMARY">Primary</SelectItem>
                              <SelectItem value="FEATURING">Featuring</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={artist.role === "PRIMARY" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {artist.role === "PRIMARY" ? "Primary" : "Featuring"}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeArtist(artist.artistId)}
                        className="text-destructive hover:text-destructive"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Validation Message */}
      {artists.length === 0 && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <User className="h-4 w-4" />
          <span>At least one artist is required</span>
        </div>
      )}

      {/* Role Distribution Info */}
      {artists.length > 0 && (
        <div className="text-xs text-muted-foreground">
          <p>
            Primary: {artists.filter(a => a.role === "PRIMARY").length} | 
            Featuring: {artists.filter(a => a.role === "FEATURING").length}
          </p>
        </div>
      )}
    </div>
  );
}
