import{r as g,u as ie,a as ae,t as u,j as e,L as k}from"./main-B9Fv5CdX.js";import{u as re}from"./useQuery-CFAncLHa.js";import{u as E}from"./useMutation-DGkS69KN.js";import{a as te}from"./auth-client-C8WifPV2.js";import{j as T,k as le,l as M,m as O,n as _,o as D,p as U}from"./dialog-iGlJJq5Q.js";import{B as f}from"./button-Ispz1G12.js";import{L as c,I as x}from"./label-CNQvdrLZ.js";import{G as ne,T as oe}from"./genre-select-BveQTmBa.js";import{S as q,d as R,e as Y,f as B,g as S,P as ce}from"./select-Cv6EF9My.js";import{T as de,C as me}from"./checkbox-Dk7Edl6C.js";import{C as ue}from"./country-dropdown-DE40VcIC.js";const V=[{value:"YOUTUBE",label:"YouTube"},{value:"SPOTIFY",label:"Spotify"},{value:"APPLE_MUSIC",label:"Apple Music"},{value:"SOUNDCLOUD",label:"SoundCloud"},{value:"AUDIOMACK",label:"Audiomack"}],$=["SPOTIFY","APPLE_MUSIC"];function Pe({children:z,onArtistCreated:G}){var A,F,L;const[b,v]=g.useState(!1),H=ie(),y=ae(),{data:m}=te.useSession(),K=((A=m==null?void 0:m.user)==null?void 0:A.role)==="admin",[a,d]=g.useState({name:"",instagram:"",biography:"",country:"",genre:"",labelId:"",identifiers:[],spotifyNoProfile:!1,appleMusicNoProfile:!1}),{data:N,refetch:Q,isLoading:J}=re({queryKey:["labelsForSelect"],queryFn:()=>y.label.getForSelect.query(),enabled:b}),[W,j]=g.useState(!1),[C,I]=g.useState(""),p=E({mutationFn:async()=>y.label.create.mutate({name:C.trim()}),onSuccess:async s=>{u.success("Label created successfully"),await Q(),d(i=>({...i,labelId:s.id})),I(""),j(!1)},onError:s=>{u.error(s.message||"Failed to create label")}});g.useEffect(()=>{if(b){const s=[];a.identifiers.some(i=>i.service==="SPOTIFY")||s.push({service:"SPOTIFY",identifier:""}),a.identifiers.some(i=>i.service==="APPLE_MUSIC")||s.push({service:"APPLE_MUSIC",identifier:""}),s.length>0&&d(i=>({...i,identifiers:[...i.identifiers,...s]}))}},[b]);const l=E({mutationFn:async s=>{const i=s.identifiers.filter(r=>r.identifier.trim()!=="");return y.artist.create.mutate({name:s.name,instagram:s.instagram||void 0,biography:s.biography||void 0,country:s.country||void 0,genre:s.genre||void 0,labelId:s.labelId||void 0,identifiers:i.length>0?i:void 0})},onSuccess:s=>{u.success("Artist created successfully"),v(!1),d({name:"",instagram:"",biography:"",country:"",genre:"",labelId:"",identifiers:[],spotifyNoProfile:!1,appleMusicNoProfile:!1}),G(),s!=null&&s.id&&H({to:"/dashboard/artist/$id",params:{id:s.id}})},onError:s=>{console.error("Failed to create artist:",s),u.error("Failed to create artist: "+(s.message||"Unknown error"))}}),X=async s=>{if(s.preventDefault(),!a.name.trim()){u.error("Artist name is required");return}const i=a.identifiers.find(t=>t.service==="SPOTIFY"),r=a.identifiers.find(t=>t.service==="APPLE_MUSIC");if(!a.spotifyNoProfile&&(!i||!i.identifier.trim())){u.error("Spotify identifier is required or mark as 'Create a new profile for me'");return}if(!a.appleMusicNoProfile&&(!r||!r.identifier.trim())){u.error("Apple Music identifier is required or mark as 'Create a new profile for me'");return}try{await l.mutateAsync({...a,name:a.name.trim(),instagram:a.instagram.trim(),biography:a.biography.trim(),country:a.country.trim(),genre:a.genre.trim()})}catch{}},h=(s,i)=>{d(r=>({...r,[s]:i}))},Z=()=>{const s=a.identifiers.map(r=>r.service),i=V.find(r=>!s.includes(r.value));if(!i){u.error("All available platforms have been added.");return}d(r=>({...r,identifiers:[...r.identifiers,{service:i.value,identifier:""}]}))},ee=s=>{const i=a.identifiers[s];if($.includes(i.service)){u.error(`${i.service.replace("_"," ")} is required. Use "I don't have profile" option instead.`);return}d(r=>({...r,identifiers:r.identifiers.filter((t,n)=>n!==s)}))},w=(s,i,r)=>{d(t=>({...t,identifiers:t.identifiers.map((n,P)=>P===s?{...n,[i]:r}:n)}))},se=(s,i)=>{const r=s==="SPOTIFY"?"spotifyNoProfile":"appleMusicNoProfile";d(t=>({...t,[r]:i})),d(i?t=>({...t,identifiers:t.identifiers.map(n=>n.service===s?{...n,identifier:"Create New"}:n)}):t=>({...t,identifiers:t.identifiers.map(n=>n.service===s?{...n,identifier:""}:n)}))};return e.jsxs(T,{open:b,onOpenChange:v,children:[e.jsx(le,{asChild:!0,children:z}),e.jsxs(M,{className:"sm:max-w-[1300px] max-h-[90vh] overflow-y-auto",children:[e.jsxs(O,{children:[e.jsx(_,{children:"Create New Artist"}),e.jsx(D,{children:"Add a new artist to your catalog. Spotify and Apple Music identifiers are required."})]}),e.jsxs("form",{onSubmit:X,className:"space-y-4",children:[e.jsx("div",{className:"space-y-4",children:K&&e.jsxs("div",{className:"space-y-2 md:col-span-2",children:[e.jsx(c,{htmlFor:"createdBy",children:"Created By"}),e.jsx(x,{id:"createdBy",value:`${(F=m==null?void 0:m.user)==null?void 0:F.email} | ${(L=m==null?void 0:m.user)==null?void 0:L.name}`,disabled:!0})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(c,{htmlFor:"name",className:"block",children:["Artist Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",role:"region","aria-live":"polite",children:"This is the name that will be displayed on the artist page, please make sure to use the correct name."}),e.jsx(x,{id:"name",placeholder:"Enter artist name",value:a.name,onChange:s=>h("name",s.target.value),required:!0,disabled:l.isPending})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"label",children:"Label"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:"Assign to a record label or create a new one."}),e.jsxs(q,{value:a.labelId,onValueChange:s=>{s==="__create__"?j(!0):h("labelId",s)},disabled:l.isPending||J,children:[e.jsx(R,{className:"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 cursor-pointer",children:e.jsx(Y,{placeholder:"Select label"})}),e.jsxs(B,{children:[N==null?void 0:N.map(s=>e.jsx(S,{value:s.id,children:s.name},s.id)),e.jsx(S,{value:"__create__",children:"+ Create new label"})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"genre",children:"Genre"}),e.jsx(ne,{value:a.genre,onValueChange:s=>h("genre",s),disabled:l.isPending,placeholder:"Select a genre"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"country",children:"Country"}),e.jsx(ue,{defaultValue:a.country,onChange:s=>h("country",s.name),disabled:l.isPending})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"instagram",children:"Instagram Handle"}),e.jsx(x,{id:"instagram",placeholder:"username (without @)",value:a.instagram,onChange:s=>h("instagram",s.target.value),disabled:l.isPending})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"biography",children:"Biography"}),e.jsx(oe,{id:"biography",placeholder:"Brief artist biography...",value:a.biography,onChange:s=>h("biography",s.target.value),disabled:l.isPending,rows:3})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6 mt-6",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs(c,{className:"block",children:["Platform Identifiers ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",role:"region","aria-live":"polite",children:"Add the platform identifiers for the artist. If you don't have a profile on a platform, we will create a new profile for you."})]}),e.jsxs(f,{type:"button",variant:"outline",size:"sm",onClick:Z,disabled:l.isPending,children:[e.jsx(ce,{className:"h-4 w-4"}),"Add Platform"]})]}),a.identifiers.map((s,i)=>{const r=$.includes(s.service),t=s.service==="SPOTIFY"&&a.spotifyNoProfile||s.service==="APPLE_MUSIC"&&a.appleMusicNoProfile,n=a.identifiers.map(o=>o.service),P=V.filter(o=>!n.includes(o.value)||o.value===s.service);return e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex flex-col md:flex-row gap-2 md:items-end",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs(c,{className:"mb-2 block",children:["Platform"," ",r&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(q,{value:s.service,onValueChange:o=>w(i,"service",o),disabled:l.isPending||r,children:[e.jsx(R,{children:e.jsx(Y,{})}),e.jsx(B,{children:P.map(o=>e.jsx(S,{value:o.value,children:o.label},o.value))})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsxs(c,{className:"mb-2 block",children:["Artist Page URL"," ",r&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(x,{placeholder:"Artist Page URL",value:s.identifier,onChange:o=>w(i,"identifier",o.target.value),disabled:l.isPending||t})]}),e.jsx(f,{type:"button",variant:"outline",size:"sm",onClick:()=>ee(i),disabled:l.isPending||r,children:e.jsx(de,{className:"h-4 w-4"})})]}),r&&e.jsxs("div",{className:"flex items-center space-x-3 mt-2",children:[e.jsx(me,{id:`no-profile-${s.service}`,checked:t,onCheckedChange:o=>se(s.service,!!o),disabled:l.isPending}),e.jsxs(c,{htmlFor:`no-profile-${s.service}`,className:"text-sm",children:["Create a new profile on"," ",s.service.replace("_"," ")," for me"]})]})]},i)})]}),e.jsxs(U,{children:[e.jsx(f,{type:"button",variant:"outline",onClick:()=>v(!1),disabled:l.isPending,children:"Cancel"}),e.jsx(f,{type:"submit",disabled:l.isPending||!a.name.trim(),children:l.isPending?e.jsxs(e.Fragment,{children:[e.jsx(k,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create Artist"})]})]})]}),e.jsx(T,{open:W,onOpenChange:j,children:e.jsxs(M,{children:[e.jsxs(O,{children:[e.jsx(_,{children:"Create New Label"}),e.jsx(D,{children:"Add a new label to organize your artists and releases."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(c,{htmlFor:"labelName",className:"block",children:["Label Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",children:"This is the name that will be displayed on the label section, please make sure to use the correct name."}),e.jsx(x,{id:"labelName",placeholder:"Enter label name",value:C,onChange:s=>I(s.target.value),required:!0,disabled:p.isPending})]}),e.jsxs(U,{children:[e.jsx(f,{type:"button",variant:"outline",onClick:()=>j(!1),disabled:p.isPending,children:"Cancel"}),e.jsx(f,{type:"button",onClick:()=>p.mutate(),disabled:p.isPending||!C.trim(),children:p.isPending?e.jsx(k,{className:"h-4 w-4 animate-spin"}):"Create"})]})]})})]})}export{Pe as C};
