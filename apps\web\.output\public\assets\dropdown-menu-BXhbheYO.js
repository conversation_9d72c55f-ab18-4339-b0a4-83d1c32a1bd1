import{c as xn,r as s,j as r}from"./main-CiHxKC0e.js";import{c as xe,t as _n,e as re,q as Cn,P as q,f as Rn,b as g,v as bn,w as Dn,x as Sn,F as In,D as Pn,a as Y,u as _e}from"./dialog-B3qaM3HV.js";import{u as G,e as En,f as Ce,a as I}from"./button-mzA_gOdo.js";import{P as j,d as yn}from"./label-C9y0GF5L.js";import{c as Re,R as be,A as Nn,C as jn,a as Tn,j as An}from"./select-Be91QLyZ.js";import{c as De,I as On,R as kn}from"./scroll-area-ASo8OkdS.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ln=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Gn=xn("ChevronRight",Ln);var oe=["Enter"," "],Fn=["ArrowDown","PageUp","Home"],Se=["ArrowUp","PageDown","End"],Kn=[...Fn,...Se],$n={ltr:[...oe,"ArrowRight"],rtl:[...oe,"ArrowLeft"]},Un={ltr:["ArrowLeft"],rtl:["ArrowRight"]},F="Menu",[k,Bn,zn]=_n(F),[P,Ie]=xe(F,[zn,Re,De]),K=Re(),Pe=De(),[Ee,D]=P(F),[Vn,$]=P(F),ye=e=>{const{__scopeMenu:t,open:n=!1,children:o,dir:a,onOpenChange:c,modal:i=!0}=e,d=K(t),[m,v]=s.useState(null),p=s.useRef(!1),u=re(c),f=Cn(a);return s.useEffect(()=>{const h=()=>{p.current=!0,document.addEventListener("pointerdown",M,{capture:!0,once:!0}),document.addEventListener("pointermove",M,{capture:!0,once:!0})},M=()=>p.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",M,{capture:!0}),document.removeEventListener("pointermove",M,{capture:!0})}},[]),r.jsx(be,{...d,children:r.jsx(Ee,{scope:t,open:n,onOpenChange:u,content:m,onContentChange:v,children:r.jsx(Vn,{scope:t,onClose:s.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:p,dir:f,modal:i,children:o})})})};ye.displayName=F;var Xn="MenuAnchor",ae=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,a=K(n);return r.jsx(Nn,{...a,...o,ref:t})});ae.displayName=Xn;var se="MenuPortal",[Yn,Ne]=P(se,{forceMount:void 0}),je=e=>{const{__scopeMenu:t,forceMount:n,children:o,container:a}=e,c=D(se,t);return r.jsx(Yn,{scope:t,forceMount:n,children:r.jsx(q,{present:n||c.open,children:r.jsx(Rn,{asChild:!0,container:a,children:o})})})};je.displayName=se;var _="MenuContent",[Hn,ce]=P(_),Te=s.forwardRef((e,t)=>{const n=Ne(_,e.__scopeMenu),{forceMount:o=n.forceMount,...a}=e,c=D(_,e.__scopeMenu),i=$(_,e.__scopeMenu);return r.jsx(k.Provider,{scope:e.__scopeMenu,children:r.jsx(q,{present:o||c.open,children:r.jsx(k.Slot,{scope:e.__scopeMenu,children:i.modal?r.jsx(Wn,{...a,ref:t}):r.jsx(qn,{...a,ref:t})})})})}),Wn=s.forwardRef((e,t)=>{const n=D(_,e.__scopeMenu),o=s.useRef(null),a=G(t,o);return s.useEffect(()=>{const c=o.current;if(c)return bn(c)},[]),r.jsx(ue,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:g(e.onFocusOutside,c=>c.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),qn=s.forwardRef((e,t)=>{const n=D(_,e.__scopeMenu);return r.jsx(ue,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Zn=En("MenuContent.ScrollLock"),ue=s.forwardRef((e,t)=>{const{__scopeMenu:n,loop:o=!1,trapFocus:a,onOpenAutoFocus:c,onCloseAutoFocus:i,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:p,onFocusOutside:u,onInteractOutside:f,onDismiss:h,disableOutsideScroll:M,...S}=e,E=D(_,n),T=$(_,n),U=K(n),B=Pe(n),fe=Bn(n),[mn,me]=s.useState(null),z=s.useRef(null),vn=G(t,z,E.onContentChange),V=s.useRef(0),X=s.useRef(""),gn=s.useRef(0),Q=s.useRef(null),ve=s.useRef("right"),ee=s.useRef(0),Mn=M?Sn:s.Fragment,hn=M?{as:Zn,allowPinchZoom:!0}:void 0,wn=l=>{var N,Me;const x=X.current+l,C=fe().filter(R=>!R.disabled),b=document.activeElement,ne=(N=C.find(R=>R.ref.current===b))==null?void 0:N.textValue,te=C.map(R=>R.textValue),ge=it(te,x,ne),A=(Me=C.find(R=>R.textValue===ge))==null?void 0:Me.ref.current;(function R(he){X.current=he,window.clearTimeout(V.current),he!==""&&(V.current=window.setTimeout(()=>R(""),1e3))})(x),A&&setTimeout(()=>A.focus())};s.useEffect(()=>()=>window.clearTimeout(V.current),[]),Dn();const y=s.useCallback(l=>{var C,b;return ve.current===((C=Q.current)==null?void 0:C.side)&&lt(l,(b=Q.current)==null?void 0:b.area)},[]);return r.jsx(Hn,{scope:n,searchRef:X,onItemEnter:s.useCallback(l=>{y(l)&&l.preventDefault()},[y]),onItemLeave:s.useCallback(l=>{var x;y(l)||((x=z.current)==null||x.focus(),me(null))},[y]),onTriggerLeave:s.useCallback(l=>{y(l)&&l.preventDefault()},[y]),pointerGraceTimerRef:gn,onPointerGraceIntentChange:s.useCallback(l=>{Q.current=l},[]),children:r.jsx(Mn,{...hn,children:r.jsx(In,{asChild:!0,trapped:a,onMountAutoFocus:g(c,l=>{var x;l.preventDefault(),(x=z.current)==null||x.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:r.jsx(Pn,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:v,onPointerDownOutside:p,onFocusOutside:u,onInteractOutside:f,onDismiss:h,children:r.jsx(kn,{asChild:!0,...B,dir:T.dir,orientation:"vertical",loop:o,currentTabStopId:mn,onCurrentTabStopIdChange:me,onEntryFocus:g(m,l=>{T.isUsingKeyboardRef.current||l.preventDefault()}),preventScrollOnEntryFocus:!0,children:r.jsx(jn,{role:"menu","aria-orientation":"vertical","data-state":qe(E.open),"data-radix-menu-content":"",dir:T.dir,...U,...S,ref:vn,style:{outline:"none",...S.style},onKeyDown:g(S.onKeyDown,l=>{const C=l.target.closest("[data-radix-menu-content]")===l.currentTarget,b=l.ctrlKey||l.altKey||l.metaKey,ne=l.key.length===1;C&&(l.key==="Tab"&&l.preventDefault(),!b&&ne&&wn(l.key));const te=z.current;if(l.target!==te||!Kn.includes(l.key))return;l.preventDefault();const A=fe().filter(N=>!N.disabled).map(N=>N.ref.current);Se.includes(l.key)&&A.reverse(),ct(A)}),onBlur:g(e.onBlur,l=>{l.currentTarget.contains(l.target)||(window.clearTimeout(V.current),X.current="")}),onPointerMove:g(e.onPointerMove,L(l=>{const x=l.target,C=ee.current!==l.clientX;if(l.currentTarget.contains(x)&&C){const b=l.clientX>ee.current?"right":"left";ve.current=b,ee.current=l.clientX}}))})})})})})})});Te.displayName=_;var Jn="MenuGroup",ie=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return r.jsx(j.div,{role:"group",...o,ref:t})});ie.displayName=Jn;var Qn="MenuLabel",Ae=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return r.jsx(j.div,{...o,ref:t})});Ae.displayName=Qn;var H="MenuItem",we="menu.itemSelect",Z=s.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:o,...a}=e,c=s.useRef(null),i=$(H,e.__scopeMenu),d=ce(H,e.__scopeMenu),m=G(t,c),v=s.useRef(!1),p=()=>{const u=c.current;if(!n&&u){const f=new CustomEvent(we,{bubbles:!0,cancelable:!0});u.addEventListener(we,h=>o==null?void 0:o(h),{once:!0}),yn(u,f),f.defaultPrevented?v.current=!1:i.onClose()}};return r.jsx(Oe,{...a,ref:m,disabled:n,onClick:g(e.onClick,p),onPointerDown:u=>{var f;(f=e.onPointerDown)==null||f.call(e,u),v.current=!0},onPointerUp:g(e.onPointerUp,u=>{var f;v.current||(f=u.currentTarget)==null||f.click()}),onKeyDown:g(e.onKeyDown,u=>{const f=d.searchRef.current!=="";n||f&&u.key===" "||oe.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});Z.displayName=H;var Oe=s.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:o=!1,textValue:a,...c}=e,i=ce(H,n),d=Pe(n),m=s.useRef(null),v=G(t,m),[p,u]=s.useState(!1),[f,h]=s.useState("");return s.useEffect(()=>{const M=m.current;M&&h((M.textContent??"").trim())},[c.children]),r.jsx(k.ItemSlot,{scope:n,disabled:o,textValue:a??f,children:r.jsx(On,{asChild:!0,...d,focusable:!o,children:r.jsx(j.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...c,ref:v,onPointerMove:g(e.onPointerMove,L(M=>{o?i.onItemLeave(M):(i.onItemEnter(M),M.defaultPrevented||M.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:g(e.onPointerLeave,L(M=>i.onItemLeave(M))),onFocus:g(e.onFocus,()=>u(!0)),onBlur:g(e.onBlur,()=>u(!1))})})})}),et="MenuCheckboxItem",ke=s.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:o,...a}=e;return r.jsx($e,{scope:e.__scopeMenu,checked:n,children:r.jsx(Z,{role:"menuitemcheckbox","aria-checked":W(n)?"mixed":n,...a,ref:t,"data-state":pe(n),onSelect:g(a.onSelect,()=>o==null?void 0:o(W(n)?!0:!n),{checkForDefaultPrevented:!1})})})});ke.displayName=et;var Le="MenuRadioGroup",[nt,tt]=P(Le,{value:void 0,onValueChange:()=>{}}),Ge=s.forwardRef((e,t)=>{const{value:n,onValueChange:o,...a}=e,c=re(o);return r.jsx(nt,{scope:e.__scopeMenu,value:n,onValueChange:c,children:r.jsx(ie,{...a,ref:t})})});Ge.displayName=Le;var Fe="MenuRadioItem",Ke=s.forwardRef((e,t)=>{const{value:n,...o}=e,a=tt(Fe,e.__scopeMenu),c=n===a.value;return r.jsx($e,{scope:e.__scopeMenu,checked:c,children:r.jsx(Z,{role:"menuitemradio","aria-checked":c,...o,ref:t,"data-state":pe(c),onSelect:g(o.onSelect,()=>{var i;return(i=a.onValueChange)==null?void 0:i.call(a,n)},{checkForDefaultPrevented:!1})})})});Ke.displayName=Fe;var de="MenuItemIndicator",[$e,ot]=P(de,{checked:!1}),Ue=s.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:o,...a}=e,c=ot(de,n);return r.jsx(q,{present:o||W(c.checked)||c.checked===!0,children:r.jsx(j.span,{...a,ref:t,"data-state":pe(c.checked)})})});Ue.displayName=de;var rt="MenuSeparator",Be=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return r.jsx(j.div,{role:"separator","aria-orientation":"horizontal",...o,ref:t})});Be.displayName=rt;var at="MenuArrow",ze=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,a=K(n);return r.jsx(Tn,{...a,...o,ref:t})});ze.displayName=at;var le="MenuSub",[st,Ve]=P(le),Xe=e=>{const{__scopeMenu:t,children:n,open:o=!1,onOpenChange:a}=e,c=D(le,t),i=K(t),[d,m]=s.useState(null),[v,p]=s.useState(null),u=re(a);return s.useEffect(()=>(c.open===!1&&u(!1),()=>u(!1)),[c.open,u]),r.jsx(be,{...i,children:r.jsx(Ee,{scope:t,open:o,onOpenChange:u,content:v,onContentChange:p,children:r.jsx(st,{scope:t,contentId:Y(),triggerId:Y(),trigger:d,onTriggerChange:m,children:n})})})};Xe.displayName=le;var O="MenuSubTrigger",Ye=s.forwardRef((e,t)=>{const n=D(O,e.__scopeMenu),o=$(O,e.__scopeMenu),a=Ve(O,e.__scopeMenu),c=ce(O,e.__scopeMenu),i=s.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:m}=c,v={__scopeMenu:e.__scopeMenu},p=s.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return s.useEffect(()=>p,[p]),s.useEffect(()=>{const u=d.current;return()=>{window.clearTimeout(u),m(null)}},[d,m]),r.jsx(ae,{asChild:!0,...v,children:r.jsx(Oe,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":qe(n.open),...e,ref:Ce(t,a.onTriggerChange),onClick:u=>{var f;(f=e.onClick)==null||f.call(e,u),!(e.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:g(e.onPointerMove,L(u=>{c.onItemEnter(u),!u.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(c.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:g(e.onPointerLeave,L(u=>{var h,M;p();const f=(h=n.content)==null?void 0:h.getBoundingClientRect();if(f){const S=(M=n.content)==null?void 0:M.dataset.side,E=S==="right",T=E?-5:5,U=f[E?"left":"right"],B=f[E?"right":"left"];c.onPointerGraceIntentChange({area:[{x:u.clientX+T,y:u.clientY},{x:U,y:f.top},{x:B,y:f.top},{x:B,y:f.bottom},{x:U,y:f.bottom}],side:S}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(u),u.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:g(e.onKeyDown,u=>{var h;const f=c.searchRef.current!=="";e.disabled||f&&u.key===" "||$n[o.dir].includes(u.key)&&(n.onOpenChange(!0),(h=n.content)==null||h.focus(),u.preventDefault())})})})});Ye.displayName=O;var He="MenuSubContent",We=s.forwardRef((e,t)=>{const n=Ne(_,e.__scopeMenu),{forceMount:o=n.forceMount,...a}=e,c=D(_,e.__scopeMenu),i=$(_,e.__scopeMenu),d=Ve(He,e.__scopeMenu),m=s.useRef(null),v=G(t,m);return r.jsx(k.Provider,{scope:e.__scopeMenu,children:r.jsx(q,{present:o||c.open,children:r.jsx(k.Slot,{scope:e.__scopeMenu,children:r.jsx(ue,{id:d.contentId,"aria-labelledby":d.triggerId,...a,ref:v,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:p=>{var u;i.isUsingKeyboardRef.current&&((u=m.current)==null||u.focus()),p.preventDefault()},onCloseAutoFocus:p=>p.preventDefault(),onFocusOutside:g(e.onFocusOutside,p=>{p.target!==d.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:g(e.onEscapeKeyDown,p=>{i.onClose(),p.preventDefault()}),onKeyDown:g(e.onKeyDown,p=>{var h;const u=p.currentTarget.contains(p.target),f=Un[i.dir].includes(p.key);u&&f&&(c.onOpenChange(!1),(h=d.trigger)==null||h.focus(),p.preventDefault())})})})})})});We.displayName=He;function qe(e){return e?"open":"closed"}function W(e){return e==="indeterminate"}function pe(e){return W(e)?"indeterminate":e?"checked":"unchecked"}function ct(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function ut(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function it(e,t,n){const a=t.length>1&&Array.from(t).every(v=>v===t[0])?t[0]:t,c=n?e.indexOf(n):-1;let i=ut(e,Math.max(c,0));a.length===1&&(i=i.filter(v=>v!==n));const m=i.find(v=>v.toLowerCase().startsWith(a.toLowerCase()));return m!==n?m:void 0}function dt(e,t){const{x:n,y:o}=e;let a=!1;for(let c=0,i=t.length-1;c<t.length;i=c++){const d=t[c],m=t[i],v=d.x,p=d.y,u=m.x,f=m.y;p>o!=f>o&&n<(u-v)*(o-p)/(f-p)+v&&(a=!a)}return a}function lt(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return dt(n,t)}function L(e){return t=>t.pointerType==="mouse"?e(t):void 0}var pt=ye,ft=ae,mt=je,vt=Te,gt=ie,Mt=Ae,ht=Z,wt=ke,xt=Ge,_t=Ke,Ct=Ue,Rt=Be,bt=ze,Dt=Xe,St=Ye,It=We,J="DropdownMenu",[Pt,lo]=xe(J,[Ie]),w=Ie(),[Et,Ze]=Pt(J),Je=e=>{const{__scopeDropdownMenu:t,children:n,dir:o,open:a,defaultOpen:c,onOpenChange:i,modal:d=!0}=e,m=w(t),v=s.useRef(null),[p,u]=_e({prop:a,defaultProp:c??!1,onChange:i,caller:J});return r.jsx(Et,{scope:t,triggerId:Y(),triggerRef:v,contentId:Y(),open:p,onOpenChange:u,onOpenToggle:s.useCallback(()=>u(f=>!f),[u]),modal:d,children:r.jsx(pt,{...m,open:p,onOpenChange:u,dir:o,modal:d,children:n})})};Je.displayName=J;var Qe="DropdownMenuTrigger",en=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:o=!1,...a}=e,c=Ze(Qe,n),i=w(n);return r.jsx(ft,{asChild:!0,...i,children:r.jsx(j.button,{type:"button",id:c.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":c.open?c.contentId:void 0,"data-state":c.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...a,ref:Ce(t,c.triggerRef),onPointerDown:g(e.onPointerDown,d=>{!o&&d.button===0&&d.ctrlKey===!1&&(c.onOpenToggle(),c.open||d.preventDefault())}),onKeyDown:g(e.onKeyDown,d=>{o||(["Enter"," "].includes(d.key)&&c.onOpenToggle(),d.key==="ArrowDown"&&c.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(d.key)&&d.preventDefault())})})})});en.displayName=Qe;var yt="DropdownMenuPortal",nn=e=>{const{__scopeDropdownMenu:t,...n}=e,o=w(t);return r.jsx(mt,{...o,...n})};nn.displayName=yt;var tn="DropdownMenuContent",on=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=Ze(tn,n),c=w(n),i=s.useRef(!1);return r.jsx(vt,{id:a.contentId,"aria-labelledby":a.triggerId,...c,...o,ref:t,onCloseAutoFocus:g(e.onCloseAutoFocus,d=>{var m;i.current||(m=a.triggerRef.current)==null||m.focus(),i.current=!1,d.preventDefault()}),onInteractOutside:g(e.onInteractOutside,d=>{const m=d.detail.originalEvent,v=m.button===0&&m.ctrlKey===!0,p=m.button===2||v;(!a.modal||p)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});on.displayName=tn;var Nt="DropdownMenuGroup",rn=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(gt,{...a,...o,ref:t})});rn.displayName=Nt;var jt="DropdownMenuLabel",an=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(Mt,{...a,...o,ref:t})});an.displayName=jt;var Tt="DropdownMenuItem",sn=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(ht,{...a,...o,ref:t})});sn.displayName=Tt;var At="DropdownMenuCheckboxItem",cn=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(wt,{...a,...o,ref:t})});cn.displayName=At;var Ot="DropdownMenuRadioGroup",kt=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(xt,{...a,...o,ref:t})});kt.displayName=Ot;var Lt="DropdownMenuRadioItem",Gt=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(_t,{...a,...o,ref:t})});Gt.displayName=Lt;var Ft="DropdownMenuItemIndicator",un=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(Ct,{...a,...o,ref:t})});un.displayName=Ft;var Kt="DropdownMenuSeparator",dn=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(Rt,{...a,...o,ref:t})});dn.displayName=Kt;var $t="DropdownMenuArrow",Ut=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(bt,{...a,...o,ref:t})});Ut.displayName=$t;var Bt=e=>{const{__scopeDropdownMenu:t,children:n,open:o,onOpenChange:a,defaultOpen:c}=e,i=w(t),[d,m]=_e({prop:o,defaultProp:c??!1,onChange:a,caller:"DropdownMenuSub"});return r.jsx(Dt,{...i,open:d,onOpenChange:m,children:n})},zt="DropdownMenuSubTrigger",ln=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(St,{...a,...o,ref:t})});ln.displayName=zt;var Vt="DropdownMenuSubContent",pn=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,a=w(n);return r.jsx(It,{...a,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});pn.displayName=Vt;var Xt=Je,Yt=en,fn=nn,Ht=on,Wt=rn,qt=an,Zt=sn,Jt=cn,Qt=un,eo=dn,no=Bt,to=ln,oo=pn;function po({...e}){return r.jsx(Xt,{"data-slot":"dropdown-menu",...e})}function fo({...e}){return r.jsx(fn,{"data-slot":"dropdown-menu-portal",...e})}function mo({...e}){return r.jsx(Yt,{"data-slot":"dropdown-menu-trigger",...e})}function vo({className:e,sideOffset:t=4,...n}){return r.jsx(fn,{children:r.jsx(Ht,{"data-slot":"dropdown-menu-content",sideOffset:t,className:I("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...n})})}function go({...e}){return r.jsx(Wt,{"data-slot":"dropdown-menu-group",...e})}function Mo({className:e,inset:t,variant:n="default",...o}){return r.jsx(Zt,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:I("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o})}function ho({className:e,children:t,checked:n,...o}){return r.jsxs(Jt,{"data-slot":"dropdown-menu-checkbox-item",className:I("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:n,...o,children:[r.jsx("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:r.jsx(Qt,{children:r.jsx(An,{className:"size-4"})})}),t]})}function wo({className:e,inset:t,...n}){return r.jsx(qt,{"data-slot":"dropdown-menu-label","data-inset":t,className:I("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function xo({className:e,...t}){return r.jsx(eo,{"data-slot":"dropdown-menu-separator",className:I("bg-border -mx-1 my-1 h-px",e),...t})}function _o({...e}){return r.jsx(no,{"data-slot":"dropdown-menu-sub",...e})}function Co({className:e,inset:t,children:n,...o}){return r.jsxs(to,{"data-slot":"dropdown-menu-sub-trigger","data-inset":t,className:I("focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8",e),...o,children:[n,r.jsx(Gn,{className:"ml-auto size-4"})]})}function Ro({className:e,...t}){return r.jsx(oo,{"data-slot":"dropdown-menu-sub-content",className:I("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg",e),...t})}export{Gn as C,po as D,mo as a,vo as b,wo as c,xo as d,go as e,Mo as f,_o as g,Co as h,fo as i,Ro as j,ho as k};
