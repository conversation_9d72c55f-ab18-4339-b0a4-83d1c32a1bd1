import { protectedProcedure, publicProcedure, router } from "../lib/trpc";
import { emailRouter } from "./email";
import { artistRouter } from "./artist";
import { labelRouter } from "./label";
import { contributorRouter } from "./contributor";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  privateData: protectedProcedure.query(({ ctx }) => {
    return {
      message: "This is private",
      user: ctx.session.user,
    };
  }),
  email: emailRouter,
  artist: artistRouter,
  label: labelRouter,
  contributor: contributorRouter,
});
export type AppRouter = typeof appRouter;
