import { initEdgeStore } from "@edgestore/server";
import {
  createEdgeStoreHonoHandler,
  type CreateContextOptions,
} from "@edgestore/server/adapters/hono";
import { auth } from "@/lib/auth";
import { AWSProvider } from "@edgestore/server/providers/aws";

type Context = {
  userId: string;
  role: string;
};

async function createContext({ c }: CreateContextOptions): Promise<Context> {
  const session = await auth.api.getSession({
    headers: c.req.raw.headers,
  });

  if (!session) {
    throw new Error("Unauthorized");
  }

  return {
    userId: session?.user.id ?? "",
    role: session?.user.role ?? "",
  };
}

const es = initEdgeStore.context<Context>().create();

/**
 * This is the main router for the EdgeStore buckets.
 */
const edgeStoreRouter = es.router({
  audio: es
    .fileBucket({
      maxSize: 200 * 1024 * 1024,
      accept: [
        "audio/wav",
        "audio/mp3",
        "audio/mpeg",
        "audio/flac",
        "audio/aac",
        "audio/ogg",
        "audio/mp4",
        "audio/m4a",
      ],
    })
    .path(({ ctx }) => [{ owner: ctx.userId }])
    .beforeDelete(({ ctx, fileInfo }) => {
      console.log("beforeDelete", ctx, fileInfo);
      return true;
    })
    .accessControl({
      OR: [
        {
          userId: { path: "owner" },
        },
        {
          role: { eq: "admin" },
        },
      ],
    }),
}) as any;

export type EdgeStoreRouter = typeof edgeStoreRouter;

export const handler = createEdgeStoreHonoHandler({
  provider: AWSProvider({
    baseUrl: process.env.EDGE_STORE_BASE_URL,
    bucketName: process.env.ES_AWS_BUCKET_NAME,
    accessKeyId: process.env.ES_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.ES_AWS_SECRET_ACCESS_KEY,
    region: process.env.ES_AWS_REGION,
    endpoint: process.env.ES_AWS_ENDPOINT,
    jwtSecret: process.env.EDGE_STORE_JWT_SECRET,
    forcePathStyle: true,
    // overwritePath: ({ defaultAccessPath }) => {
    //   return defaultAccessPath.split("/_public/")[1];
    // },
  }),
  router: edgeStoreRouter,
  /**
   * The context is generated and saved to a cookie
   * in the first load of the page.
   */
  createContext,
});
