import{r as x,a as L,t as f,j as e,L as oe,g as ce,s as de,k as q}from"./main-B9Fv5CdX.js";import{a as E}from"./auth-client-C8WifPV2.js";import{E as me,A as _,a as O,b as Q,c as X,d as J,e as W,f as Y,g as Z,h as ee,u as ue,t as xe,v as he,w as ge,x as pe,y as je,C as fe,T as be,i as Ce,j as y,k as we,l as P,m as Ne,n as k,P as ve,o as ye,p as A,q as Se,r as De,s as ze}from"./table-gaCg9Yy-.js";import{u as ke}from"./useQuery-CFAncLHa.js";import{u as V}from"./useMutation-DGkS69KN.js";import{B as g,a as $}from"./button-Ispz1G12.js";import{D as se,a as te,b as ae,e as Ae,f as G,d as Ie,c as Me,k as Pe,C as Te}from"./dropdown-menu-DKdrXVD1.js";import{L as T,I as F}from"./label-CNQvdrLZ.js";import{P as Fe,h as Re,i as Le,S as Ee,d as Ve,e as Ue,f as He,g as Be}from"./select-Cv6EF9My.js";import{C as qe}from"./create-contributor-dialog-bHaIXYGw.js";import{T as ne,C as K}from"./checkbox-Dk7Edl6C.js";import{j as $e,k as Ge,l as Ke,m as _e,n as Oe,o as Qe,p as Xe}from"./dialog-iGlJJq5Q.js";import{S as Je}from"./square-pen-DzJw3_Uf.js";import{C as R}from"./circle-alert-DwqW4ucM.js";import{L as We}from"./list-filter-BY1QXxSs.js";import{C as Ye}from"./circle-x-Dhf4rEsb.js";import{U as Ze}from"./users-uEpTONEC.js";import"./scroll-area-BkIMWkxZ.js";function es({children:c,contributor:r,onContributorUpdated:l}){var S;const[t,i]=x.useState(!1),m=L(),{data:C}=E.useSession(),I=((S=C==null?void 0:C.user)==null?void 0:S.role)==="admin",[b,d]=x.useState({name:""});x.useEffect(()=>{t&&d({name:r.name||""})},[t,r]);const h=V({mutationFn:async o=>m.contributor.update.mutate(o),onSuccess:()=>{f.success("Contributor updated successfully"),i(!1),l()},onError:o=>{console.error("Failed to update contributor:",o),f.error("Failed to update contributor: "+(o.message||"Unknown error"))}}),w=async o=>{if(o.preventDefault(),!b.name.trim()){f.error("Contributor name is required");return}try{await h.mutateAsync({id:r.id,name:b.name.trim()})}catch{}},M=(o,N)=>{d(n=>({...n,[o]:N}))};return e.jsxs($e,{open:t,onOpenChange:i,children:[e.jsx(Ge,{asChild:!0,children:c}),e.jsxs(Ke,{className:"sm:max-w-[500px]",children:[e.jsxs(_e,{children:[e.jsxs(Oe,{children:["Edit Contributor: ",r.name]}),e.jsx(Qe,{children:"Update the contributor information."})]}),e.jsxs("form",{onSubmit:w,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[I&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(T,{htmlFor:"createdBy",children:"Created By"}),e.jsx(F,{id:"createdBy",value:`${r.user.email} | ${r.user.name}`,disabled:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(T,{htmlFor:"name",className:"block",children:["Contributor Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",role:"region","aria-live":"polite",children:"This is the name that will be displayed for the contributor. Please make sure to use the correct name."}),e.jsx(F,{id:"name",placeholder:"Enter contributor name",value:b.name,onChange:o=>M("name",o.target.value),required:!0,disabled:h.isPending})]})]}),e.jsxs(Xe,{children:[e.jsx(g,{type:"button",variant:"outline",onClick:()=>i(!1),disabled:h.isPending,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:h.isPending||!b.name.trim(),children:h.isPending?e.jsxs(e.Fragment,{children:[e.jsx(oe,{className:"h-4 w-4 animate-spin"}),"Updating..."]}):"Update Contributor"})]})]})]})]})}function ss({row:c,onContributorDeleted:r}){const l=L(),t=V({mutationFn:async()=>l.contributor.delete.mutate({id:c.original.id}),onSuccess:()=>{f.success(`Successfully deleted contributor ${c.original.name}`),r()},onError:m=>{console.error("Failed to delete contributor:",m),f.error("Failed to delete contributor: "+(m.message||"Unknown error"))}}),i=async()=>{try{await t.mutateAsync()}catch{}};return e.jsxs(se,{children:[e.jsx(te,{asChild:!0,children:e.jsx("div",{className:"flex justify-end",children:e.jsx(g,{size:"icon",variant:"ghost",className:"shadow-none cursor-pointer","aria-label":"Edit item",children:e.jsx(me,{size:16,"aria-hidden":"true"})})})}),e.jsxs(ae,{align:"end",children:[e.jsx(Ae,{children:e.jsx(es,{contributor:c.original,onContributorUpdated:r,children:e.jsxs(G,{onSelect:m=>m.preventDefault(),className:"cursor-pointer",children:[e.jsx(Je,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit Contributor"})]})})}),e.jsx(Ie,{className:"h-[2px]"}),e.jsxs(_,{children:[e.jsx(O,{asChild:!0,children:e.jsx(G,{className:"text-destructive focus:text-destructive cursor-pointer",onSelect:m=>m.preventDefault(),children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ne,{size:16,className:"text-destructive focus:text-destructive"}),e.jsx("span",{children:"Delete"})]})})}),e.jsxs(Q,{children:[e.jsxs("div",{className:"flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4",children:[e.jsx("div",{className:"flex size-9 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(R,{className:"opacity-80",size:16})}),e.jsxs(X,{children:[e.jsx(J,{children:"Delete Contributor"}),e.jsxs(W,{children:["Are you sure you want to delete"," ",e.jsx("strong",{children:c.original.name}),"? This action cannot be undone and will permanently remove the contributor."]})]})]}),e.jsxs(Y,{children:[e.jsx(Z,{children:"Cancel"}),e.jsx(ee,{onClick:i,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:t.isPending,children:t.isPending?"Deleting...":"Delete Contributor"})]})]})]})]})]})}const ts=(c,r,l)=>{const t=`${c.original.name}`.toLowerCase(),i=(l??"").toLowerCase();return t.includes(i)},as=(c,r)=>{const l=[{id:"select",header:({table:t})=>e.jsx(K,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:i=>t.toggleAllPageRowsSelected(!!i),"aria-label":"Select all"}),cell:({row:t})=>e.jsx(K,{checked:t.getIsSelected(),onCheckedChange:i=>t.toggleSelected(!!i),"aria-label":"Select row"}),size:28,enableSorting:!1,enableHiding:!1},{header:"Contributor Name",accessorKey:"name",cell:({row:t})=>e.jsx("div",{className:"font-medium",children:t.getValue("name")}),size:200,filterFn:ts,enableHiding:!1},{header:"Created At",accessorKey:"createdAt",cell:({row:t})=>{const i=new Date(t.getValue("createdAt"));return e.jsx("div",{className:"text-sm text-muted-foreground",children:i.toLocaleDateString()})},size:120}];return r&&l.push({header:"Created By",accessorKey:"user",cell:({row:t})=>{var i,m;return e.jsxs("div",{className:"text-sm min-w-0 truncate",children:[((i=t.original.user)==null?void 0:i.email)??"-"," | ",((m=t.original.user)==null?void 0:m.name)??"-"]})},size:200}),l.push({id:"actions",header:()=>e.jsx("span",{className:"sr-only",children:"Actions"}),cell:({row:t})=>e.jsx(ss,{row:t,onContributorDeleted:c}),size:80,enableHiding:!1}),l};function ns(){var H,B;const c=x.useId(),r=ce(),l=L(),{data:t}=E.useSession(),i=((H=t==null?void 0:t.user)==null?void 0:H.role)==="admin",[m,C]=x.useState([]),[I,b]=x.useState({}),[d,h]=x.useState({pageIndex:0,pageSize:10}),w=x.useRef(null),[M,S]=x.useState([{id:"name",desc:!1}]),[o,N]=x.useState(""),{data:n,isLoading:j,error:U,refetch:v}=ke(r.contributor.getAll.queryOptions({page:d.pageIndex+1,limit:d.pageSize,search:o||void 0})),ie=V({mutationFn:async s=>l.contributor.delete.mutate({id:s}),onSuccess:()=>{f.success("Contributor deleted successfully"),v()},onError:s=>{f.error(`Failed to delete contributor: ${s.message}`)}}),re=x.useMemo(()=>((n==null?void 0:n.contributors)||[]).map(a=>({...a,createdAt:new Date(a.createdAt),updatedAt:new Date(a.updatedAt)})),[n==null?void 0:n.contributors]),le=async()=>{const a=p.getSelectedRowModel().rows.map(u=>u.original.id);try{const u=a.map(z=>ie.mutateAsync(z));await Promise.all(u),p.resetRowSelection()}catch{}},D=x.useMemo(()=>as(v,i),[v,i]),p=ue({data:re,columns:D,getCoreRowModel:je(),getSortedRowModel:pe(),onSortingChange:S,enableSortingRemoval:!1,getPaginationRowModel:ge(),onPaginationChange:h,onColumnFiltersChange:C,onColumnVisibilityChange:b,getFilteredRowModel:he(),getFacetedUniqueValues:xe(),state:{sorting:M,pagination:d,columnFilters:m,columnVisibility:I}});return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(F,{id:`${c}-input`,ref:w,className:$("peer min-w-60 max-sm:min-w-48 ps-9 h-9",!!o&&"pe-9"),value:o,onChange:s=>N(s.target.value),placeholder:"Search by contributor name...",type:"text","aria-label":"Search by contributor name"}),e.jsx("div",{className:"text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50",children:e.jsx(We,{size:16,"aria-hidden":"true"})}),!!o&&e.jsx("button",{className:"text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","aria-label":"Clear search",onClick:()=>{N(""),w.current&&w.current.focus()},disabled:j,children:e.jsx(Ye,{size:16,"aria-hidden":"true"})})]}),e.jsxs(se,{children:[e.jsx(te,{asChild:!0,children:e.jsxs(g,{variant:"outline",className:"hidden sm:flex",children:[e.jsx(fe,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"View"]})}),e.jsxs(ae,{align:"end",children:[e.jsx(Me,{children:"Toggle columns"}),p.getAllColumns().filter(s=>s.getCanHide()).map(s=>{const a=typeof s.columnDef.header=="string"?s.columnDef.header:s.id;return e.jsx(Pe,{className:"capitalize",checked:s.getIsVisible(),onCheckedChange:u=>s.toggleVisibility(!!u),onSelect:u=>u.preventDefault(),children:a},s.id)})]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[p.getSelectedRowModel().rows.length>0&&e.jsxs(_,{children:[e.jsx(O,{asChild:!0,children:e.jsxs(g,{variant:"outline",children:[e.jsx(ne,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Delete",e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:p.getSelectedRowModel().rows.length})]})}),e.jsxs(Q,{children:[e.jsxs("div",{className:"flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4",children:[e.jsx("div",{className:"flex size-9 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(R,{className:"opacity-80",size:16})}),e.jsxs(X,{children:[e.jsx(J,{children:"Are you absolutely sure?"}),e.jsxs(W,{children:["This action cannot be undone. This will permanently delete"," ",p.getSelectedRowModel().rows.length," selected"," ",p.getSelectedRowModel().rows.length===1?"contributor":"contributors","."]})]})]}),e.jsxs(Y,{children:[e.jsx(Z,{children:"Cancel"}),e.jsx(ee,{onClick:le,children:"Delete"})]})]})]}),e.jsx(qe,{onContributorCreated:v,children:e.jsxs(g,{variant:"outline",children:[e.jsx(Fe,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Add Contributor"]})})]})]}),e.jsx("div",{className:"bg-background rounded-md border",children:e.jsxs(be,{className:"table-fixed",children:[e.jsx(Ce,{children:p.getHeaderGroups().map(s=>e.jsx(y,{className:"hover:bg-transparent",children:s.headers.map(a=>e.jsx(we,{style:{width:`${a.getSize()}px`},className:"h-11",children:a.isPlaceholder?null:a.column.getCanSort()?e.jsxs("div",{className:$(a.column.getCanSort()&&"flex h-full cursor-pointer items-center justify-between gap-2 select-none"),onClick:a.column.getToggleSortingHandler(),onKeyDown:u=>{var z;a.column.getCanSort()&&(u.key==="Enter"||u.key===" ")&&(u.preventDefault(),(z=a.column.getToggleSortingHandler())==null||z(u))},tabIndex:a.column.getCanSort()?0:void 0,children:[P(a.column.columnDef.header,a.getContext()),{asc:e.jsx(Le,{className:"shrink-0 opacity-60",size:16,"aria-hidden":"true"}),desc:e.jsx(Re,{className:"shrink-0 opacity-60",size:16,"aria-hidden":"true"})}[a.column.getIsSorted()]??null]}):P(a.column.columnDef.header,a.getContext())},a.id))},s.id))}),e.jsx(Ne,{children:j?e.jsx(y,{children:e.jsx(k,{colSpan:D.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"}),"Loading contributors..."]})})}):U?e.jsx(y,{children:e.jsx(k,{colSpan:D.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-2",children:[e.jsx(R,{className:"h-8 w-8 text-red-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Failed to load contributors"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:U.message||"An unknown error occurred"}),e.jsx(g,{variant:"outline",size:"sm",onClick:()=>v(),className:"mt-2",children:"Try again"})]})]})})}):(B=p.getRowModel().rows)!=null&&B.length?p.getRowModel().rows.map(s=>e.jsx(y,{"data-state":s.getIsSelected()&&"selected",children:s.getVisibleCells().map(a=>e.jsx(k,{className:"last:py-0",children:P(a.column.columnDef.cell,a.getContext())},a.id))},s.id)):e.jsx(y,{children:e.jsx(k,{colSpan:D.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-2 p-6",children:[e.jsx(Ze,{className:"h-8 w-8 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"No contributors found"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:o?"Try adjusting your search":"No contributors have been created yet"})]})]})})})})]})}),e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3",children:[e.jsx(T,{htmlFor:c,className:"text-sm whitespace-nowrap",children:"Rows per page"}),e.jsxs(Ee,{value:d.pageSize.toString(),onValueChange:s=>{h(a=>({...a,pageSize:Number(s),pageIndex:0}))},disabled:j,children:[e.jsx(Ve,{id:c,className:"w-fit whitespace-nowrap min-w-[4rem]",children:e.jsx(Ue,{placeholder:"Select number of results"})}),e.jsx(He,{className:"[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2",children:[5,10,25,50].map(s=>e.jsx(Be,{value:s.toString(),children:s},s))})]})]}),e.jsxs("div",{className:"flex items-center gap-4 sm:gap-8",children:[e.jsx("div",{className:"text-muted-foreground text-sm whitespace-nowrap",children:e.jsx("p",{className:"text-muted-foreground text-sm whitespace-nowrap","aria-live":"polite",children:j?"Loading...":e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"text-foreground",children:[d.pageIndex*d.pageSize+1,"-",Math.min((d.pageIndex+1)*d.pageSize,(n==null?void 0:n.pagination.total)||0)]})," ","of"," ",e.jsx("span",{className:"text-foreground",children:(n==null?void 0:n.pagination.total)||0})]})})}),e.jsx("div",{children:e.jsx(ve,{children:e.jsxs(ye,{className:"gap-1",children:[e.jsx(A,{children:e.jsx(g,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>h(s=>({...s,pageIndex:0})),disabled:d.pageIndex===0||j,"aria-label":"Go to first page",children:e.jsx(Se,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(A,{children:e.jsx(g,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>h(s=>({...s,pageIndex:s.pageIndex-1})),disabled:d.pageIndex===0||j,"aria-label":"Go to previous page",children:e.jsx(De,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(A,{children:e.jsx(g,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>h(s=>({...s,pageIndex:s.pageIndex+1})),disabled:j||!(n!=null&&n.pagination)||d.pageIndex>=n.pagination.totalPages-1,"aria-label":"Go to next page",children:e.jsx(Te,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(A,{children:e.jsx(g,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>{const s=(n==null?void 0:n.pagination.totalPages)||1;h(a=>({...a,pageIndex:s-1}))},disabled:j||!(n!=null&&n.pagination)||d.pageIndex>=n.pagination.totalPages-1,"aria-label":"Go to last page",children:e.jsx(ze,{size:14,className:"sm:size-4","aria-hidden":"true"})})})]})})})]})]})]})}const vs=function(){const r=de.useNavigate(),{data:l,isPending:t}=E.useSession();return x.useEffect(()=>{if(!l&&!t){r({to:"/auth"});return}},[l,t,r]),t?e.jsx(q,{}):!l||!l.user?e.jsx(q,{}):e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"pl-2 text-xl font-bold",children:"Contributor Management"}),e.jsx("div",{className:"p-2",children:e.jsx(ns,{})})]})};export{vs as component};
