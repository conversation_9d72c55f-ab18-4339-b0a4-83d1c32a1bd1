import{i,g as p,r as u,j as e}from"./main-CiHxKC0e.js";import{a as c}from"./auth-client-Bm508aVF.js";import{u as m}from"./useQuery-DciuO3_z.js";const l=function(){var s;const n=i.useNavigate(),o=p(),{data:t,isPending:a}=c.useSession(),r=m(o.privateData.queryOptions());return u.useEffect(()=>{!t&&!a&&n({to:"/auth"})},[t,a]),a?e.jsx("div",{children:"Loading..."}):e.jsxs("div",{children:[e.jsx("h1",{children:"Dashboard"}),e.jsxs("p",{children:["Welcome ",t==null?void 0:t.user.name]}),e.jsxs("p",{children:["privateData: ",(s=r.data)==null?void 0:s.message]})]})};export{l as component};
