import{c as v,x as g,a as T,j as e,k as o,m as f}from"./main-CiHxKC0e.js";import{u as M}from"./useQuery-DciuO3_z.js";import{a as P}from"./auth-client-Bm508aVF.js";import{B as x}from"./button-mzA_gOdo.js";import{C as i,a as n,b as c,d}from"./card-BeF5Jhpw.js";import{B as m}from"./badge-BuhFyAsn.js";import{S as R}from"./separator-DRGR7AfC.js";import{a as B,U as D,P as F}from"./track-status-dialog-B1hkn9eC.js";import{M as u}from"./genre-select-m4c2kc80.js";import{A as U,F as z,C as Y}from"./file-text-D6ZGaWI0.js";import{S as $}from"./square-pen-cru0dwrj.js";import{U as _}from"./user-BJgcZwil.js";import{U as q}from"./users-D10a2zbL.js";import"./label-C9y0GF5L.js";import"./useMutation-C9Oj6nVm.js";import"./dialog-B3qaM3HV.js";import"./select-Be91QLyZ.js";import"./circle-alert-CqYvwAi4.js";import"./popover-CFVdoKjL.js";import"./circle-check-big-DYr6Kv7b.js";import"./clock-CBl_wPns.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],I=v("Download",E);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],G=v("Settings",V),ue=function(){var p;const{id:h}=g.useParams(),y=g.useNavigate(),k=T(),{data:l,isPending:w}=P.useSession();(p=l==null?void 0:l.user)==null||p.role;const{data:s,isLoading:b,error:S,refetch:A}=M({queryKey:["track",h],queryFn:async()=>{const t=await k.track.get.query({id:h});return{...t,createdAt:new Date(t.createdAt),updatedAt:new Date(t.updatedAt),submittedAt:t.submittedAt?new Date(t.submittedAt):null,readyAt:t.readyAt?new Date(t.readyAt):null}},enabled:!!h});if(w)return e.jsx(o,{});if(!(l!=null&&l.user))return y({to:"/auth"}),e.jsx(o,{});if(b)return e.jsx(o,{});if(S)return e.jsx("div",{className:"container mx-auto p-2 space-y-6",children:e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx(u,{className:"mx-auto h-12 w-12 text-muted-foreground"}),e.jsx("h3",{className:"mt-2 text-sm font-semibold text-foreground",children:"Track not found"}),e.jsx("p",{className:"mt-1 text-sm text-muted-foreground",children:"The track you're looking for doesn't exist or you don't have permission to view it."}),e.jsx("div",{className:"mt-6",children:e.jsx(f,{to:"/dashboard/track",children:e.jsx(x,{children:"Back to Tracks"})})})]})})});if(!s)return e.jsx(o,{});const j=()=>{A()},C=t=>{if(t===0)return"0 B";const a=1024,r=["B","KB","MB","GB"],N=Math.floor(Math.log(t)/Math.log(a));return parseFloat((t/Math.pow(a,N)).toFixed(1))+" "+r[N]},L=t=>{const a=Math.floor(t/60),r=Math.floor(t%60);return`${a}:${r.toString().padStart(2,"0")}`};return e.jsxs("div",{className:"container mx-auto p-2 space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(f,{to:"/dashboard/track",className:"inline-flex items-center text-sm text-muted-foreground hover:text-foreground hover:underline mb-4",children:[e.jsx(U,{className:"h-4 w-4 mr-1"}),"Back to Tracks"]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:s.title}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Track Details"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(B,{track:s,onStatusUpdated:j,children:e.jsx(x,{variant:"outline",children:e.jsx(m,{variant:s.status==="READY"?"default":"secondary",className:"cursor-pointer",children:s.status})})}),e.jsx(D,{track:s,onTrackUpdated:j,children:e.jsxs(x,{children:[e.jsx($,{className:"h-4 w-4"}),"Edit Track"]})})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(i,{children:[e.jsx(n,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(z,{className:"h-5 w-5"}),e.jsx("span",{children:"Basic Information"})]})}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Title"}),e.jsx("p",{className:"text-sm",children:s.title})]}),s.isrc&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"ISRC"}),e.jsx("p",{className:"text-sm font-mono",children:s.isrc})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Genre"}),e.jsx("p",{className:"text-sm",children:s.genre})]}),s.subGenre&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Sub-genre"}),e.jsx("p",{className:"text-sm",children:s.subGenre})]})]}),s.trackVersion&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Version"}),e.jsx("p",{className:"text-sm",children:s.trackVersion})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Recording Year"}),e.jsx("p",{className:"text-sm",children:s.recordingYear})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Publishing Year"}),e.jsx("p",{className:"text-sm",children:s.publishingYear})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Publishing Holder"}),e.jsx("p",{className:"text-sm",children:s.publishingHolder})]})]})]}),e.jsxs(i,{children:[e.jsx(n,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(G,{className:"h-5 w-5"}),e.jsx("span",{children:"Content & Rights"})]})}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Explicit Content"}),e.jsx(m,{variant:"outline",className:"mt-1",children:s.explicit.replace("_"," ")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Rights Claim"}),e.jsx(m,{variant:"outline",className:"mt-1",children:s.rightsClaim.replace("_"," ")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Status"}),e.jsx(m,{variant:s.status==="READY"?"default":"secondary",className:"mt-1",children:s.status})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Metadata Language"}),e.jsx("p",{className:"text-sm uppercase",children:s.metadataLanguage})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Audio Language"}),e.jsx("p",{className:"text-sm uppercase",children:s.audioLanguage})]})]}),(s.previewStart||s.previewLength)&&e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.previewStart&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Preview Start"}),e.jsx("p",{className:"text-sm font-mono",children:s.previewStart})]}),s.previewLength&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Preview Length"}),e.jsx("p",{className:"text-sm font-mono",children:s.previewLength})]})]})]})]}),s.lyrics&&e.jsxs(i,{children:[e.jsx(n,{children:e.jsx(c,{children:"Lyrics"})}),e.jsx(d,{children:e.jsx("div",{className:"whitespace-pre-wrap text-sm",children:s.lyrics})})]}),e.jsxs(i,{children:[e.jsx(n,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(u,{className:"h-5 w-5"}),e.jsxs("span",{children:["Track Files (",s.trackFiles.length,")"]})]})}),e.jsx(d,{children:s.trackFiles.length>0?e.jsx("div",{className:"space-y-3",children:s.trackFiles.map((t,a)=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded",children:e.jsx(u,{className:"h-4 w-4 text-primary"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:t.fileName||`Track File ${a+1}`}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-muted-foreground",children:[t.fileSize&&e.jsx("span",{children:C(t.fileSize)}),t.duration&&e.jsx("span",{children:L(t.duration)}),t.mimeType&&e.jsx("span",{className:"uppercase",children:t.mimeType.split("/")[1]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{variant:"outline",size:"sm",onClick:()=>window.open(t.fileUrl,"_blank"),children:e.jsx(F,{className:"h-4 w-4"})}),e.jsx(x,{variant:"outline",size:"sm",onClick:()=>{const r=document.createElement("a");r.href=t.fileUrl,r.download=t.fileName||"track",r.click()},children:e.jsx(I,{className:"h-4 w-4"})})]})]},t.id))}):e.jsx("p",{className:"text-muted-foreground text-center py-4",children:"No track files uploaded"})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(i,{children:[e.jsx(n,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(_,{className:"h-5 w-5"}),e.jsxs("span",{children:["Artists (",s.artists.length,")"]})]})}),e.jsx(d,{children:s.artists.length>0?e.jsx("div",{className:"space-y-3",children:s.artists.map((t,a)=>e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:t.artist.name}),e.jsx(m,{variant:t.role==="PRIMARY"?"default":"secondary",className:"text-xs mt-1",children:t.role==="PRIMARY"?"Primary":"Featuring"})]})},`${t.artist.id}-${a}`))}):e.jsx("p",{className:"text-muted-foreground text-center py-4",children:"No artists assigned"})})]}),e.jsxs(i,{children:[e.jsx(n,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(q,{className:"h-5 w-5"}),e.jsxs("span",{children:["Contributors (",s.contributors.length,")"]})]})}),e.jsx(d,{children:s.contributors.length>0?e.jsx("div",{className:"space-y-3",children:s.contributors.map((t,a)=>e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:t.contributor.name}),e.jsx(m,{variant:"outline",className:"text-xs mt-1",children:t.role})]},`${t.contributor.id}-${a}`))}):e.jsx("p",{className:"text-muted-foreground text-center py-4",children:"No contributors assigned"})})]}),e.jsxs(i,{children:[e.jsx(n,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(Y,{className:"h-5 w-5"}),e.jsx("span",{children:"Metadata"})]})}),e.jsxs(d,{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Created"}),e.jsx("p",{className:"text-sm",children:s.createdAt.toLocaleString()})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:s.updatedAt.toLocaleString()})]}),s.submittedAt&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Submitted"}),e.jsx("p",{className:"text-sm",children:s.submittedAt.toLocaleString()})]}),s.readyAt&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Ready"}),e.jsx("p",{className:"text-sm",children:s.readyAt.toLocaleString()})]}),e.jsx(R,{}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"Created By"}),e.jsx("p",{className:"text-sm",children:s.user.name}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s.user.email})]})]})]})]})]})]})};export{ue as component};
