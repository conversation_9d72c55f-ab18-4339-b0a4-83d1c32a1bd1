import{c as ge,r as j,u as je,a as G,t as p,j as e,m as U,g as pe,n as fe,k as _}from"./main-CiHxKC0e.js";import{a as H}from"./auth-client-Bm508aVF.js";import{E as ve,A as Z,a as W,b as ee,c as se,d as ae,e as te,f as re,g as le,h as ie,u as Ne,t as Ce,v as we,w as ye,x as Se,y as be,C as ke,T as Te,i as Ae,j as R,k as Fe,l as Y,m as De,n as z,P as Pe,o as Me,p as I,q as Re,r as Ie,s as Le}from"./table-TBKnZXRI.js";import{u as Ee}from"./useQuery-DciuO3_z.js";import{u as B}from"./useMutation-C9Oj6nVm.js";import{B as g,a as Ve}from"./button-mzA_gOdo.js";import{D as ne,a as ce,b as oe,c as de,f as L,d as X,k as Ye,C as ze}from"./dropdown-menu-BXhbheYO.js";import{L as u,I as k}from"./label-C9y0GF5L.js";import{S as N,d as C,e as w,f as y,g as l,P as Ge,h as He,i as Be}from"./select-Be91QLyZ.js";import{j as Oe,k as qe,l as Ke,m as Ue,n as _e,o as Xe,p as $e}from"./dialog-B3qaM3HV.js";import{G as Qe,T as Je,M as $}from"./genre-select-m4c2kc80.js";import{T as Ze,A as We,C as es,a as ss,U as as,P as ts}from"./track-status-dialog-B1hkn9eC.js";import{C as Q,T as rs}from"./checkbox-CnzziXQ_.js";import{B as J}from"./badge-BuhFyAsn.js";import{E as ls}from"./eye-DEOsx-Wb.js";import{S as is}from"./square-pen-cru0dwrj.js";import{C as ns}from"./circle-alert-CqYvwAi4.js";import{C as cs}from"./circle-x-6nZ_0hIQ.js";import"./scroll-area-ASo8OkdS.js";import"./popover-CFVdoKjL.js";import"./card-BeF5Jhpw.js";import"./user-BJgcZwil.js";import"./users-D10a2zbL.js";import"./circle-check-big-DYr6Kv7b.js";import"./clock-CBl_wPns.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const os=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],ds=ge("Trash2",os);function hs({children:S,onTrackCreated:T}){var b;const[f,r]=j.useState(!1);je();const a=G(),{data:x}=H.useSession();(b=x==null?void 0:x.user)==null||b.role;const[t,c]=j.useState({title:"",isrc:"",trackVersion:"",recordingYear:new Date().getFullYear(),publishingYear:new Date().getFullYear(),publishingHolder:"",genre:"",subGenre:"",lyrics:"",previewStart:"",previewLength:"",metadataLanguage:"en",explicit:"NOT_EXPLICIT",audioLanguage:"en",rightsClaim:"NO_CLAIM",status:"DRAFT",trackFiles:[],artists:[],contributors:[]}),m=B({mutationFn:async s=>a.track.create.mutate(s),onSuccess:()=>{p.success("Track created successfully"),r(!1),F(),T()},onError:s=>{console.error("Failed to create track:",s),p.error("Failed to create track: "+(s.message||"Unknown error"))}}),F=()=>{c({title:"",isrc:"",trackVersion:"",recordingYear:new Date().getFullYear(),publishingYear:new Date().getFullYear(),publishingHolder:"",genre:"",subGenre:"",lyrics:"",previewStart:"",previewLength:"",metadataLanguage:"en",explicit:"NOT_EXPLICIT",audioLanguage:"en",rightsClaim:"NO_CLAIM",status:"DRAFT",trackFiles:[],artists:[],contributors:[]})},v=async s=>{if(s.preventDefault(),!t.title.trim()){p.error("Track title is required");return}if(!t.genre){p.error("Genre is required");return}if(!t.publishingHolder.trim()){p.error("Publishing holder is required");return}if(t.trackFiles.length===0){p.error("At least one track file is required");return}if(t.artists.length===0){p.error("At least one artist is required");return}if(t.contributors.length===0){p.error("At least one contributor is required");return}try{await m.mutateAsync(t)}catch{}},h=(s,d)=>{c(E=>({...E,[s]:d}))},A=new Date().getFullYear(),D=Array.from({length:A-1900+2},(s,d)=>A+1-d);return e.jsxs(Oe,{open:f,onOpenChange:r,children:[e.jsx(qe,{asChild:!0,children:S}),e.jsxs(Ke,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:[e.jsxs(Ue,{children:[e.jsx(_e,{children:"Create New Track"}),e.jsx(Xe,{children:"Add a new track with all required metadata and files."})]}),e.jsxs("form",{onSubmit:v,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Basic Information"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"title",children:"Title *"}),e.jsx(k,{id:"title",value:t.title,onChange:s=>h("title",s.target.value),placeholder:"Enter track title"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"isrc",children:"ISRC"}),e.jsx(k,{id:"isrc",value:t.isrc,onChange:s=>h("isrc",s.target.value),placeholder:"CC-XXX-YY-NNNNN"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"genre",children:"Genre *"}),e.jsx(Qe,{value:t.genre,onValueChange:s=>h("genre",s),placeholder:"Select genre"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"subGenre",children:"Sub-genre"}),e.jsx(k,{id:"subGenre",value:t.subGenre,onChange:s=>h("subGenre",s.target.value),placeholder:"Enter sub-genre"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"trackVersion",children:"Track Version"}),e.jsx(k,{id:"trackVersion",value:t.trackVersion,onChange:s=>h("trackVersion",s.target.value),placeholder:"e.g., Radio Edit, Extended Mix"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Years & Publishing"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"recordingYear",children:"Recording Year *"}),e.jsxs(N,{value:t.recordingYear.toString(),onValueChange:s=>h("recordingYear",parseInt(s)),children:[e.jsx(C,{children:e.jsx(w,{placeholder:"Select year"})}),e.jsx(y,{children:D.map(s=>e.jsx(l,{value:s.toString(),children:s},s))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"publishingYear",children:"Publishing Year *"}),e.jsxs(N,{value:t.publishingYear.toString(),onValueChange:s=>h("publishingYear",parseInt(s)),children:[e.jsx(C,{children:e.jsx(w,{placeholder:"Select year"})}),e.jsx(y,{children:D.map(s=>e.jsx(l,{value:s.toString(),children:s},s))})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"publishingHolder",children:"Publishing Holder *"}),e.jsx(k,{id:"publishingHolder",value:t.publishingHolder,onChange:s=>h("publishingHolder",s.target.value),placeholder:"Enter publishing holder name"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Content & Language"}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"explicit",children:"Explicit Content"}),e.jsxs(N,{value:t.explicit,onValueChange:s=>h("explicit",s),children:[e.jsx(C,{children:e.jsx(w,{})}),e.jsxs(y,{children:[e.jsx(l,{value:"NOT_EXPLICIT",children:"Not Explicit"}),e.jsx(l,{value:"EXPLICIT",children:"Explicit"}),e.jsx(l,{value:"CLEAN",children:"Clean"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"metadataLanguage",children:"Metadata Language"}),e.jsxs(N,{value:t.metadataLanguage,onValueChange:s=>h("metadataLanguage",s),children:[e.jsx(C,{children:e.jsx(w,{})}),e.jsxs(y,{children:[e.jsx(l,{value:"en",children:"English"}),e.jsx(l,{value:"es",children:"Spanish"}),e.jsx(l,{value:"fr",children:"French"}),e.jsx(l,{value:"de",children:"German"}),e.jsx(l,{value:"it",children:"Italian"}),e.jsx(l,{value:"pt",children:"Portuguese"}),e.jsx(l,{value:"ja",children:"Japanese"}),e.jsx(l,{value:"ko",children:"Korean"}),e.jsx(l,{value:"zh",children:"Chinese"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"audioLanguage",children:"Audio Language"}),e.jsxs(N,{value:t.audioLanguage,onValueChange:s=>h("audioLanguage",s),children:[e.jsx(C,{children:e.jsx(w,{})}),e.jsxs(y,{children:[e.jsx(l,{value:"en",children:"English"}),e.jsx(l,{value:"es",children:"Spanish"}),e.jsx(l,{value:"fr",children:"French"}),e.jsx(l,{value:"de",children:"German"}),e.jsx(l,{value:"it",children:"Italian"}),e.jsx(l,{value:"pt",children:"Portuguese"}),e.jsx(l,{value:"ja",children:"Japanese"}),e.jsx(l,{value:"ko",children:"Korean"}),e.jsx(l,{value:"zh",children:"Chinese"})]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"rightsClaim",children:"Rights Claim"}),e.jsxs(N,{value:t.rightsClaim,onValueChange:s=>h("rightsClaim",s),children:[e.jsx(C,{children:e.jsx(w,{})}),e.jsxs(y,{children:[e.jsx(l,{value:"NO_CLAIM",children:"No Claim"}),e.jsx(l,{value:"REPORT",children:"Report"}),e.jsx(l,{value:"MONETIZE",children:"Monetize"}),e.jsx(l,{value:"BLOCK",children:"Block"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"lyrics",children:"Lyrics"}),e.jsx(Je,{id:"lyrics",value:t.lyrics,onChange:s=>h("lyrics",s.target.value),placeholder:"Enter track lyrics (optional)",rows:4})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Preview Settings"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"previewStart",children:"Preview Start (MM:SS)"}),e.jsx(k,{id:"previewStart",value:t.previewStart,onChange:s=>h("previewStart",s.target.value),placeholder:"1:30"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"previewLength",children:"Preview Length (MM:SS)"}),e.jsx(k,{id:"previewLength",value:t.previewLength,onChange:s=>h("previewLength",s.target.value),placeholder:"0:30"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Track Files *"}),e.jsx(Ze,{files:t.trackFiles,onFilesChange:s=>h("trackFiles",s)})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Artists *"}),e.jsx(We,{artists:t.artists,onArtistsChange:s=>h("artists",s)})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Contributors *"}),e.jsx(es,{contributors:t.contributors,onContributorsChange:s=>h("contributors",s)})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Status"}),e.jsxs(N,{value:t.status,onValueChange:s=>h("status",s),children:[e.jsx(C,{children:e.jsx(w,{})}),e.jsxs(y,{children:[e.jsx(l,{value:"DRAFT",children:"Draft"}),e.jsx(l,{value:"READY",children:"Ready"})]})]})]}),e.jsxs($e,{children:[e.jsx(g,{type:"button",variant:"outline",onClick:()=>r(!1),children:"Cancel"}),e.jsx(g,{type:"submit",disabled:m.isPending,children:m.isPending?"Creating...":"Create Track"})]})]})]})]})}const xs=(S,T)=>[{id:"select",header:({table:r})=>e.jsx(Q,{checked:r.getIsAllPageRowsSelected()||r.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:a=>r.toggleAllPageRowsSelected(!!a),"aria-label":"Select all"}),cell:({row:r})=>e.jsx(Q,{checked:r.getIsSelected(),onCheckedChange:a=>r.toggleSelected(!!a),"aria-label":"Select row"}),size:28,enableSorting:!1,enableHiding:!1},{accessorKey:"title",header:"Title",cell:({row:r})=>{const a=r.original;return e.jsxs("div",{className:"flex flex-col",children:[e.jsx(U,{to:"/dashboard/track/$id",params:{id:a.id},className:"font-medium hover:underline",children:a.title}),a.isrc&&e.jsxs("span",{className:"text-xs text-muted-foreground",children:["ISRC: ",a.isrc]})]})},size:200},{accessorKey:"status",header:"Status",cell:({row:r})=>{const a=r.getValue("status");return e.jsx(J,{variant:a==="READY"?"default":"secondary",className:"capitalize",children:a.toLowerCase()})},size:80},{accessorKey:"genre",header:"Genre",cell:({row:r})=>{const a=r.original;return e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:a.genre}),a.subGenre&&e.jsx("span",{className:"text-xs text-muted-foreground",children:a.subGenre})]})},size:120},{accessorKey:"artists",header:"Artists",cell:({row:r})=>{const a=r.original.artists;if(!a.length)return e.jsx("span",{className:"text-muted-foreground",children:"-"});const x=a.filter(c=>c.role==="PRIMARY"),t=a.filter(c=>c.role==="FEATURING");return e.jsxs("div",{className:"flex flex-col space-y-1",children:[x.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1",children:x.map((c,m)=>e.jsxs("span",{className:"text-sm",children:[c.artist.name,m<x.length-1&&", "]},c.artist.id))}),t.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"feat. "}),t.map((c,m)=>e.jsxs("span",{className:"text-xs text-muted-foreground",children:[c.artist.name,m<t.length-1&&", "]},c.artist.id))]})]})},size:180},{accessorKey:"contributors",header:"Contributors",cell:({row:r})=>{const a=r.original.contributors;return a.length?e.jsxs("div",{className:"flex flex-col space-y-1",children:[a.slice(0,2).map(x=>e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"font-medium",children:x.contributor.name}),e.jsxs("span",{className:"text-xs text-muted-foreground ml-1",children:["(",x.role,")"]})]},x.contributor.id)),a.length>2&&e.jsxs("span",{className:"text-xs text-muted-foreground",children:["+",a.length-2," more"]})]}):e.jsx("span",{className:"text-muted-foreground",children:"-"})},size:160},{accessorKey:"recordingYear",header:"Recording",cell:({row:r})=>{const a=r.original;return e.jsxs("div",{className:"flex flex-col text-sm",children:[e.jsxs("span",{children:["Rec: ",a.recordingYear]}),e.jsxs("span",{className:"text-muted-foreground",children:["Pub: ",a.publishingYear]})]})},size:90},{accessorKey:"trackFiles",header:"Files",cell:({row:r})=>{const a=r.original.trackFiles;if(!a.length)return e.jsx("span",{className:"text-muted-foreground",children:"No files"});const x=a.reduce((c,m)=>c+(m.fileSize||0),0),t=c=>{if(c===0)return"0 B";const m=1024,F=["B","KB","MB","GB"],v=Math.floor(Math.log(c)/Math.log(m));return parseFloat((c/Math.pow(m,v)).toFixed(1))+" "+F[v]};return e.jsxs("div",{className:"flex flex-col text-sm",children:[e.jsxs("span",{children:[a.length," file",a.length>1?"s":""]}),x>0&&e.jsx("span",{className:"text-xs text-muted-foreground",children:t(x)})]})},size:80},{accessorKey:"createdAt",header:"Created",cell:({row:r})=>{const a=r.getValue("createdAt");return e.jsxs("div",{className:"flex flex-col text-sm",children:[e.jsx("span",{children:a.toLocaleDateString()}),e.jsx("span",{className:"text-xs text-muted-foreground",children:a.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},size:100},{id:"actions",header:"Actions",cell:({row:r})=>{const a=r.original,x=G(),t=B({mutationFn:async()=>x.track.delete.mutate({id:a.id}),onSuccess:()=>{p.success("Track deleted successfully"),S()},onError:c=>{p.error(`Failed to delete track: ${c.message}`)}});return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ss,{track:a,onStatusUpdated:S,children:e.jsx(g,{variant:"ghost",size:"sm",children:e.jsx(J,{variant:a.status==="READY"?"default":"secondary",className:"cursor-pointer",children:a.status})})}),e.jsxs(ne,{children:[e.jsx(ce,{asChild:!0,children:e.jsxs(g,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(ve,{className:"h-4 w-4"})]})}),e.jsxs(oe,{align:"end",children:[e.jsx(de,{children:"Actions"}),e.jsx(L,{asChild:!0,children:e.jsxs(U,{to:"/dashboard/track/$id",params:{id:a.id},children:[e.jsx(ls,{className:"mr-2 h-4 w-4"}),"View Details"]})}),e.jsx(as,{track:a,onTrackUpdated:S,children:e.jsxs(L,{onSelect:c=>c.preventDefault(),children:[e.jsx(is,{className:"mr-2 h-4 w-4"}),"Edit Track"]})}),e.jsx(X,{}),a.trackFiles.length>0&&e.jsxs(L,{onClick:()=>{window.open(a.trackFiles[0].fileUrl,"_blank")},children:[e.jsx(ts,{className:"mr-2 h-4 w-4"}),"Preview Audio"]}),e.jsx(X,{}),e.jsxs(Z,{children:[e.jsx(W,{asChild:!0,children:e.jsxs(L,{onSelect:c=>c.preventDefault(),className:"text-destructive focus:text-destructive",children:[e.jsx(ds,{className:"mr-2 h-4 w-4"}),"Delete Track"]})}),e.jsxs(ee,{children:[e.jsxs(se,{children:[e.jsx(ae,{children:"Are you sure?"}),e.jsxs(te,{children:['This will permanently delete "',a.title,'" and all its associated files. This action cannot be undone.']})]}),e.jsxs(re,{children:[e.jsx(le,{children:"Cancel"}),e.jsx(ie,{onClick:()=>t.mutate(),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete"})]})]})]})]})]})]})},size:120,enableSorting:!1,enableHiding:!1}];function us(){var q,K;const S=pe(),T=G(),{data:f}=H.useSession(),r=((q=f==null?void 0:f.user)==null?void 0:q.role)==="admin",[a,x]=j.useState([]),[t,c]=j.useState([]),[m,F]=j.useState({}),[v,h]=j.useState({pageIndex:0,pageSize:10}),[A,D]=j.useState(""),[b,s]=j.useState(""),{data:d,isLoading:E,error:O,refetch:P}=Ee(S.track.list.queryOptions({page:v.pageIndex+1,limit:v.pageSize,search:A||void 0,status:b||void 0})),he=B({mutationFn:async i=>T.track.delete.mutate({id:i}),onSuccess:()=>{p.success("Track deleted successfully"),P()},onError:i=>{p.error(`Failed to delete track: ${i.message}`)}}),xe=j.useMemo(()=>((d==null?void 0:d.tracks)||[]).map(n=>({...n,createdAt:new Date(n.createdAt),updatedAt:new Date(n.updatedAt),submittedAt:n.submittedAt?new Date(n.submittedAt):null,readyAt:n.readyAt?new Date(n.readyAt):null})),[d==null?void 0:d.tracks]),ue=async()=>{const n=o.getSelectedRowModel().rows.map(M=>M.original.id);try{const M=n.map(me=>he.mutateAsync(me));await Promise.all(M),o.resetRowSelection()}catch{}},V=j.useMemo(()=>xs(P),[P,r]),o=Ne({data:xe,columns:V,getCoreRowModel:be(),getSortedRowModel:Se(),onSortingChange:x,enableSortingRemoval:!1,getPaginationRowModel:ye(),onPaginationChange:h,onColumnFiltersChange:c,onColumnVisibilityChange:F,getFilteredRowModel:we(),getFacetedUniqueValues:Ce(),state:{sorting:a,pagination:v,columnFilters:t,columnVisibility:m}});return j.useEffect(()=>{if(d!=null&&d.pagination){const i=d.pagination.pages;v.pageIndex>=i&&i>0&&h(n=>({...n,pageIndex:i-1}))}},[d==null?void 0:d.pagination,v.pageIndex]),O?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ns,{className:"mx-auto h-12 w-12 text-muted-foreground"}),e.jsx("h3",{className:"mt-2 text-sm font-semibold text-foreground",children:"Error loading tracks"}),e.jsx("p",{className:"mt-1 text-sm text-muted-foreground",children:O.message||"Something went wrong"}),e.jsx("div",{className:"mt-6",children:e.jsx(g,{onClick:()=>P(),children:"Try again"})})]})}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Tracks"}),e.jsx("p",{className:"text-muted-foreground",children:"Manage your music tracks and their metadata."})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(k,{placeholder:"Search tracks...",value:A,onChange:i=>D(i.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),e.jsxs(N,{value:b,onValueChange:s,children:[e.jsx(C,{className:"h-8 w-[120px]",children:e.jsx(w,{placeholder:"Status"})}),e.jsxs(y,{children:[e.jsx(l,{value:"",children:"All Status"}),e.jsx(l,{value:"DRAFT",children:"Draft"}),e.jsx(l,{value:"READY",children:"Ready"})]})]}),(A||b)&&e.jsxs(g,{variant:"ghost",onClick:()=>{D(""),s("")},className:"h-8 px-2 lg:px-3",children:["Reset",e.jsx(cs,{className:"ml-2 h-4 w-4"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(ne,{children:[e.jsx(ce,{asChild:!0,children:e.jsxs(g,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[e.jsx(ke,{className:"mr-2 h-4 w-4"}),"View"]})}),e.jsxs(oe,{align:"end",className:"w-[150px]",children:[e.jsx(de,{children:"Toggle columns"}),o.getAllColumns().filter(i=>typeof i.accessorFn<"u"&&i.getCanHide()).map(i=>e.jsx(Ye,{className:"capitalize",checked:i.getIsVisible(),onCheckedChange:n=>i.toggleVisibility(!!n),children:i.id},i.id))]})]}),o.getSelectedRowModel().rows.length>0&&e.jsxs(Z,{children:[e.jsx(W,{asChild:!0,children:e.jsxs(g,{variant:"outline",size:"sm",className:"h-8",children:[e.jsx(rs,{className:"mr-2 h-4 w-4"}),"Delete (",o.getSelectedRowModel().rows.length,")"]})}),e.jsxs(ee,{children:[e.jsxs(se,{children:[e.jsx(ae,{children:"Are you sure?"}),e.jsxs(te,{children:["This will permanently delete"," ",o.getSelectedRowModel().rows.length," track(s). This action cannot be undone."]})]}),e.jsxs(re,{children:[e.jsx(le,{children:"Cancel"}),e.jsx(ie,{onClick:ue,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete"})]})]})]}),e.jsx(hs,{onTrackCreated:P,children:e.jsxs(g,{variant:"outline",children:[e.jsx(Ge,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Add Track"]})})]})]}),e.jsx("div",{className:"bg-background rounded-md border",children:e.jsxs(Te,{className:"table-fixed",children:[e.jsx(Ae,{children:o.getHeaderGroups().map(i=>e.jsx(R,{className:"hover:bg-transparent",children:i.headers.map(n=>e.jsx(Fe,{style:{width:`${n.getSize()}px`},className:"h-11",children:n.isPlaceholder?null:n.column.getCanSort()?e.jsxs("div",{className:Ve(n.column.getCanSort()&&"flex h-full cursor-pointer items-center justify-between gap-2 select-none"),onClick:n.column.getToggleSortingHandler(),children:[Y(n.column.columnDef.header,n.getContext()),{asc:e.jsx(Be,{className:"h-4 w-4"}),desc:e.jsx(He,{className:"h-4 w-4"})}[n.column.getIsSorted()]??null]}):Y(n.column.columnDef.header,n.getContext())},n.id))},i.id))}),e.jsx(De,{children:E?e.jsx(R,{children:e.jsx(z,{colSpan:V.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx($,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading tracks..."]})})}):(K=o.getRowModel().rows)!=null&&K.length?o.getRowModel().rows.map(i=>e.jsx(R,{"data-state":i.getIsSelected()&&"selected",className:"hover:bg-muted/50",children:i.getVisibleCells().map(n=>e.jsx(z,{style:{width:`${n.column.getSize()}px`},className:"py-2",children:Y(n.column.columnDef.cell,n.getContext())},n.id))},i.id)):e.jsx(R,{children:e.jsx(z,{colSpan:V.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx($,{className:"h-8 w-8 text-muted-foreground mb-2"}),e.jsx("p",{className:"text-muted-foreground",children:"No tracks found."}),e.jsx("p",{className:"text-sm text-muted-foreground",children:A||b?"Try adjusting your search or filters.":"Get started by creating your first track."})]})})})})]})}),(d==null?void 0:d.pagination)&&e.jsxs("div",{className:"flex items-center justify-between px-2",children:[e.jsx("div",{className:"flex-1 text-sm text-muted-foreground",children:o.getSelectedRowModel().rows.length>0&&e.jsxs("span",{children:[o.getSelectedRowModel().rows.length," of"," ",o.getRowModel().rows.length," row(s) selected."]})}),e.jsxs("div",{className:"flex items-center space-x-6 lg:space-x-8",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:"text-sm font-medium",children:"Rows per page"}),e.jsxs(N,{value:`${o.getState().pagination.pageSize}`,onValueChange:i=>{o.setPageSize(Number(i))},children:[e.jsx(C,{className:"h-8 w-[70px]",children:e.jsx(w,{placeholder:o.getState().pagination.pageSize})}),e.jsx(y,{side:"top",children:[10,20,30,40,50].map(i=>e.jsx(l,{value:`${i}`,children:i},i))})]})]}),e.jsxs("div",{className:"flex w-[100px] items-center justify-center text-sm font-medium",children:["Page ",d.pagination.page," of"," ",d.pagination.pages]}),e.jsx(Pe,{children:e.jsxs(Me,{className:"flex items-center space-x-2",children:[e.jsx(I,{children:e.jsxs(g,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>o.setPageIndex(0),disabled:!o.getCanPreviousPage(),children:[e.jsx("span",{className:"sr-only",children:"Go to first page"}),e.jsx(Re,{className:"h-4 w-4"})]})}),e.jsx(I,{children:e.jsxs(g,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>o.previousPage(),disabled:!o.getCanPreviousPage(),children:[e.jsx("span",{className:"sr-only",children:"Go to previous page"}),e.jsx(Ie,{className:"h-4 w-4"})]})}),e.jsx(I,{children:e.jsxs(g,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>o.nextPage(),disabled:!o.getCanNextPage(),children:[e.jsx("span",{className:"sr-only",children:"Go to next page"}),e.jsx(ze,{className:"h-4 w-4"})]})}),e.jsx(I,{children:e.jsxs(g,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>o.setPageIndex(o.getPageCount()-1),disabled:!o.getCanNextPage(),children:[e.jsx("span",{className:"sr-only",children:"Go to last page"}),e.jsx(Le,{className:"h-4 w-4"})]})})]})})]})]})]})}const zs=function(){const T=fe.useNavigate(),{data:f,isPending:r}=H.useSession();return j.useEffect(()=>{if(!f&&!r){T({to:"/auth"});return}},[f,r,T]),r?e.jsx(_,{}):!f||!f.user?e.jsx(_,{}):e.jsx("div",{className:"container mx-auto p-2 space-y-6",children:e.jsx(us,{})})};export{zs as component};
