import{c as w,r as l,j as e,d as Qe,u as je,e as Je,f as et,g as tt,h as at,O as rt}from"./main-CiHxKC0e.js";import{u as J,c as nt,a as h,B as ye,S as I,b as st}from"./button-mzA_gOdo.js";import{u as we,c as ee,a as Ce,b as N,P as te,d as B,e as ot,f as it,D as lt,R as dt,C as ct,g as ut,X as pt,T as ht,h as ft,i as mt,O as xt}from"./dialog-B3qaM3HV.js";import{c as Ne,R as bt,A as gt,a as vt,C as jt,b as yt}from"./select-Be91QLyZ.js";import{P as T}from"./label-C9y0GF5L.js";import{S as wt}from"./scroll-area-ASo8OkdS.js";import{C as ke,D as Se,a as Te,b as Me,c as Ct,d as ce,e as Nt,f as k}from"./dropdown-menu-BXhbheYO.js";import{a as Y}from"./auth-client-Bm508aVF.js";import{S as M}from"./skeleton-C8PRFenc.js";import{C as kt}from"./create-artist-dialog-CTfN1z1l.js";import{C as St}from"./create-label-dialog-B5nj_Mv8.js";import{C as Tt}from"./create-contributor-dialog-CchRLeku.js";import{D as Mt,S as _t}from"./send-BrBtB4dM.js";import{M as _e}from"./genre-select-m4c2kc80.js";import{U as At}from"./users-D10a2zbL.js";import{S as ue}from"./separator-DRGR7AfC.js";import{u as Rt}from"./useQuery-DciuO3_z.js";import"./useMutation-C9Oj6nVm.js";import"./checkbox-CnzziXQ_.js";import"./country-dropdown-u20PekMn.js";import"./popover-CFVdoKjL.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pt=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],Et=w("Bell",Pt);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lt=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]],Dt=w("Building2",Lt);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ot=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],pe=w("ChevronsUpDown",Ot);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const It=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],zt=w("CreditCard",It);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bt=[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]],$t=w("LayoutDashboard",Bt);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ht=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.93 4.93 4.24 4.24",key:"1ymg45"}],["path",{d:"m14.83 9.17 4.24-4.24",key:"1cb5xl"}],["path",{d:"m14.83 14.83 4.24 4.24",key:"q42g0n"}],["path",{d:"m9.17 14.83-4.24 4.24",key:"bqpfvv"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]],Ft=w("LifeBuoy",Ht);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qt=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],Gt=w("LogOut",qt);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ut=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],Kt=w("Moon",Ut);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vt=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Wt=w("PanelLeft",Vt);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xt=[["path",{d:"M18.5 8c-1.4 0-2.6-.8-3.2-2A6.87 6.87 0 0 0 2 9v11a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-8.5C22 9.6 20.4 8 18.5 8",key:"lag0yf"}],["path",{d:"M2 14h20",key:"myj16y"}],["path",{d:"M6 14v4",key:"9ng0ue"}],["path",{d:"M10 14v4",key:"1v8uk5"}],["path",{d:"M14 14v4",key:"1tqops"}],["path",{d:"M18 14v4",key:"18uqwm"}]],Yt=w("Piano",Xt);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zt=[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]],Qt=w("Settings2",Zt);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],ea=w("Sun",Jt);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ta=[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]],aa=w("UserRound",ta);var $="Collapsible",[ra,Ur]=ee($),[na,ae]=ra($),Ae=l.forwardRef((t,a)=>{const{__scopeCollapsible:r,open:n,defaultOpen:s,disabled:o,onOpenChange:d,...c}=t,[i,u]=we({prop:n,defaultProp:s??!1,onChange:d,caller:$});return e.jsx(na,{scope:r,disabled:o,contentId:Ce(),open:i,onOpenToggle:l.useCallback(()=>u(p=>!p),[u]),children:e.jsx(T.div,{"data-state":ne(i),"data-disabled":o?"":void 0,...c,ref:a})})});Ae.displayName=$;var Re="CollapsibleTrigger",Pe=l.forwardRef((t,a)=>{const{__scopeCollapsible:r,...n}=t,s=ae(Re,r);return e.jsx(T.button,{type:"button","aria-controls":s.contentId,"aria-expanded":s.open||!1,"data-state":ne(s.open),"data-disabled":s.disabled?"":void 0,disabled:s.disabled,...n,ref:a,onClick:N(t.onClick,s.onOpenToggle)})});Pe.displayName=Re;var re="CollapsibleContent",Ee=l.forwardRef((t,a)=>{const{forceMount:r,...n}=t,s=ae(re,t.__scopeCollapsible);return e.jsx(te,{present:r||s.open,children:({present:o})=>e.jsx(sa,{...n,ref:a,present:o})})});Ee.displayName=re;var sa=l.forwardRef((t,a)=>{const{__scopeCollapsible:r,present:n,children:s,...o}=t,d=ae(re,r),[c,i]=l.useState(n),u=l.useRef(null),p=J(a,u),x=l.useRef(0),b=x.current,m=l.useRef(0),v=m.current,j=d.open||c,y=l.useRef(j),g=l.useRef(void 0);return l.useEffect(()=>{const f=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(f)},[]),B(()=>{const f=u.current;if(f){g.current=g.current||{transitionDuration:f.style.transitionDuration,animationName:f.style.animationName},f.style.transitionDuration="0s",f.style.animationName="none";const C=f.getBoundingClientRect();x.current=C.height,m.current=C.width,y.current||(f.style.transitionDuration=g.current.transitionDuration,f.style.animationName=g.current.animationName),i(n)}},[d.open,n]),e.jsx(T.div,{"data-state":ne(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!j,...o,ref:p,style:{"--radix-collapsible-content-height":b?`${b}px`:void 0,"--radix-collapsible-content-width":v?`${v}px`:void 0,...t.style},children:j&&s})});function ne(t){return t?"open":"closed"}var oa=Ae,ia=Qe();function la(){return ia.useSyncExternalStore(da,()=>!0,()=>!1)}function da(){return()=>{}}var se="Avatar",[ca,Kr]=ee(se),[ua,Le]=ca(se),De=l.forwardRef((t,a)=>{const{__scopeAvatar:r,...n}=t,[s,o]=l.useState("idle");return e.jsx(ua,{scope:r,imageLoadingStatus:s,onImageLoadingStatusChange:o,children:e.jsx(T.span,{...n,ref:a})})});De.displayName=se;var Oe="AvatarImage",Ie=l.forwardRef((t,a)=>{const{__scopeAvatar:r,src:n,onLoadingStatusChange:s=()=>{},...o}=t,d=Le(Oe,r),c=pa(n,o),i=ot(u=>{s(u),d.onImageLoadingStatusChange(u)});return B(()=>{c!=="idle"&&i(c)},[c,i]),c==="loaded"?e.jsx(T.img,{...o,ref:a,src:n}):null});Ie.displayName=Oe;var ze="AvatarFallback",Be=l.forwardRef((t,a)=>{const{__scopeAvatar:r,delayMs:n,...s}=t,o=Le(ze,r),[d,c]=l.useState(n===void 0);return l.useEffect(()=>{if(n!==void 0){const i=window.setTimeout(()=>c(!0),n);return()=>window.clearTimeout(i)}},[n]),d&&o.imageLoadingStatus!=="loaded"?e.jsx(T.span,{...s,ref:a}):null});Be.displayName=ze;function he(t,a){return t?a?(t.src!==a&&(t.src=a),t.complete&&t.naturalWidth>0?"loaded":"loading"):"error":"idle"}function pa(t,{referrerPolicy:a,crossOrigin:r}){const n=la(),s=l.useRef(null),o=n?(s.current||(s.current=new window.Image),s.current):null,[d,c]=l.useState(()=>he(o,t));return B(()=>{c(he(o,t))},[o,t]),B(()=>{const i=x=>()=>{c(x)};if(!o)return;const u=i("loaded"),p=i("error");return o.addEventListener("load",u),o.addEventListener("error",p),a&&(o.referrerPolicy=a),typeof r=="string"&&(o.crossOrigin=r),()=>{o.removeEventListener("load",u),o.removeEventListener("error",p)}},[o,r,a]),d}var ha=De,fa=Ie,ma=Be,[H,Vr]=ee("Tooltip",[Ne]),F=Ne(),$e="TooltipProvider",xa=700,Z="tooltip.open",[ba,oe]=H($e),He=t=>{const{__scopeTooltip:a,delayDuration:r=xa,skipDelayDuration:n=300,disableHoverableContent:s=!1,children:o}=t,d=l.useRef(!0),c=l.useRef(!1),i=l.useRef(0);return l.useEffect(()=>{const u=i.current;return()=>window.clearTimeout(u)},[]),e.jsx(ba,{scope:a,isOpenDelayedRef:d,delayDuration:r,onOpen:l.useCallback(()=>{window.clearTimeout(i.current),d.current=!1},[]),onClose:l.useCallback(()=>{window.clearTimeout(i.current),i.current=window.setTimeout(()=>d.current=!0,n)},[n]),isPointerInTransitRef:c,onPointerInTransitChange:l.useCallback(u=>{c.current=u},[]),disableHoverableContent:s,children:o})};He.displayName=$e;var L="Tooltip",[ga,z]=H(L),Fe=t=>{const{__scopeTooltip:a,children:r,open:n,defaultOpen:s,onOpenChange:o,disableHoverableContent:d,delayDuration:c}=t,i=oe(L,t.__scopeTooltip),u=F(a),[p,x]=l.useState(null),b=Ce(),m=l.useRef(0),v=d??i.disableHoverableContent,j=c??i.delayDuration,y=l.useRef(!1),[g,f]=we({prop:n,defaultProp:s??!1,onChange:de=>{de?(i.onOpen(),document.dispatchEvent(new CustomEvent(Z))):i.onClose(),o==null||o(de)},caller:L}),C=l.useMemo(()=>g?y.current?"delayed-open":"instant-open":"closed",[g]),A=l.useCallback(()=>{window.clearTimeout(m.current),m.current=0,y.current=!1,f(!0)},[f]),R=l.useCallback(()=>{window.clearTimeout(m.current),m.current=0,f(!1)},[f]),le=l.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{y.current=!0,f(!0),m.current=0},j)},[j,f]);return l.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),e.jsx(bt,{...u,children:e.jsx(ga,{scope:a,contentId:b,open:g,stateAttribute:C,trigger:p,onTriggerChange:x,onTriggerEnter:l.useCallback(()=>{i.isOpenDelayedRef.current?le():A()},[i.isOpenDelayedRef,le,A]),onTriggerLeave:l.useCallback(()=>{v?R():(window.clearTimeout(m.current),m.current=0)},[R,v]),onOpen:A,onClose:R,disableHoverableContent:v,children:r})})};Fe.displayName=L;var Q="TooltipTrigger",qe=l.forwardRef((t,a)=>{const{__scopeTooltip:r,...n}=t,s=z(Q,r),o=oe(Q,r),d=F(r),c=l.useRef(null),i=J(a,c,s.onTriggerChange),u=l.useRef(!1),p=l.useRef(!1),x=l.useCallback(()=>u.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",x),[x]),e.jsx(gt,{asChild:!0,...d,children:e.jsx(T.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...n,ref:i,onPointerMove:N(t.onPointerMove,b=>{b.pointerType!=="touch"&&!p.current&&!o.isPointerInTransitRef.current&&(s.onTriggerEnter(),p.current=!0)}),onPointerLeave:N(t.onPointerLeave,()=>{s.onTriggerLeave(),p.current=!1}),onPointerDown:N(t.onPointerDown,()=>{s.open&&s.onClose(),u.current=!0,document.addEventListener("pointerup",x,{once:!0})}),onFocus:N(t.onFocus,()=>{u.current||s.onOpen()}),onBlur:N(t.onBlur,s.onClose),onClick:N(t.onClick,s.onClose)})})});qe.displayName=Q;var ie="TooltipPortal",[va,ja]=H(ie,{forceMount:void 0}),Ge=t=>{const{__scopeTooltip:a,forceMount:r,children:n,container:s}=t,o=z(ie,a);return e.jsx(va,{scope:a,forceMount:r,children:e.jsx(te,{present:r||o.open,children:e.jsx(it,{asChild:!0,container:s,children:n})})})};Ge.displayName=ie;var _="TooltipContent",Ue=l.forwardRef((t,a)=>{const r=ja(_,t.__scopeTooltip),{forceMount:n=r.forceMount,side:s="top",...o}=t,d=z(_,t.__scopeTooltip);return e.jsx(te,{present:n||d.open,children:d.disableHoverableContent?e.jsx(Ke,{side:s,...o,ref:a}):e.jsx(ya,{side:s,...o,ref:a})})}),ya=l.forwardRef((t,a)=>{const r=z(_,t.__scopeTooltip),n=oe(_,t.__scopeTooltip),s=l.useRef(null),o=J(a,s),[d,c]=l.useState(null),{trigger:i,onClose:u}=r,p=s.current,{onPointerInTransitChange:x}=n,b=l.useCallback(()=>{c(null),x(!1)},[x]),m=l.useCallback((v,j)=>{const y=v.currentTarget,g={x:v.clientX,y:v.clientY},f=ka(g,y.getBoundingClientRect()),C=Sa(g,f),A=Ta(j.getBoundingClientRect()),R=_a([...C,...A]);c(R),x(!0)},[x]);return l.useEffect(()=>()=>b(),[b]),l.useEffect(()=>{if(i&&p){const v=y=>m(y,p),j=y=>m(y,i);return i.addEventListener("pointerleave",v),p.addEventListener("pointerleave",j),()=>{i.removeEventListener("pointerleave",v),p.removeEventListener("pointerleave",j)}}},[i,p,m,b]),l.useEffect(()=>{if(d){const v=j=>{const y=j.target,g={x:j.clientX,y:j.clientY},f=(i==null?void 0:i.contains(y))||(p==null?void 0:p.contains(y)),C=!Ma(g,d);f?b():C&&(b(),u())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[i,p,d,u,b]),e.jsx(Ke,{...t,ref:o})}),[wa,Ca]=H(L,{isInside:!1}),Na=nt("TooltipContent"),Ke=l.forwardRef((t,a)=>{const{__scopeTooltip:r,children:n,"aria-label":s,onEscapeKeyDown:o,onPointerDownOutside:d,...c}=t,i=z(_,r),u=F(r),{onClose:p}=i;return l.useEffect(()=>(document.addEventListener(Z,p),()=>document.removeEventListener(Z,p)),[p]),l.useEffect(()=>{if(i.trigger){const x=b=>{const m=b.target;m!=null&&m.contains(i.trigger)&&p()};return window.addEventListener("scroll",x,{capture:!0}),()=>window.removeEventListener("scroll",x,{capture:!0})}},[i.trigger,p]),e.jsx(lt,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:d,onFocusOutside:x=>x.preventDefault(),onDismiss:p,children:e.jsxs(jt,{"data-state":i.stateAttribute,...u,...c,ref:a,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[e.jsx(Na,{children:n}),e.jsx(wa,{scope:r,isInside:!0,children:e.jsx(yt,{id:i.contentId,role:"tooltip",children:s||n})})]})})});Ue.displayName=_;var Ve="TooltipArrow",We=l.forwardRef((t,a)=>{const{__scopeTooltip:r,...n}=t,s=F(r);return Ca(Ve,r).isInside?null:e.jsx(vt,{...s,...n,ref:a})});We.displayName=Ve;function ka(t,a){const r=Math.abs(a.top-t.y),n=Math.abs(a.bottom-t.y),s=Math.abs(a.right-t.x),o=Math.abs(a.left-t.x);switch(Math.min(r,n,s,o)){case o:return"left";case s:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Sa(t,a,r=5){const n=[];switch(a){case"top":n.push({x:t.x-r,y:t.y+r},{x:t.x+r,y:t.y+r});break;case"bottom":n.push({x:t.x-r,y:t.y-r},{x:t.x+r,y:t.y-r});break;case"left":n.push({x:t.x+r,y:t.y-r},{x:t.x+r,y:t.y+r});break;case"right":n.push({x:t.x-r,y:t.y-r},{x:t.x-r,y:t.y+r});break}return n}function Ta(t){const{top:a,right:r,bottom:n,left:s}=t;return[{x:s,y:a},{x:r,y:a},{x:r,y:n},{x:s,y:n}]}function Ma(t,a){const{x:r,y:n}=t;let s=!1;for(let o=0,d=a.length-1;o<a.length;d=o++){const c=a[o],i=a[d],u=c.x,p=c.y,x=i.x,b=i.y;p>n!=b>n&&r<(x-u)*(n-p)/(b-p)+u&&(s=!s)}return s}function _a(t){const a=t.slice();return a.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),Aa(a)}function Aa(t){if(t.length<=1)return t.slice();const a=[];for(let n=0;n<t.length;n++){const s=t[n];for(;a.length>=2;){const o=a[a.length-1],d=a[a.length-2];if((o.x-d.x)*(s.y-d.y)>=(o.y-d.y)*(s.x-d.x))a.pop();else break}a.push(s)}a.pop();const r=[];for(let n=t.length-1;n>=0;n--){const s=t[n];for(;r.length>=2;){const o=r[r.length-1],d=r[r.length-2];if((o.x-d.x)*(s.y-d.y)>=(o.y-d.y)*(s.x-d.x))r.pop();else break}r.push(s)}return r.pop(),a.length===1&&r.length===1&&a[0].x===r[0].x&&a[0].y===r[0].y?a:a.concat(r)}var Ra=He,Pa=Fe,Ea=qe,La=Ge,Da=Ue,Oa=We;const G=768;function Ia(){const[t,a]=l.useState(void 0);return l.useEffect(()=>{const r=window.matchMedia(`(max-width: ${G-1}px)`),n=()=>{a(window.innerWidth<G)};return r.addEventListener("change",n),a(window.innerWidth<G),()=>r.removeEventListener("change",n)},[]),!!t}function za({...t}){return e.jsx(dt,{"data-slot":"sheet",...t})}function Ba({...t}){return e.jsx(mt,{"data-slot":"sheet-portal",...t})}function $a({className:t,...a}){return e.jsx(xt,{"data-slot":"sheet-overlay",className:h("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function Ha({className:t,children:a,side:r="right",...n}){return e.jsxs(Ba,{children:[e.jsx($a,{}),e.jsxs(ct,{"data-slot":"sheet-content",className:h("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",r==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",r==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",r==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",r==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...n,children:[a,e.jsxs(ut,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[e.jsx(pt,{className:"size-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Fa({className:t,...a}){return e.jsx("div",{"data-slot":"sheet-header",className:h("flex flex-col gap-1.5 p-4",t),...a})}function qa({className:t,...a}){return e.jsx(ht,{"data-slot":"sheet-title",className:h("text-foreground font-semibold",t),...a})}function Ga({className:t,...a}){return e.jsx(ft,{"data-slot":"sheet-description",className:h("text-muted-foreground text-sm",t),...a})}function Xe({delayDuration:t=0,...a}){return e.jsx(Ra,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function Ua({...t}){return e.jsx(Xe,{children:e.jsx(Pa,{"data-slot":"tooltip",...t})})}function Ka({...t}){return e.jsx(Ea,{"data-slot":"tooltip-trigger",...t})}function Va({className:t,sideOffset:a=0,children:r,...n}){return e.jsx(La,{children:e.jsxs(Da,{"data-slot":"tooltip-content",sideOffset:a,className:h("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...n,children:[r,e.jsx(Oa,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Wa="sidebar_state",Xa=60*60*24*7,Ya="16rem",Za="18rem",Qa="3rem",Ja="b",Ye=l.createContext(null);function q(){const t=l.useContext(Ye);if(!t)throw new Error("useSidebar must be used within a SidebarProvider.");return t}function er({defaultOpen:t=!0,open:a,onOpenChange:r,className:n,style:s,children:o,...d}){const c=Ia(),[i,u]=l.useState(!1),[p,x]=l.useState(t),b=a??p,m=l.useCallback(g=>{const f=typeof g=="function"?g(b):g;r?r(f):x(f),document.cookie=`${Wa}=${f}; path=/; max-age=${Xa}`},[r,b]),v=l.useCallback(()=>c?u(g=>!g):m(g=>!g),[c,m,u]);l.useEffect(()=>{const g=f=>{f.key===Ja&&(f.metaKey||f.ctrlKey)&&(f.preventDefault(),v())};return window.addEventListener("keydown",g),()=>window.removeEventListener("keydown",g)},[v]);const j=b?"expanded":"collapsed",y=l.useMemo(()=>({state:j,open:b,setOpen:m,isMobile:c,openMobile:i,setOpenMobile:u,toggleSidebar:v}),[j,b,m,c,i,u,v]);return e.jsx(Ye.Provider,{value:y,children:e.jsx(Xe,{delayDuration:0,children:e.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Ya,"--sidebar-width-icon":Qa,...s},className:h("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n),...d,children:o})})})}function tr({side:t="left",variant:a="sidebar",collapsible:r="offcanvas",className:n,children:s,...o}){const{isMobile:d,state:c,openMobile:i,setOpenMobile:u}=q();return r==="none"?e.jsx("div",{"data-slot":"sidebar",className:h("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...o,children:s}):d?e.jsx(za,{open:i,onOpenChange:u,...o,children:e.jsxs(Ha,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":Za},side:t,children:[e.jsxs(Fa,{className:"sr-only",children:[e.jsx(qa,{children:"Sidebar"}),e.jsx(Ga,{children:"Displays the mobile sidebar."})]}),e.jsx("div",{className:"flex h-full w-full flex-col",children:s})]})}):e.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?r:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[e.jsx("div",{"data-slot":"sidebar-gap",className:h("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",a==="floating"||a==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),e.jsx("div",{"data-slot":"sidebar-container",className:h("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",t==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",a==="floating"||a==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...o,children:e.jsx("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function ar({className:t,onClick:a,...r}){const{toggleSidebar:n}=q();return e.jsxs(ye,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:h("size-7",t),onClick:s=>{a==null||a(s),n()},...r,children:[e.jsx(Wt,{}),e.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function rr({className:t,...a}){return e.jsx("main",{"data-slot":"sidebar-inset",className:h("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...a})}function nr({className:t,...a}){return e.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:h("flex flex-col gap-2 p-2",t),...a})}function sr({className:t,...a}){return e.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:h("flex flex-col gap-2 p-2",t),...a})}function or({className:t,...a}){return e.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:h("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function Ze({className:t,...a}){return e.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:h("relative flex w-full min-w-0 flex-col p-2",t),...a})}function ir({className:t,asChild:a=!1,...r}){const n=a?I:"div";return e.jsx(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:h("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...r})}function lr({className:t,...a}){return e.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:h("w-full text-sm",t),...a})}function D({className:t,...a}){return e.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:h("flex w-full min-w-0 flex-col gap-1",t),...a})}function O({className:t,...a}){return e.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:h("group/menu-item relative",t),...a})}const dr=st("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function S({asChild:t=!1,isActive:a=!1,variant:r="default",size:n="default",tooltip:s,className:o,...d}){const c=t?I:"button",{isMobile:i,state:u}=q(),p=e.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":a,className:h(dr({variant:r,size:n}),o),...d});return s?(typeof s=="string"&&(s={children:s}),e.jsxs(Ua,{children:[e.jsx(Ka,{asChild:!0,children:p}),e.jsx(Va,{side:"right",align:"center",hidden:u!=="collapsed"||i,...s})]})):p}function cr({className:t,asChild:a=!1,showOnHover:r=!1,...n}){const s=a?I:"button";return e.jsx(s,{"data-slot":"sidebar-menu-action","data-sidebar":"menu-action",className:h("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",r&&"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0",t),...n})}function ur({className:t,...a}){return e.jsx("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:h("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...a})}function pr({className:t,...a}){return e.jsx("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:h("group/menu-sub-item relative",t),...a})}function E({asChild:t=!1,size:a="md",isActive:r=!1,className:n,...s}){const o=t?I:"a";return e.jsx(o,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":a,"data-active":r,className:h("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",a==="sm"&&"text-xs",a==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",n),...s})}function hr({...t}){return e.jsx(oa,{"data-slot":"collapsible",...t})}function fe({...t}){return e.jsx(Pe,{"data-slot":"collapsible-trigger",...t})}function fr({...t}){return e.jsx(Ee,{"data-slot":"collapsible-content",...t})}function mr({items:t,customRenderers:a}){return e.jsx(wt,{className:"flex-1 min-h-0",children:e.jsx("div",{className:"p-1",children:e.jsxs(Ze,{children:[e.jsx(D,{children:e.jsx(O,{children:e.jsx(S,{asChild:!0,tooltip:"Dashboard",children:e.jsxs("a",{href:"/dashboard",className:"cursor-pointer",children:[e.jsx($t,{}),e.jsx("span",{children:"Dashboard"})]})})})}),e.jsx(ir,{children:"Distribution"}),e.jsx(D,{children:t.map(r=>{var n,s,o;return e.jsx(hr,{asChild:!0,defaultOpen:r.isActive,children:e.jsxs(O,{children:[(n=r.items)!=null&&n.length?e.jsx(fe,{asChild:!0,children:e.jsxs(S,{tooltip:r.title,className:"cursor-pointer",children:[e.jsx(r.icon,{}),e.jsx("span",{children:r.title})]})}):e.jsx(S,{asChild:!0,tooltip:r.title,children:e.jsxs("a",{href:r.url,className:"cursor-pointer",children:[e.jsx(r.icon,{}),e.jsx("span",{children:r.title})]})}),(s=r.items)!=null&&s.length?e.jsxs(e.Fragment,{children:[e.jsx(fe,{asChild:!0,children:e.jsxs(cr,{className:"data-[state=open]:rotate-90",children:[e.jsx(ke,{}),e.jsx("span",{className:"sr-only",children:"Toggle"})]})}),e.jsx(fr,{children:e.jsx(ur,{children:(o=r.items)==null?void 0:o.map(d=>e.jsx(pr,{children:a!=null&&a[d.title]?a[d.title](d):e.jsx(E,{asChild:!0,children:e.jsx("a",{href:d.url,children:e.jsx("span",{children:d.title})})})},d.title))})})]}):null]})},r.title)})})]})})})}function xr({items:t,...a}){return e.jsx(Ze,{...a,children:e.jsx(lr,{children:e.jsx(D,{children:t.map(r=>e.jsx(O,{children:e.jsx(S,{asChild:!0,size:"sm",children:e.jsxs("a",{href:r.url,children:[e.jsx(r.icon,{}),e.jsx("span",{children:r.title})]})})},r.title))})})})}function me({className:t,...a}){return e.jsx(ha,{"data-slot":"avatar",className:h("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function xe({className:t,...a}){return e.jsx(fa,{"data-slot":"avatar-image",className:h("aspect-square size-full",t),...a})}function be({className:t,...a}){return e.jsx(ma,{"data-slot":"avatar-fallback",className:h("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}function br({}){const{isMobile:t}=q(),{data:a,isPending:r}=Y.useSession(),n=je();return e.jsx(D,{children:e.jsx(O,{children:e.jsxs(Se,{children:[e.jsx(Te,{asChild:!0,children:r?e.jsxs(S,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground cursor-pointer",children:[e.jsx(M,{className:"h-8 w-8 rounded-lg"}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx(M,{className:"h-4 w-24"}),e.jsx(M,{className:"h-3 w-32"})]}),e.jsx(pe,{className:"ml-auto size-4"})]}):e.jsxs(S,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground cursor-pointer",children:[e.jsxs(me,{className:"h-8 w-8 rounded-lg",children:[e.jsx(xe,{src:(a==null?void 0:a.user.image)??void 0,alt:(a==null?void 0:a.user.name)??""}),e.jsx(be,{className:"rounded-lg",children:a==null?void 0:a.user.name.split(" ").map(s=>s.charAt(0)).join("")})]}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx("span",{className:"truncate font-medium",children:a==null?void 0:a.user.name}),e.jsx("span",{className:"truncate text-xs",children:a==null?void 0:a.user.email})]}),e.jsx(pe,{className:"ml-auto size-4"})]})}),e.jsxs(Me,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:t?"bottom":"right",align:"end",sideOffset:4,children:[e.jsx(Ct,{className:"p-0 font-normal",children:e.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:r?e.jsxs(e.Fragment,{children:[e.jsx(M,{className:"h-8 w-8 rounded-lg"}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx(M,{className:"h-4 w-24"}),e.jsx(M,{className:"h-3 w-32"})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs(me,{className:"h-8 w-8 rounded-lg",children:[e.jsx(xe,{src:(a==null?void 0:a.user.image)??void 0,alt:(a==null?void 0:a.user.name)??""}),e.jsx(be,{className:"rounded-lg",children:a==null?void 0:a.user.name.split(" ").map(s=>s.charAt(0)).join("")})]}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx("span",{className:"truncate font-medium",children:a==null?void 0:a.user.name}),e.jsx("span",{className:"truncate text-xs",children:a==null?void 0:a.user.email})]})]})})}),e.jsx(ce,{}),e.jsxs(Nt,{children:[e.jsxs(k,{className:"cursor-pointer",onClick:()=>{n({to:"/dashboard/profile"})},children:[e.jsx(aa,{}),"Profile"]}),e.jsxs(k,{children:[e.jsx(zt,{}),"Billing"]}),e.jsxs(k,{children:[e.jsx(Et,{}),"Notifications"]})]}),e.jsx(ce,{}),e.jsxs(k,{className:"cursor-pointer",onClick:()=>{Y.signOut({fetchOptions:{onSuccess:()=>{n({to:"/auth"})}}})},children:[e.jsx(Gt,{}),"Log out"]})]})]})})})}function gr(t){return a=>{switch(t.type){case"dialog":if(!t.component)throw new Error("Dialog type requires a component");const r=t.component,n={...t.props||{}};return t.onSuccess&&t.successPropName&&(n[t.successPropName]=t.onSuccess),e.jsx(r,{...n,children:e.jsx(E,{className:"cursor-pointer",children:e.jsx("span",{children:a.title})})});case"action":return e.jsx(E,{className:"cursor-pointer",onClick:t.action,children:e.jsx("span",{children:a.title})});case"navigation":return e.jsx(E,{className:"cursor-pointer",children:e.jsx("a",{href:t.navigationPath||a.url,children:e.jsx("span",{children:a.title})})});default:return e.jsx(E,{className:"cursor-pointer",children:e.jsx("a",{href:a.url,children:e.jsx("span",{children:a.title})})})}}}function vr(t){const a=je();return l.useMemo(()=>{const r={};return Object.entries(t).forEach(([n,s])=>{const o={...s};o.type==="dialog"&&o.navigationPath&&!o.onSuccess&&(o.onSuccess=()=>a({to:o.navigationPath})),r[n]=gr(o)}),r},[t,a])}const ge={navMain:[{title:"Releases",url:"#",icon:Mt,isActive:!0,items:[{title:"New Release",url:"#"},{title:"All Releases",url:"#"},{title:"Need Corrections",url:"#"}]},{title:"Tracks",url:"#",icon:_e,items:[{title:"New Track",url:"#"},{title:"All Tracks",url:"/dashboard/track"}]},{title:"Artists",url:"#",icon:Yt,items:[{title:"New Artist",url:"#"},{title:"All Artists",url:"/dashboard/artist"}]},{title:"Labels",url:"#",icon:Dt,items:[{title:"New Label",url:"#"},{title:"All Labels",url:"/dashboard/label"}]},{title:"Contributors",url:"#",icon:At,items:[{title:"New Contributor",url:"#"},{title:"All Contributors",url:"/dashboard/contributor"}]},{title:"Admin",url:"#",icon:Qt,items:[{title:"User Management",url:"/dashboard/user-management"},{title:"Team",url:"#"},{title:"Billing",url:"#"},{title:"Limits",url:"#"}]}],navSecondary:[{title:"Support",url:"#",icon:Ft},{title:"Feedback",url:"#",icon:_t}]};function jr({...t}){var o;const{data:a}=Y.useSession(),n=vr({"New Artist":{type:"dialog",component:kt,navigationPath:"/dashboard/artist",successPropName:"onArtistCreated"},"New Label":{type:"dialog",component:St,navigationPath:"/dashboard/label",successPropName:"onLabelCreated"},"New Contributor":{type:"dialog",component:Tt,navigationPath:"/dashboard/contributor",successPropName:"onContributorCreated"}}),s=l.useMemo(()=>ge.navMain.filter(d=>{var c;return d.title==="Admin"?((c=a==null?void 0:a.user)==null?void 0:c.role)==="admin":!0}),[(o=a==null?void 0:a.user)==null?void 0:o.role]);return e.jsxs(tr,{variant:"inset",...t,children:[e.jsx(nr,{children:e.jsx(D,{children:e.jsx(O,{children:e.jsx(S,{size:"lg",asChild:!0,children:e.jsxs("a",{href:"/dashboard",children:[e.jsx("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:e.jsx(_e,{className:"size-4"})}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx("span",{className:"truncate font-medium",children:"Soundmera"}),e.jsx("span",{className:"truncate text-xs",children:"Unleash your creativity!"})]})]})})})})}),e.jsxs(or,{className:"flex flex-col overflow-hidden",children:[e.jsx(mr,{items:s,customRenderers:n}),e.jsx(xr,{items:ge.navSecondary,className:"mt-auto"})]}),e.jsx(sr,{children:e.jsx(br,{})})]})}function yr(){const{setTheme:t}=Je();return e.jsxs(Se,{children:[e.jsx(Te,{asChild:!0,className:"cursor-pointer",children:e.jsxs(ye,{variant:"outline",size:"icon",children:[e.jsx(ea,{className:"h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90"}),e.jsx(Kt,{className:"absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0"}),e.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),e.jsxs(Me,{align:"end",className:"cursor-pointer",children:[e.jsx(k,{onClick:()=>t("light"),className:"cursor-pointer",children:"Light"}),e.jsx(k,{onClick:()=>t("dark"),className:"cursor-pointer",children:"Dark"}),e.jsx(k,{onClick:()=>t("system"),className:"cursor-pointer",children:"System"})]})]})}function U({...t}){return e.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...t})}function K({className:t,...a}){return e.jsx("ol",{"data-slot":"breadcrumb-list",className:h("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",t),...a})}function P({className:t,...a}){return e.jsx("li",{"data-slot":"breadcrumb-item",className:h("inline-flex items-center gap-1.5",t),...a})}function V({asChild:t,className:a,...r}){const n=t?I:"a";return e.jsx(n,{"data-slot":"breadcrumb-link",className:h("hover:text-foreground transition-colors",a),...r})}function W({className:t,...a}){return e.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:h("text-foreground font-normal",t),...a})}function X({children:t,className:a,...r}){return e.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:h("[&>svg]:size-3.5",a),...r,children:t??e.jsx(ke,{})})}const ve={"/dashboard":"Dashboard","/dashboard/":"Dashboard","/dashboard/profile":"Profile Settings","/dashboard/profile/":"Profile Settings","/dashboard/user-management":"User Management","/dashboard/user-management/":"User Management","/dashboard/artist":"Artist Management","/dashboard/artist/":"Artist Management","/dashboard/contributor":"Contributor Management","/dashboard/contributor/":"Contributor Management","/dashboard/label":"Label Management","/dashboard/label/":"Label Management","/dashboard/release":"Release Management","/dashboard/release/":"Release Management","/dashboard/release/create":"Create Release","/dashboard/release/create/":"Create Release"};function wr(t){if(ve[t])return ve[t];const a=t.split("/").filter(Boolean),r=a[a.length-1];return r?r.split("-").map(n=>n.charAt(0).toUpperCase()+n.slice(1)).join(" "):"Dashboard"}function Cr(){var c;const t=et({select:i=>i.matches}),a=tt(),n=(((c=t[t.length-1])==null?void 0:c.pathname)||"").match(/^\/dashboard\/artist\/([^\/]+)$/),s=n?n[1]:null,{data:o}=Rt(a.artist.getById.queryOptions({id:s},{enabled:!!s}));if(s&&o)return e.jsx(U,{children:e.jsxs(K,{children:[e.jsx(P,{children:e.jsx(V,{href:"/dashboard",children:"Dashboard"})}),e.jsx(X,{}),e.jsx(P,{children:e.jsx(V,{href:"/dashboard/artist",children:"Artist Management"})}),e.jsx(X,{}),e.jsx(P,{children:e.jsx(W,{children:o.name})})]})});const d=t.filter(i=>i.pathname.startsWith("/dashboard")).map((i,u,p)=>{const x=u===p.length-1;return{title:wr(i.pathname),path:i.pathname,isLast:x}}).filter((i,u,p)=>u===0||i.title!==p[u-1].title);return d.length===0?e.jsx(U,{children:e.jsx(K,{children:e.jsx(P,{children:e.jsx(W,{children:"Dashboard"})})})}):e.jsx(U,{children:e.jsx(K,{children:d.map((i,u)=>e.jsxs(at.Fragment,{children:[e.jsx(P,{children:i.isLast?e.jsx(W,{children:i.title}):e.jsx(V,{href:i.path,children:i.title})}),!i.isLast&&e.jsx(X,{})]},i.path))})})}const Wr=function(){return e.jsx(e.Fragment,{children:e.jsxs(er,{children:[e.jsx(jr,{}),e.jsxs(rr,{children:[e.jsxs("header",{className:"flex h-16 shrink-0 items-center gap-2 justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2 px-4",children:[e.jsx(ar,{className:"-ml-1 cursor-pointer"}),e.jsx(ue,{orientation:"vertical",className:"mr-2 data-[orientation=vertical]:h-4"}),e.jsx(Cr,{})]}),e.jsx("div",{className:"px-4",children:e.jsx(yr,{})})]}),e.jsx(ue,{}),e.jsx("div",{className:"flex flex-1 flex-col gap-4 p-4 overflow-auto bg-background rounded-b-xl",children:e.jsx(rt,{})})]})]})})};export{Wr as component};
