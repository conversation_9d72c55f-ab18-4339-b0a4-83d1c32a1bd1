import{u as $u,a as _u,r as Zt,t as q,j,k as nr,N as bu}from"./main-B9Fv5CdX.js";import{a as We}from"./auth-client-C8WifPV2.js";import{u as yu}from"./useForm-BIMU_iAr.js";import{B as Tt}from"./button-Ispz1G12.js";import{L as Me,I as Ve}from"./label-CNQvdrLZ.js";import{C as Et,a as At,b as Lt,c as Ct,d as Rt}from"./card-PyhbSuya.js";import{u as ku}from"./useMutation-DGkS69KN.js";const tr=Object.freeze({status:"aborted"});function c(n,t,i){function o(u,l){var s;Object.defineProperty(u,"_zod",{value:u._zod??{},enumerable:!1}),(s=u._zod).traits??(s.traits=new Set),u._zod.traits.add(n),t(u,l);for(const g in a.prototype)g in u||Object.defineProperty(u,g,{value:a.prototype[g].bind(u)});u._zod.constr=a,u._zod.def=l}const e=(i==null?void 0:i.Parent)??Object;class r extends e{}Object.defineProperty(r,"name",{value:n});function a(u){var l;const s=i!=null&&i.Parent?new r:this;o(s,u),(l=s._zod).deferred??(l.deferred=[]);for(const g of s._zod.deferred)g();return s}return Object.defineProperty(a,"init",{value:o}),Object.defineProperty(a,Symbol.hasInstance,{value:u=>{var l,s;return i!=null&&i.Parent&&u instanceof i.Parent?!0:(s=(l=u==null?void 0:u._zod)==null?void 0:l.traits)==null?void 0:s.has(n)}}),Object.defineProperty(a,"name",{value:n}),a}const rr=Symbol("zod_brand");class Q extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const he={};function E(n){return n&&Object.assign(he,n),he}function Iu(n){return n}function wu(n){return n}function zu(n){}function Su(n){throw new Error}function ju(n){}function Qe(n){const t=Object.values(n).filter(o=>typeof o=="number");return Object.entries(n).filter(([o,e])=>t.indexOf(+o)===-1).map(([o,e])=>e)}function v(n,t="|"){return n.map(i=>I(i)).join(t)}function ir(n,t){return typeof t=="bigint"?t.toString():t}function je(n){return{get value(){{const t=n();return Object.defineProperty(this,"value",{value:t}),t}}}}function B(n){return n==null}function xe(n){const t=n.startsWith("^")?1:0,i=n.endsWith("$")?n.length-1:n.length;return n.slice(t,i)}function or(n,t){const i=(n.toString().split(".")[1]||"").length,o=(t.toString().split(".")[1]||"").length,e=i>o?i:o,r=Number.parseInt(n.toFixed(e).replace(".","")),a=Number.parseInt(t.toFixed(e).replace(".",""));return r%a/10**e}function x(n,t,i){Object.defineProperty(n,t,{get(){{const o=i();return n[t]=o,o}},set(o){Object.defineProperty(n,t,{value:o})},configurable:!0})}function re(n,t,i){Object.defineProperty(n,t,{value:i,writable:!0,enumerable:!0,configurable:!0})}function xu(n,t){return t?t.reduce((i,o)=>i==null?void 0:i[o],n):n}function Nu(n){const t=Object.keys(n),i=t.map(o=>n[o]);return Promise.all(i).then(o=>{const e={};for(let r=0;r<t.length;r++)e[t[r]]=o[r];return e})}function Ou(n=10){const t="abcdefghijklmnopqrstuvwxyz";let i="";for(let o=0;o<n;o++)i+=t[Math.floor(Math.random()*t.length)];return i}function Y(n){return JSON.stringify(n)}const en=Error.captureStackTrace?Error.captureStackTrace:(...n)=>{};function ae(n){return typeof n=="object"&&n!==null&&!Array.isArray(n)}const ar=je(()=>{var n;if(typeof navigator<"u"&&((n=navigator==null?void 0:navigator.userAgent)!=null&&n.includes("Cloudflare")))return!1;try{const t=Function;return new t(""),!0}catch{return!1}});function ue(n){if(ae(n)===!1)return!1;const t=n.constructor;if(t===void 0)return!0;const i=t.prototype;return!(ae(i)===!1||Object.prototype.hasOwnProperty.call(i,"isPrototypeOf")===!1)}function Uu(n){let t=0;for(const i in n)Object.prototype.hasOwnProperty.call(n,i)&&t++;return t}const Pu=n=>{const t=typeof n;switch(t){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(n)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":return Array.isArray(n)?"array":n===null?"null":n.then&&typeof n.then=="function"&&n.catch&&typeof n.catch=="function"?"promise":typeof Map<"u"&&n instanceof Map?"map":typeof Set<"u"&&n instanceof Set?"set":typeof Date<"u"&&n instanceof Date?"date":typeof File<"u"&&n instanceof File?"file":"object";default:throw new Error(`Unknown data type: ${t}`)}},$e=new Set(["string","number","symbol"]),ur=new Set(["string","number","bigint","boolean","symbol","undefined"]);function K(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function F(n,t,i){const o=new n._zod.constr(t??n._zod.def);return(!t||i!=null&&i.parent)&&(o._zod.parent=n),o}function m(n){const t=n;if(!t)return{};if(typeof t=="string")return{error:()=>t};if((t==null?void 0:t.message)!==void 0){if((t==null?void 0:t.error)!==void 0)throw new Error("Cannot specify both `message` and `error` params");t.error=t.message}return delete t.message,typeof t.error=="string"?{...t,error:()=>t.error}:t}function Du(n){let t;return new Proxy({},{get(i,o,e){return t??(t=n()),Reflect.get(t,o,e)},set(i,o,e,r){return t??(t=n()),Reflect.set(t,o,e,r)},has(i,o){return t??(t=n()),Reflect.has(t,o)},deleteProperty(i,o){return t??(t=n()),Reflect.deleteProperty(t,o)},ownKeys(i){return t??(t=n()),Reflect.ownKeys(t)},getOwnPropertyDescriptor(i,o){return t??(t=n()),Reflect.getOwnPropertyDescriptor(t,o)},defineProperty(i,o,e){return t??(t=n()),Reflect.defineProperty(t,o,e)}})}function I(n){return typeof n=="bigint"?n.toString()+"n":typeof n=="string"?`"${n}"`:`${n}`}function cr(n){return Object.keys(n).filter(t=>n[t]._zod.optin==="optional"&&n[t]._zod.optout==="optional")}const lr={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},sr={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function dr(n,t){const i={},o=n._zod.def;for(const e in t){if(!(e in o.shape))throw new Error(`Unrecognized key: "${e}"`);t[e]&&(i[e]=o.shape[e])}return F(n,{...n._zod.def,shape:i,checks:[]})}function mr(n,t){const i={...n._zod.def.shape},o=n._zod.def;for(const e in t){if(!(e in o.shape))throw new Error(`Unrecognized key: "${e}"`);t[e]&&delete i[e]}return F(n,{...n._zod.def,shape:i,checks:[]})}function fr(n,t){if(!ue(t))throw new Error("Invalid input to extend: expected a plain object");const i={...n._zod.def,get shape(){const o={...n._zod.def.shape,...t};return re(this,"shape",o),o},checks:[]};return F(n,i)}function vr(n,t){return F(n,{...n._zod.def,get shape(){const i={...n._zod.def.shape,...t._zod.def.shape};return re(this,"shape",i),i},catchall:t._zod.def.catchall,checks:[]})}function pr(n,t,i){const o=t._zod.def.shape,e={...o};if(i)for(const r in i){if(!(r in o))throw new Error(`Unrecognized key: "${r}"`);i[r]&&(e[r]=n?new n({type:"optional",innerType:o[r]}):o[r])}else for(const r in o)e[r]=n?new n({type:"optional",innerType:o[r]}):o[r];return F(t,{...t._zod.def,shape:e,checks:[]})}function gr(n,t,i){const o=t._zod.def.shape,e={...o};if(i)for(const r in i){if(!(r in e))throw new Error(`Unrecognized key: "${r}"`);i[r]&&(e[r]=new n({type:"nonoptional",innerType:o[r]}))}else for(const r in o)e[r]=new n({type:"nonoptional",innerType:o[r]});return F(t,{...t._zod.def,shape:e,checks:[]})}function H(n,t=0){var i;for(let o=t;o<n.issues.length;o++)if(((i=n.issues[o])==null?void 0:i.continue)!==!0)return!0;return!1}function L(n,t){return t.map(i=>{var o;return(o=i).path??(o.path=[]),i.path.unshift(n),i})}function oe(n){return typeof n=="string"?n:n==null?void 0:n.message}function R(n,t,i){var e,r,a,u,l,s;const o={...n,path:n.path??[]};if(!n.message){const g=oe((a=(r=(e=n.inst)==null?void 0:e._zod.def)==null?void 0:r.error)==null?void 0:a.call(r,n))??oe((u=t==null?void 0:t.error)==null?void 0:u.call(t,n))??oe((l=i.customError)==null?void 0:l.call(i,n))??oe((s=i.localeError)==null?void 0:s.call(i,n))??"Invalid input";o.message=g}return delete o.inst,delete o.continue,t!=null&&t.reportInput||delete o.input,o}function Ne(n){return n instanceof Set?"set":n instanceof Map?"map":n instanceof File?"file":"unknown"}function Oe(n){return Array.isArray(n)?"array":typeof n=="string"?"string":"unknown"}function ee(...n){const[t,i,o]=n;return typeof t=="string"?{message:t,code:"custom",input:i,inst:o}:{...t}}function Zu(n){return Object.entries(n).filter(([t,i])=>Number.isNaN(Number.parseInt(t,10))).map(t=>t[1])}class Tu{constructor(...t){}}const Eu=Object.freeze(Object.defineProperty({__proto__:null,BIGINT_FORMAT_RANGES:sr,Class:Tu,NUMBER_FORMAT_RANGES:lr,aborted:H,allowsEval:ar,assert:ju,assertEqual:Iu,assertIs:zu,assertNever:Su,assertNotEqual:wu,assignProp:re,cached:je,captureStackTrace:en,cleanEnum:Zu,cleanRegex:xe,clone:F,createTransparentProxy:Du,defineLazy:x,esc:Y,escapeRegex:K,extend:fr,finalizeIssue:R,floatSafeRemainder:or,getElementAtPath:xu,getEnumValues:Qe,getLengthableOrigin:Oe,getParsedType:Pu,getSizableOrigin:Ne,isObject:ae,isPlainObject:ue,issue:ee,joinValues:v,jsonStringifyReplacer:ir,merge:vr,normalizeParams:m,nullish:B,numKeys:Uu,omit:mr,optionalKeys:cr,partial:pr,pick:dr,prefixIssues:L,primitiveTypes:ur,promiseAllObject:Nu,propertyKeyTypes:$e,randomString:Ou,required:gr,stringifyPrimitive:I,unwrapMessage:oe},Symbol.toStringTag,{value:"Module"})),hr=(n,t)=>{n.name="$ZodError",Object.defineProperty(n,"_zod",{value:n._zod,enumerable:!1}),Object.defineProperty(n,"issues",{value:t,enumerable:!1}),Object.defineProperty(n,"message",{get(){return JSON.stringify(t,ir,2)},enumerable:!0}),Object.defineProperty(n,"toString",{value:()=>n.message,enumerable:!1})},nn=c("$ZodError",hr),de=c("$ZodError",hr,{Parent:Error});function tn(n,t=i=>i.message){const i={},o=[];for(const e of n.issues)e.path.length>0?(i[e.path[0]]=i[e.path[0]]||[],i[e.path[0]].push(t(e))):o.push(t(e));return{formErrors:o,fieldErrors:i}}function rn(n,t){const i=t||function(r){return r.message},o={_errors:[]},e=r=>{for(const a of r.issues)if(a.code==="invalid_union"&&a.errors.length)a.errors.map(u=>e({issues:u}));else if(a.code==="invalid_key")e({issues:a.issues});else if(a.code==="invalid_element")e({issues:a.issues});else if(a.path.length===0)o._errors.push(i(a));else{let u=o,l=0;for(;l<a.path.length;){const s=a.path[l];l===a.path.length-1?(u[s]=u[s]||{_errors:[]},u[s]._errors.push(i(a))):u[s]=u[s]||{_errors:[]},u=u[s],l++}}};return e(n),o}function $r(n,t){const i=t||function(r){return r.message},o={errors:[]},e=(r,a=[])=>{var u,l;for(const s of r.issues)if(s.code==="invalid_union"&&s.errors.length)s.errors.map(g=>e({issues:g},s.path));else if(s.code==="invalid_key")e({issues:s.issues},s.path);else if(s.code==="invalid_element")e({issues:s.issues},s.path);else{const g=[...a,...s.path];if(g.length===0){o.errors.push(i(s));continue}let _=o,y=0;for(;y<g.length;){const w=g[y],h=y===g.length-1;typeof w=="string"?(_.properties??(_.properties={}),(u=_.properties)[w]??(u[w]={errors:[]}),_=_.properties[w]):(_.items??(_.items=[]),(l=_.items)[w]??(l[w]={errors:[]}),_=_.items[w]),h&&_.errors.push(i(s)),y++}}};return e(n),o}function _r(n){const t=[];for(const i of n)typeof i=="number"?t.push(`[${i}]`):typeof i=="symbol"?t.push(`[${JSON.stringify(String(i))}]`):/[^\w$]/.test(i)?t.push(`[${JSON.stringify(i)}]`):(t.length&&t.push("."),t.push(i));return t.join("")}function br(n){var o;const t=[],i=[...n.issues].sort((e,r)=>e.path.length-r.path.length);for(const e of i)t.push(`✖ ${e.message}`),(o=e.path)!=null&&o.length&&t.push(`  → at ${_r(e.path)}`);return t.join(`
`)}const on=n=>(t,i,o,e)=>{const r=o?Object.assign(o,{async:!1}):{async:!1},a=t._zod.run({value:i,issues:[]},r);if(a instanceof Promise)throw new Q;if(a.issues.length){const u=new((e==null?void 0:e.Err)??n)(a.issues.map(l=>R(l,r,E())));throw en(u,e==null?void 0:e.callee),u}return a.value},Be=on(de),an=n=>async(t,i,o,e)=>{const r=o?Object.assign(o,{async:!0}):{async:!0};let a=t._zod.run({value:i,issues:[]},r);if(a instanceof Promise&&(a=await a),a.issues.length){const u=new((e==null?void 0:e.Err)??n)(a.issues.map(l=>R(l,r,E())));throw en(u,e==null?void 0:e.callee),u}return a.value},Ke=an(de),un=n=>(t,i,o)=>{const e=o?{...o,async:!1}:{async:!1},r=t._zod.run({value:i,issues:[]},e);if(r instanceof Promise)throw new Q;return r.issues.length?{success:!1,error:new(n??nn)(r.issues.map(a=>R(a,e,E())))}:{success:!0,data:r.value}},yr=un(de),cn=n=>async(t,i,o)=>{const e=o?Object.assign(o,{async:!0}):{async:!0};let r=t._zod.run({value:i,issues:[]},e);return r instanceof Promise&&(r=await r),r.issues.length?{success:!1,error:new n(r.issues.map(a=>R(a,e,E())))}:{success:!0,data:r.value}},kr=cn(de),Ir=/^[cC][^\s-]{8,}$/,wr=/^[0-9a-z]+$/,zr=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,Sr=/^[0-9a-vA-V]{20}$/,jr=/^[A-Za-z0-9]{27}$/,xr=/^[a-zA-Z0-9_-]{21}$/,Nr=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,Au=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Or=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,ne=n=>n?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${n}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,Lu=ne(4),Cu=ne(6),Ru=ne(7),Ur=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,Fu=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Ju=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,Mu=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,Vu=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Pr="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function Dr(){return new RegExp(Pr,"u")}const Zr=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Tr=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,Er=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,Ar=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Lr=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,ln=/^[A-Za-z0-9_-]*$/,Cr=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,Gu=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,Rr=/^\+(?:[0-9]){6,14}[0-9]$/,Fr="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",Jr=new RegExp(`^${Fr}$`);function Mr(n){const t="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof n.precision=="number"?n.precision===-1?`${t}`:n.precision===0?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${n.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}function Vr(n){return new RegExp(`^${Mr(n)}$`)}function Gr(n){const t=Mr({precision:n.precision}),i=["Z"];n.local&&i.push(""),n.offset&&i.push("([+-]\\d{2}:\\d{2})");const o=`${t}(?:${i.join("|")})`;return new RegExp(`^${Fr}T(?:${o})$`)}const Wr=n=>{const t=n?`[\\s\\S]{${(n==null?void 0:n.minimum)??0},${(n==null?void 0:n.maximum)??""}}`:"[\\s\\S]*";return new RegExp(`^${t}$`)},Br=/^\d+n?$/,Kr=/^\d+$/,Xr=/^-?\d+(?:\.\d+)?/i,qr=/true|false/i,Yr=/null/i,Hr=/undefined/i,Qr=/^[^A-Z]*$/,ei=/^[^a-z]*$/,ni=Object.freeze(Object.defineProperty({__proto__:null,_emoji:Pr,base64:Lr,base64url:ln,bigint:Br,boolean:qr,browserEmail:Vu,cidrv4:Er,cidrv6:Ar,cuid:Ir,cuid2:wr,date:Jr,datetime:Gr,domain:Gu,duration:Nr,e164:Rr,email:Ur,emoji:Dr,extendedDuration:Au,guid:Or,hostname:Cr,html5Email:Fu,integer:Kr,ipv4:Zr,ipv6:Tr,ksuid:jr,lowercase:Qr,nanoid:xr,null:Yr,number:Xr,rfc5322Email:Ju,string:Wr,time:Vr,ulid:zr,undefined:Hr,unicodeEmail:Mu,uppercase:ei,uuid:ne,uuid4:Lu,uuid6:Cu,uuid7:Ru,xid:Sr},Symbol.toStringTag,{value:"Module"})),P=c("$ZodCheck",(n,t)=>{var i;n._zod??(n._zod={}),n._zod.def=t,(i=n._zod).onattach??(i.onattach=[])}),ti={number:"number",bigint:"bigint",object:"date"},sn=c("$ZodCheckLessThan",(n,t)=>{P.init(n,t);const i=ti[typeof t.value];n._zod.onattach.push(o=>{const e=o._zod.bag,r=(t.inclusive?e.maximum:e.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<r&&(t.inclusive?e.maximum=t.value:e.exclusiveMaximum=t.value)}),n._zod.check=o=>{(t.inclusive?o.value<=t.value:o.value<t.value)||o.issues.push({origin:i,code:"too_big",maximum:t.value,input:o.value,inclusive:t.inclusive,inst:n,continue:!t.abort})}}),dn=c("$ZodCheckGreaterThan",(n,t)=>{P.init(n,t);const i=ti[typeof t.value];n._zod.onattach.push(o=>{const e=o._zod.bag,r=(t.inclusive?e.minimum:e.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>r&&(t.inclusive?e.minimum=t.value:e.exclusiveMinimum=t.value)}),n._zod.check=o=>{(t.inclusive?o.value>=t.value:o.value>t.value)||o.issues.push({origin:i,code:"too_small",minimum:t.value,input:o.value,inclusive:t.inclusive,inst:n,continue:!t.abort})}}),ri=c("$ZodCheckMultipleOf",(n,t)=>{P.init(n,t),n._zod.onattach.push(i=>{var o;(o=i._zod.bag).multipleOf??(o.multipleOf=t.value)}),n._zod.check=i=>{if(typeof i.value!=typeof t.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof i.value=="bigint"?i.value%t.value===BigInt(0):or(i.value,t.value)===0)||i.issues.push({origin:typeof i.value,code:"not_multiple_of",divisor:t.value,input:i.value,inst:n,continue:!t.abort})}}),ii=c("$ZodCheckNumberFormat",(n,t)=>{var a;P.init(n,t),t.format=t.format||"float64";const i=(a=t.format)==null?void 0:a.includes("int"),o=i?"int":"number",[e,r]=lr[t.format];n._zod.onattach.push(u=>{const l=u._zod.bag;l.format=t.format,l.minimum=e,l.maximum=r,i&&(l.pattern=Kr)}),n._zod.check=u=>{const l=u.value;if(i){if(!Number.isInteger(l)){u.issues.push({expected:o,format:t.format,code:"invalid_type",input:l,inst:n});return}if(!Number.isSafeInteger(l)){l>0?u.issues.push({input:l,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:n,origin:o,continue:!t.abort}):u.issues.push({input:l,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:n,origin:o,continue:!t.abort});return}}l<e&&u.issues.push({origin:"number",input:l,code:"too_small",minimum:e,inclusive:!0,inst:n,continue:!t.abort}),l>r&&u.issues.push({origin:"number",input:l,code:"too_big",maximum:r,inst:n})}}),oi=c("$ZodCheckBigIntFormat",(n,t)=>{P.init(n,t);const[i,o]=sr[t.format];n._zod.onattach.push(e=>{const r=e._zod.bag;r.format=t.format,r.minimum=i,r.maximum=o}),n._zod.check=e=>{const r=e.value;r<i&&e.issues.push({origin:"bigint",input:r,code:"too_small",minimum:i,inclusive:!0,inst:n,continue:!t.abort}),r>o&&e.issues.push({origin:"bigint",input:r,code:"too_big",maximum:o,inst:n})}}),ai=c("$ZodCheckMaxSize",(n,t)=>{var i;P.init(n,t),(i=n._zod.def).when??(i.when=o=>{const e=o.value;return!B(e)&&e.size!==void 0}),n._zod.onattach.push(o=>{const e=o._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<e&&(o._zod.bag.maximum=t.maximum)}),n._zod.check=o=>{const e=o.value;e.size<=t.maximum||o.issues.push({origin:Ne(e),code:"too_big",maximum:t.maximum,input:e,inst:n,continue:!t.abort})}}),ui=c("$ZodCheckMinSize",(n,t)=>{var i;P.init(n,t),(i=n._zod.def).when??(i.when=o=>{const e=o.value;return!B(e)&&e.size!==void 0}),n._zod.onattach.push(o=>{const e=o._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>e&&(o._zod.bag.minimum=t.minimum)}),n._zod.check=o=>{const e=o.value;e.size>=t.minimum||o.issues.push({origin:Ne(e),code:"too_small",minimum:t.minimum,input:e,inst:n,continue:!t.abort})}}),ci=c("$ZodCheckSizeEquals",(n,t)=>{var i;P.init(n,t),(i=n._zod.def).when??(i.when=o=>{const e=o.value;return!B(e)&&e.size!==void 0}),n._zod.onattach.push(o=>{const e=o._zod.bag;e.minimum=t.size,e.maximum=t.size,e.size=t.size}),n._zod.check=o=>{const e=o.value,r=e.size;if(r===t.size)return;const a=r>t.size;o.issues.push({origin:Ne(e),...a?{code:"too_big",maximum:t.size}:{code:"too_small",minimum:t.size},inclusive:!0,exact:!0,input:o.value,inst:n,continue:!t.abort})}}),li=c("$ZodCheckMaxLength",(n,t)=>{var i;P.init(n,t),(i=n._zod.def).when??(i.when=o=>{const e=o.value;return!B(e)&&e.length!==void 0}),n._zod.onattach.push(o=>{const e=o._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<e&&(o._zod.bag.maximum=t.maximum)}),n._zod.check=o=>{const e=o.value;if(e.length<=t.maximum)return;const a=Oe(e);o.issues.push({origin:a,code:"too_big",maximum:t.maximum,inclusive:!0,input:e,inst:n,continue:!t.abort})}}),si=c("$ZodCheckMinLength",(n,t)=>{var i;P.init(n,t),(i=n._zod.def).when??(i.when=o=>{const e=o.value;return!B(e)&&e.length!==void 0}),n._zod.onattach.push(o=>{const e=o._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>e&&(o._zod.bag.minimum=t.minimum)}),n._zod.check=o=>{const e=o.value;if(e.length>=t.minimum)return;const a=Oe(e);o.issues.push({origin:a,code:"too_small",minimum:t.minimum,inclusive:!0,input:e,inst:n,continue:!t.abort})}}),di=c("$ZodCheckLengthEquals",(n,t)=>{var i;P.init(n,t),(i=n._zod.def).when??(i.when=o=>{const e=o.value;return!B(e)&&e.length!==void 0}),n._zod.onattach.push(o=>{const e=o._zod.bag;e.minimum=t.length,e.maximum=t.length,e.length=t.length}),n._zod.check=o=>{const e=o.value,r=e.length;if(r===t.length)return;const a=Oe(e),u=r>t.length;o.issues.push({origin:a,...u?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:o.value,inst:n,continue:!t.abort})}}),me=c("$ZodCheckStringFormat",(n,t)=>{var i,o;P.init(n,t),n._zod.onattach.push(e=>{const r=e._zod.bag;r.format=t.format,t.pattern&&(r.patterns??(r.patterns=new Set),r.patterns.add(t.pattern))}),t.pattern?(i=n._zod).check??(i.check=e=>{t.pattern.lastIndex=0,!t.pattern.test(e.value)&&e.issues.push({origin:"string",code:"invalid_format",format:t.format,input:e.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:n,continue:!t.abort})}):(o=n._zod).check??(o.check=()=>{})}),mi=c("$ZodCheckRegex",(n,t)=>{me.init(n,t),n._zod.check=i=>{t.pattern.lastIndex=0,!t.pattern.test(i.value)&&i.issues.push({origin:"string",code:"invalid_format",format:"regex",input:i.value,pattern:t.pattern.toString(),inst:n,continue:!t.abort})}}),fi=c("$ZodCheckLowerCase",(n,t)=>{t.pattern??(t.pattern=Qr),me.init(n,t)}),vi=c("$ZodCheckUpperCase",(n,t)=>{t.pattern??(t.pattern=ei),me.init(n,t)}),pi=c("$ZodCheckIncludes",(n,t)=>{P.init(n,t);const i=K(t.includes),o=new RegExp(typeof t.position=="number"?`^.{${t.position}}${i}`:i);t.pattern=o,n._zod.onattach.push(e=>{const r=e._zod.bag;r.patterns??(r.patterns=new Set),r.patterns.add(o)}),n._zod.check=e=>{e.value.includes(t.includes,t.position)||e.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:e.value,inst:n,continue:!t.abort})}}),gi=c("$ZodCheckStartsWith",(n,t)=>{P.init(n,t);const i=new RegExp(`^${K(t.prefix)}.*`);t.pattern??(t.pattern=i),n._zod.onattach.push(o=>{const e=o._zod.bag;e.patterns??(e.patterns=new Set),e.patterns.add(i)}),n._zod.check=o=>{o.value.startsWith(t.prefix)||o.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:o.value,inst:n,continue:!t.abort})}}),hi=c("$ZodCheckEndsWith",(n,t)=>{P.init(n,t);const i=new RegExp(`.*${K(t.suffix)}$`);t.pattern??(t.pattern=i),n._zod.onattach.push(o=>{const e=o._zod.bag;e.patterns??(e.patterns=new Set),e.patterns.add(i)}),n._zod.check=o=>{o.value.endsWith(t.suffix)||o.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:o.value,inst:n,continue:!t.abort})}});function Ft(n,t,i){n.issues.length&&t.issues.push(...L(i,n.issues))}const $i=c("$ZodCheckProperty",(n,t)=>{P.init(n,t),n._zod.check=i=>{const o=t.schema._zod.run({value:i.value[t.property],issues:[]},{});if(o instanceof Promise)return o.then(e=>Ft(e,i,t.property));Ft(o,i,t.property)}}),_i=c("$ZodCheckMimeType",(n,t)=>{P.init(n,t);const i=new Set(t.mime);n._zod.onattach.push(o=>{o._zod.bag.mime=t.mime}),n._zod.check=o=>{i.has(o.value.type)||o.issues.push({code:"invalid_value",values:t.mime,input:o.value.type,inst:n})}}),bi=c("$ZodCheckOverwrite",(n,t)=>{P.init(n,t),n._zod.check=i=>{i.value=t.tx(i.value)}});class yi{constructor(t=[]){this.content=[],this.indent=0,this&&(this.args=t)}indented(t){this.indent+=1,t(this),this.indent-=1}write(t){if(typeof t=="function"){t(this,{execution:"sync"}),t(this,{execution:"async"});return}const o=t.split(`
`).filter(a=>a),e=Math.min(...o.map(a=>a.length-a.trimStart().length)),r=o.map(a=>a.slice(e)).map(a=>" ".repeat(this.indent*2)+a);for(const a of r)this.content.push(a)}compile(){const t=Function,i=this==null?void 0:this.args,e=[...((this==null?void 0:this.content)??[""]).map(r=>`  ${r}`)];return new t(...i,e.join(`
`))}}const ki={major:4,minor:0,patch:0},k=c("$ZodType",(n,t)=>{var e;var i;n??(n={}),n._zod.def=t,n._zod.bag=n._zod.bag||{},n._zod.version=ki;const o=[...n._zod.def.checks??[]];n._zod.traits.has("$ZodCheck")&&o.unshift(n);for(const r of o)for(const a of r._zod.onattach)a(n);if(o.length===0)(i=n._zod).deferred??(i.deferred=[]),(e=n._zod.deferred)==null||e.push(()=>{n._zod.run=n._zod.parse});else{const r=(a,u,l)=>{let s=H(a),g;for(const _ of u){if(_._zod.def.when){if(!_._zod.def.when(a))continue}else if(s)continue;const y=a.issues.length,w=_._zod.check(a);if(w instanceof Promise&&(l==null?void 0:l.async)===!1)throw new Q;if(g||w instanceof Promise)g=(g??Promise.resolve()).then(async()=>{await w,a.issues.length!==y&&(s||(s=H(a,y)))});else{if(a.issues.length===y)continue;s||(s=H(a,y))}}return g?g.then(()=>a):a};n._zod.run=(a,u)=>{const l=n._zod.parse(a,u);if(l instanceof Promise){if(u.async===!1)throw new Q;return l.then(s=>r(s,o,u))}return r(l,o,u)}}n["~standard"]={validate:r=>{var a;try{const u=yr(n,r);return u.success?{value:u.data}:{issues:(a=u.error)==null?void 0:a.issues}}catch{return kr(n,r).then(l=>{var s;return l.success?{value:l.data}:{issues:(s=l.error)==null?void 0:s.issues}})}},vendor:"zod",version:1}}),fe=c("$ZodString",(n,t)=>{var i;k.init(n,t),n._zod.pattern=[...((i=n==null?void 0:n._zod.bag)==null?void 0:i.patterns)??[]].pop()??Wr(n._zod.bag),n._zod.parse=(o,e)=>{if(t.coerce)try{o.value=String(o.value)}catch{}return typeof o.value=="string"||o.issues.push({expected:"string",code:"invalid_type",input:o.value,inst:n}),o}}),O=c("$ZodStringFormat",(n,t)=>{me.init(n,t),fe.init(n,t)}),Ii=c("$ZodGUID",(n,t)=>{t.pattern??(t.pattern=Or),O.init(n,t)}),wi=c("$ZodUUID",(n,t)=>{if(t.version){const o={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(o===void 0)throw new Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=ne(o))}else t.pattern??(t.pattern=ne());O.init(n,t)}),zi=c("$ZodEmail",(n,t)=>{t.pattern??(t.pattern=Ur),O.init(n,t)}),Si=c("$ZodURL",(n,t)=>{O.init(n,t),n._zod.check=i=>{try{const o=i.value,e=new URL(o),r=e.href;t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(e.hostname)||i.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:Cr.source,input:i.value,inst:n,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||i.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:i.value,inst:n,continue:!t.abort})),!o.endsWith("/")&&r.endsWith("/")?i.value=r.slice(0,-1):i.value=r;return}catch{i.issues.push({code:"invalid_format",format:"url",input:i.value,inst:n,continue:!t.abort})}}}),ji=c("$ZodEmoji",(n,t)=>{t.pattern??(t.pattern=Dr()),O.init(n,t)}),xi=c("$ZodNanoID",(n,t)=>{t.pattern??(t.pattern=xr),O.init(n,t)}),Ni=c("$ZodCUID",(n,t)=>{t.pattern??(t.pattern=Ir),O.init(n,t)}),Oi=c("$ZodCUID2",(n,t)=>{t.pattern??(t.pattern=wr),O.init(n,t)}),Ui=c("$ZodULID",(n,t)=>{t.pattern??(t.pattern=zr),O.init(n,t)}),Pi=c("$ZodXID",(n,t)=>{t.pattern??(t.pattern=Sr),O.init(n,t)}),Di=c("$ZodKSUID",(n,t)=>{t.pattern??(t.pattern=jr),O.init(n,t)}),Zi=c("$ZodISODateTime",(n,t)=>{t.pattern??(t.pattern=Gr(t)),O.init(n,t)}),Ti=c("$ZodISODate",(n,t)=>{t.pattern??(t.pattern=Jr),O.init(n,t)}),Ei=c("$ZodISOTime",(n,t)=>{t.pattern??(t.pattern=Vr(t)),O.init(n,t)}),Ai=c("$ZodISODuration",(n,t)=>{t.pattern??(t.pattern=Nr),O.init(n,t)}),Li=c("$ZodIPv4",(n,t)=>{t.pattern??(t.pattern=Zr),O.init(n,t),n._zod.onattach.push(i=>{const o=i._zod.bag;o.format="ipv4"})}),Ci=c("$ZodIPv6",(n,t)=>{t.pattern??(t.pattern=Tr),O.init(n,t),n._zod.onattach.push(i=>{const o=i._zod.bag;o.format="ipv6"}),n._zod.check=i=>{try{new URL(`http://[${i.value}]`)}catch{i.issues.push({code:"invalid_format",format:"ipv6",input:i.value,inst:n,continue:!t.abort})}}}),Ri=c("$ZodCIDRv4",(n,t)=>{t.pattern??(t.pattern=Er),O.init(n,t)}),Fi=c("$ZodCIDRv6",(n,t)=>{t.pattern??(t.pattern=Ar),O.init(n,t),n._zod.check=i=>{const[o,e]=i.value.split("/");try{if(!e)throw new Error;const r=Number(e);if(`${r}`!==e)throw new Error;if(r<0||r>128)throw new Error;new URL(`http://[${o}]`)}catch{i.issues.push({code:"invalid_format",format:"cidrv6",input:i.value,inst:n,continue:!t.abort})}}});function mn(n){if(n==="")return!0;if(n.length%4!==0)return!1;try{return atob(n),!0}catch{return!1}}const Ji=c("$ZodBase64",(n,t)=>{t.pattern??(t.pattern=Lr),O.init(n,t),n._zod.onattach.push(i=>{i._zod.bag.contentEncoding="base64"}),n._zod.check=i=>{mn(i.value)||i.issues.push({code:"invalid_format",format:"base64",input:i.value,inst:n,continue:!t.abort})}});function Mi(n){if(!ln.test(n))return!1;const t=n.replace(/[-_]/g,o=>o==="-"?"+":"/"),i=t.padEnd(Math.ceil(t.length/4)*4,"=");return mn(i)}const Vi=c("$ZodBase64URL",(n,t)=>{t.pattern??(t.pattern=ln),O.init(n,t),n._zod.onattach.push(i=>{i._zod.bag.contentEncoding="base64url"}),n._zod.check=i=>{Mi(i.value)||i.issues.push({code:"invalid_format",format:"base64url",input:i.value,inst:n,continue:!t.abort})}}),Gi=c("$ZodE164",(n,t)=>{t.pattern??(t.pattern=Rr),O.init(n,t)});function Wi(n,t=null){try{const i=n.split(".");if(i.length!==3)return!1;const[o]=i;if(!o)return!1;const e=JSON.parse(atob(o));return!("typ"in e&&(e==null?void 0:e.typ)!=="JWT"||!e.alg||t&&(!("alg"in e)||e.alg!==t))}catch{return!1}}const Bi=c("$ZodJWT",(n,t)=>{O.init(n,t),n._zod.check=i=>{Wi(i.value,t.alg)||i.issues.push({code:"invalid_format",format:"jwt",input:i.value,inst:n,continue:!t.abort})}}),Ki=c("$ZodCustomStringFormat",(n,t)=>{O.init(n,t),n._zod.check=i=>{t.fn(i.value)||i.issues.push({code:"invalid_format",format:t.format,input:i.value,inst:n,continue:!t.abort})}}),fn=c("$ZodNumber",(n,t)=>{k.init(n,t),n._zod.pattern=n._zod.bag.pattern??Xr,n._zod.parse=(i,o)=>{if(t.coerce)try{i.value=Number(i.value)}catch{}const e=i.value;if(typeof e=="number"&&!Number.isNaN(e)&&Number.isFinite(e))return i;const r=typeof e=="number"?Number.isNaN(e)?"NaN":Number.isFinite(e)?void 0:"Infinity":void 0;return i.issues.push({expected:"number",code:"invalid_type",input:e,inst:n,...r?{received:r}:{}}),i}}),Xi=c("$ZodNumber",(n,t)=>{ii.init(n,t),fn.init(n,t)}),vn=c("$ZodBoolean",(n,t)=>{k.init(n,t),n._zod.pattern=qr,n._zod.parse=(i,o)=>{if(t.coerce)try{i.value=!!i.value}catch{}const e=i.value;return typeof e=="boolean"||i.issues.push({expected:"boolean",code:"invalid_type",input:e,inst:n}),i}}),pn=c("$ZodBigInt",(n,t)=>{k.init(n,t),n._zod.pattern=Br,n._zod.parse=(i,o)=>{if(t.coerce)try{i.value=BigInt(i.value)}catch{}return typeof i.value=="bigint"||i.issues.push({expected:"bigint",code:"invalid_type",input:i.value,inst:n}),i}}),qi=c("$ZodBigInt",(n,t)=>{oi.init(n,t),pn.init(n,t)}),Yi=c("$ZodSymbol",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=i.value;return typeof e=="symbol"||i.issues.push({expected:"symbol",code:"invalid_type",input:e,inst:n}),i}}),Hi=c("$ZodUndefined",(n,t)=>{k.init(n,t),n._zod.pattern=Hr,n._zod.values=new Set([void 0]),n._zod.optin="optional",n._zod.optout="optional",n._zod.parse=(i,o)=>{const e=i.value;return typeof e>"u"||i.issues.push({expected:"undefined",code:"invalid_type",input:e,inst:n}),i}}),Qi=c("$ZodNull",(n,t)=>{k.init(n,t),n._zod.pattern=Yr,n._zod.values=new Set([null]),n._zod.parse=(i,o)=>{const e=i.value;return e===null||i.issues.push({expected:"null",code:"invalid_type",input:e,inst:n}),i}}),eo=c("$ZodAny",(n,t)=>{k.init(n,t),n._zod.parse=i=>i}),_e=c("$ZodUnknown",(n,t)=>{k.init(n,t),n._zod.parse=i=>i}),no=c("$ZodNever",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>(i.issues.push({expected:"never",code:"invalid_type",input:i.value,inst:n}),i)}),to=c("$ZodVoid",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=i.value;return typeof e>"u"||i.issues.push({expected:"void",code:"invalid_type",input:e,inst:n}),i}}),ro=c("$ZodDate",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{if(t.coerce)try{i.value=new Date(i.value)}catch{}const e=i.value,r=e instanceof Date;return r&&!Number.isNaN(e.getTime())||i.issues.push({expected:"date",code:"invalid_type",input:e,...r?{received:"Invalid Date"}:{},inst:n}),i}});function Jt(n,t,i){n.issues.length&&t.issues.push(...L(i,n.issues)),t.value[i]=n.value}const gn=c("$ZodArray",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=i.value;if(!Array.isArray(e))return i.issues.push({expected:"array",code:"invalid_type",input:e,inst:n}),i;i.value=Array(e.length);const r=[];for(let a=0;a<e.length;a++){const u=e[a],l=t.element._zod.run({value:u,issues:[]},o);l instanceof Promise?r.push(l.then(s=>Jt(s,i,a))):Jt(l,i,a)}return r.length?Promise.all(r).then(()=>i):i}});function pe(n,t,i){n.issues.length&&t.issues.push(...L(i,n.issues)),t.value[i]=n.value}function Mt(n,t,i,o){n.issues.length?o[i]===void 0?i in o?t.value[i]=void 0:t.value[i]=n.value:t.issues.push(...L(i,n.issues)):n.value===void 0?i in o&&(t.value[i]=void 0):t.value[i]=n.value}const io=c("$ZodObject",(n,t)=>{k.init(n,t);const i=je(()=>{const _=Object.keys(t.shape);for(const w of _)if(!(t.shape[w]instanceof k))throw new Error(`Invalid element at key "${w}": expected a Zod schema`);const y=cr(t.shape);return{shape:t.shape,keys:_,keySet:new Set(_),numKeys:_.length,optionalKeys:new Set(y)}});x(n._zod,"propValues",()=>{const _=t.shape,y={};for(const w in _){const h=_[w]._zod;if(h.values){y[w]??(y[w]=new Set);for(const D of h.values)y[w].add(D)}}return y});const o=_=>{const y=new yi(["shape","payload","ctx"]),w=i.value,h=p=>{const f=Y(p);return`shape[${f}]._zod.run({ value: input[${f}], issues: [] }, ctx)`};y.write("const input = payload.value;");const D=Object.create(null);let $=0;for(const p of w.keys)D[p]=`key_${$++}`;y.write("const newResult = {}");for(const p of w.keys)if(w.optionalKeys.has(p)){const f=D[p];y.write(`const ${f} = ${h(p)};`);const b=Y(p);y.write(`
        if (${f}.issues.length) {
          if (input[${b}] === undefined) {
            if (${b} in input) {
              newResult[${b}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${f}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${b}, ...iss.path] : [${b}],
              }))
            );
          }
        } else if (${f}.value === undefined) {
          if (${b} in input) newResult[${b}] = undefined;
        } else {
          newResult[${b}] = ${f}.value;
        }
        `)}else{const f=D[p];y.write(`const ${f} = ${h(p)};`),y.write(`
          if (${f}.issues.length) payload.issues = payload.issues.concat(${f}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${Y(p)}, ...iss.path] : [${Y(p)}]
          })));`),y.write(`newResult[${Y(p)}] = ${f}.value`)}y.write("payload.value = newResult;"),y.write("return payload;");const d=y.compile();return(p,f)=>d(_,p,f)};let e;const r=ae,a=!he.jitless,l=a&&ar.value,s=t.catchall;let g;n._zod.parse=(_,y)=>{g??(g=i.value);const w=_.value;if(!r(w))return _.issues.push({expected:"object",code:"invalid_type",input:w,inst:n}),_;const h=[];if(a&&l&&(y==null?void 0:y.async)===!1&&y.jitless!==!0)e||(e=o(t.shape)),_=e(_,y);else{_.value={};const f=g.shape;for(const b of g.keys){const z=f[b],N=z._zod.run({value:w[b],issues:[]},y),T=z._zod.optin==="optional"&&z._zod.optout==="optional";N instanceof Promise?h.push(N.then(M=>T?Mt(M,_,b,w):pe(M,_,b))):T?Mt(N,_,b,w):pe(N,_,b)}}if(!s)return h.length?Promise.all(h).then(()=>_):_;const D=[],$=g.keySet,d=s._zod,p=d.def.type;for(const f of Object.keys(w)){if($.has(f))continue;if(p==="never"){D.push(f);continue}const b=d.run({value:w[f],issues:[]},y);b instanceof Promise?h.push(b.then(z=>pe(z,_,f))):pe(b,_,f)}return D.length&&_.issues.push({code:"unrecognized_keys",keys:D,input:w,inst:n}),h.length?Promise.all(h).then(()=>_):_}});function Vt(n,t,i,o){for(const e of n)if(e.issues.length===0)return t.value=e.value,t;return t.issues.push({code:"invalid_union",input:t.value,inst:i,errors:n.map(e=>e.issues.map(r=>R(r,o,E())))}),t}const hn=c("$ZodUnion",(n,t)=>{k.init(n,t),x(n._zod,"optin",()=>t.options.some(i=>i._zod.optin==="optional")?"optional":void 0),x(n._zod,"optout",()=>t.options.some(i=>i._zod.optout==="optional")?"optional":void 0),x(n._zod,"values",()=>{if(t.options.every(i=>i._zod.values))return new Set(t.options.flatMap(i=>Array.from(i._zod.values)))}),x(n._zod,"pattern",()=>{if(t.options.every(i=>i._zod.pattern)){const i=t.options.map(o=>o._zod.pattern);return new RegExp(`^(${i.map(o=>xe(o.source)).join("|")})$`)}}),n._zod.parse=(i,o)=>{let e=!1;const r=[];for(const a of t.options){const u=a._zod.run({value:i.value,issues:[]},o);if(u instanceof Promise)r.push(u),e=!0;else{if(u.issues.length===0)return u;r.push(u)}}return e?Promise.all(r).then(a=>Vt(a,i,n,o)):Vt(r,i,n,o)}}),oo=c("$ZodDiscriminatedUnion",(n,t)=>{hn.init(n,t);const i=n._zod.parse;x(n._zod,"propValues",()=>{const e={};for(const r of t.options){const a=r._zod.propValues;if(!a||Object.keys(a).length===0)throw new Error(`Invalid discriminated union option at index "${t.options.indexOf(r)}"`);for(const[u,l]of Object.entries(a)){e[u]||(e[u]=new Set);for(const s of l)e[u].add(s)}}return e});const o=je(()=>{const e=t.options,r=new Map;for(const a of e){const u=a._zod.propValues[t.discriminator];if(!u||u.size===0)throw new Error(`Invalid discriminated union option at index "${t.options.indexOf(a)}"`);for(const l of u){if(r.has(l))throw new Error(`Duplicate discriminator value "${String(l)}"`);r.set(l,a)}}return r});n._zod.parse=(e,r)=>{const a=e.value;if(!ae(a))return e.issues.push({code:"invalid_type",expected:"object",input:a,inst:n}),e;const u=o.value.get(a==null?void 0:a[t.discriminator]);return u?u._zod.run(e,r):t.unionFallback?i(e,r):(e.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",input:a,path:[t.discriminator],inst:n}),e)}}),ao=c("$ZodIntersection",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=i.value,r=t.left._zod.run({value:e,issues:[]},o),a=t.right._zod.run({value:e,issues:[]},o);return r instanceof Promise||a instanceof Promise?Promise.all([r,a]).then(([l,s])=>Gt(i,l,s)):Gt(i,r,a)}});function Xe(n,t){if(n===t)return{valid:!0,data:n};if(n instanceof Date&&t instanceof Date&&+n==+t)return{valid:!0,data:n};if(ue(n)&&ue(t)){const i=Object.keys(t),o=Object.keys(n).filter(r=>i.indexOf(r)!==-1),e={...n,...t};for(const r of o){const a=Xe(n[r],t[r]);if(!a.valid)return{valid:!1,mergeErrorPath:[r,...a.mergeErrorPath]};e[r]=a.data}return{valid:!0,data:e}}if(Array.isArray(n)&&Array.isArray(t)){if(n.length!==t.length)return{valid:!1,mergeErrorPath:[]};const i=[];for(let o=0;o<n.length;o++){const e=n[o],r=t[o],a=Xe(e,r);if(!a.valid)return{valid:!1,mergeErrorPath:[o,...a.mergeErrorPath]};i.push(a.data)}return{valid:!0,data:i}}return{valid:!1,mergeErrorPath:[]}}function Gt(n,t,i){if(t.issues.length&&n.issues.push(...t.issues),i.issues.length&&n.issues.push(...i.issues),H(n))return n;const o=Xe(t.value,i.value);if(!o.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(o.mergeErrorPath)}`);return n.value=o.data,n}const Ue=c("$ZodTuple",(n,t)=>{k.init(n,t);const i=t.items,o=i.length-[...i].reverse().findIndex(e=>e._zod.optin!=="optional");n._zod.parse=(e,r)=>{const a=e.value;if(!Array.isArray(a))return e.issues.push({input:a,inst:n,expected:"tuple",code:"invalid_type"}),e;e.value=[];const u=[];if(!t.rest){const s=a.length>i.length,g=a.length<o-1;if(s||g)return e.issues.push({input:a,inst:n,origin:"array",...s?{code:"too_big",maximum:i.length}:{code:"too_small",minimum:i.length}}),e}let l=-1;for(const s of i){if(l++,l>=a.length&&l>=o)continue;const g=s._zod.run({value:a[l],issues:[]},r);g instanceof Promise?u.push(g.then(_=>ge(_,e,l))):ge(g,e,l)}if(t.rest){const s=a.slice(i.length);for(const g of s){l++;const _=t.rest._zod.run({value:g,issues:[]},r);_ instanceof Promise?u.push(_.then(y=>ge(y,e,l))):ge(_,e,l)}}return u.length?Promise.all(u).then(()=>e):e}});function ge(n,t,i){n.issues.length&&t.issues.push(...L(i,n.issues)),t.value[i]=n.value}const uo=c("$ZodRecord",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=i.value;if(!ue(e))return i.issues.push({expected:"record",code:"invalid_type",input:e,inst:n}),i;const r=[];if(t.keyType._zod.values){const a=t.keyType._zod.values;i.value={};for(const l of a)if(typeof l=="string"||typeof l=="number"||typeof l=="symbol"){const s=t.valueType._zod.run({value:e[l],issues:[]},o);s instanceof Promise?r.push(s.then(g=>{g.issues.length&&i.issues.push(...L(l,g.issues)),i.value[l]=g.value})):(s.issues.length&&i.issues.push(...L(l,s.issues)),i.value[l]=s.value)}let u;for(const l in e)a.has(l)||(u=u??[],u.push(l));u&&u.length>0&&i.issues.push({code:"unrecognized_keys",input:e,inst:n,keys:u})}else{i.value={};for(const a of Reflect.ownKeys(e)){if(a==="__proto__")continue;const u=t.keyType._zod.run({value:a,issues:[]},o);if(u instanceof Promise)throw new Error("Async schemas not supported in object keys currently");if(u.issues.length){i.issues.push({origin:"record",code:"invalid_key",issues:u.issues.map(s=>R(s,o,E())),input:a,path:[a],inst:n}),i.value[u.value]=u.value;continue}const l=t.valueType._zod.run({value:e[a],issues:[]},o);l instanceof Promise?r.push(l.then(s=>{s.issues.length&&i.issues.push(...L(a,s.issues)),i.value[u.value]=s.value})):(l.issues.length&&i.issues.push(...L(a,l.issues)),i.value[u.value]=l.value)}}return r.length?Promise.all(r).then(()=>i):i}}),co=c("$ZodMap",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=i.value;if(!(e instanceof Map))return i.issues.push({expected:"map",code:"invalid_type",input:e,inst:n}),i;const r=[];i.value=new Map;for(const[a,u]of e){const l=t.keyType._zod.run({value:a,issues:[]},o),s=t.valueType._zod.run({value:u,issues:[]},o);l instanceof Promise||s instanceof Promise?r.push(Promise.all([l,s]).then(([g,_])=>{Wt(g,_,i,a,e,n,o)})):Wt(l,s,i,a,e,n,o)}return r.length?Promise.all(r).then(()=>i):i}});function Wt(n,t,i,o,e,r,a){n.issues.length&&($e.has(typeof o)?i.issues.push(...L(o,n.issues)):i.issues.push({origin:"map",code:"invalid_key",input:e,inst:r,issues:n.issues.map(u=>R(u,a,E()))})),t.issues.length&&($e.has(typeof o)?i.issues.push(...L(o,t.issues)):i.issues.push({origin:"map",code:"invalid_element",input:e,inst:r,key:o,issues:t.issues.map(u=>R(u,a,E()))})),i.value.set(n.value,t.value)}const lo=c("$ZodSet",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=i.value;if(!(e instanceof Set))return i.issues.push({input:e,inst:n,expected:"set",code:"invalid_type"}),i;const r=[];i.value=new Set;for(const a of e){const u=t.valueType._zod.run({value:a,issues:[]},o);u instanceof Promise?r.push(u.then(l=>Bt(l,i))):Bt(u,i)}return r.length?Promise.all(r).then(()=>i):i}});function Bt(n,t){n.issues.length&&t.issues.push(...n.issues),t.value.add(n.value)}const so=c("$ZodEnum",(n,t)=>{k.init(n,t);const i=Qe(t.entries);n._zod.values=new Set(i),n._zod.pattern=new RegExp(`^(${i.filter(o=>$e.has(typeof o)).map(o=>typeof o=="string"?K(o):o.toString()).join("|")})$`),n._zod.parse=(o,e)=>{const r=o.value;return n._zod.values.has(r)||o.issues.push({code:"invalid_value",values:i,input:r,inst:n}),o}}),mo=c("$ZodLiteral",(n,t)=>{k.init(n,t),n._zod.values=new Set(t.values),n._zod.pattern=new RegExp(`^(${t.values.map(i=>typeof i=="string"?K(i):i?i.toString():String(i)).join("|")})$`),n._zod.parse=(i,o)=>{const e=i.value;return n._zod.values.has(e)||i.issues.push({code:"invalid_value",values:t.values,input:e,inst:n}),i}}),fo=c("$ZodFile",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=i.value;return e instanceof File||i.issues.push({expected:"file",code:"invalid_type",input:e,inst:n}),i}}),$n=c("$ZodTransform",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=t.transform(i.value,i);if(o.async)return(e instanceof Promise?e:Promise.resolve(e)).then(a=>(i.value=a,i));if(e instanceof Promise)throw new Q;return i.value=e,i}}),vo=c("$ZodOptional",(n,t)=>{k.init(n,t),n._zod.optin="optional",n._zod.optout="optional",x(n._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),x(n._zod,"pattern",()=>{const i=t.innerType._zod.pattern;return i?new RegExp(`^(${xe(i.source)})?$`):void 0}),n._zod.parse=(i,o)=>t.innerType._zod.optin==="optional"?t.innerType._zod.run(i,o):i.value===void 0?i:t.innerType._zod.run(i,o)}),po=c("$ZodNullable",(n,t)=>{k.init(n,t),x(n._zod,"optin",()=>t.innerType._zod.optin),x(n._zod,"optout",()=>t.innerType._zod.optout),x(n._zod,"pattern",()=>{const i=t.innerType._zod.pattern;return i?new RegExp(`^(${xe(i.source)}|null)$`):void 0}),x(n._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),n._zod.parse=(i,o)=>i.value===null?i:t.innerType._zod.run(i,o)}),go=c("$ZodDefault",(n,t)=>{k.init(n,t),n._zod.optin="optional",x(n._zod,"values",()=>t.innerType._zod.values),n._zod.parse=(i,o)=>{if(i.value===void 0)return i.value=t.defaultValue,i;const e=t.innerType._zod.run(i,o);return e instanceof Promise?e.then(r=>Kt(r,t)):Kt(e,t)}});function Kt(n,t){return n.value===void 0&&(n.value=t.defaultValue),n}const ho=c("$ZodPrefault",(n,t)=>{k.init(n,t),n._zod.optin="optional",x(n._zod,"values",()=>t.innerType._zod.values),n._zod.parse=(i,o)=>(i.value===void 0&&(i.value=t.defaultValue),t.innerType._zod.run(i,o))}),$o=c("$ZodNonOptional",(n,t)=>{k.init(n,t),x(n._zod,"values",()=>{const i=t.innerType._zod.values;return i?new Set([...i].filter(o=>o!==void 0)):void 0}),n._zod.parse=(i,o)=>{const e=t.innerType._zod.run(i,o);return e instanceof Promise?e.then(r=>Xt(r,n)):Xt(e,n)}});function Xt(n,t){return!n.issues.length&&n.value===void 0&&n.issues.push({code:"invalid_type",expected:"nonoptional",input:n.value,inst:t}),n}const _o=c("$ZodSuccess",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>{const e=t.innerType._zod.run(i,o);return e instanceof Promise?e.then(r=>(i.value=r.issues.length===0,i)):(i.value=e.issues.length===0,i)}}),bo=c("$ZodCatch",(n,t)=>{k.init(n,t),n._zod.optin="optional",x(n._zod,"optout",()=>t.innerType._zod.optout),x(n._zod,"values",()=>t.innerType._zod.values),n._zod.parse=(i,o)=>{const e=t.innerType._zod.run(i,o);return e instanceof Promise?e.then(r=>(i.value=r.value,r.issues.length&&(i.value=t.catchValue({...i,error:{issues:r.issues.map(a=>R(a,o,E()))},input:i.value}),i.issues=[]),i)):(i.value=e.value,e.issues.length&&(i.value=t.catchValue({...i,error:{issues:e.issues.map(r=>R(r,o,E()))},input:i.value}),i.issues=[]),i)}}),yo=c("$ZodNaN",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>((typeof i.value!="number"||!Number.isNaN(i.value))&&i.issues.push({input:i.value,inst:n,expected:"nan",code:"invalid_type"}),i)}),_n=c("$ZodPipe",(n,t)=>{k.init(n,t),x(n._zod,"values",()=>t.in._zod.values),x(n._zod,"optin",()=>t.in._zod.optin),x(n._zod,"optout",()=>t.out._zod.optout),n._zod.parse=(i,o)=>{const e=t.in._zod.run(i,o);return e instanceof Promise?e.then(r=>qt(r,t,o)):qt(e,t,o)}});function qt(n,t,i){return H(n)?n:t.out._zod.run({value:n.value,issues:n.issues},i)}const ko=c("$ZodReadonly",(n,t)=>{k.init(n,t),x(n._zod,"propValues",()=>t.innerType._zod.propValues),x(n._zod,"values",()=>t.innerType._zod.values),x(n._zod,"optin",()=>t.innerType._zod.optin),x(n._zod,"optout",()=>t.innerType._zod.optout),n._zod.parse=(i,o)=>{const e=t.innerType._zod.run(i,o);return e instanceof Promise?e.then(Yt):Yt(e)}});function Yt(n){return n.value=Object.freeze(n.value),n}const Io=c("$ZodTemplateLiteral",(n,t)=>{k.init(n,t);const i=[];for(const o of t.parts)if(o instanceof k){if(!o._zod.pattern)throw new Error(`Invalid template literal part, no pattern found: ${[...o._zod.traits].shift()}`);const e=o._zod.pattern instanceof RegExp?o._zod.pattern.source:o._zod.pattern;if(!e)throw new Error(`Invalid template literal part: ${o._zod.traits}`);const r=e.startsWith("^")?1:0,a=e.endsWith("$")?e.length-1:e.length;i.push(e.slice(r,a))}else if(o===null||ur.has(typeof o))i.push(K(`${o}`));else throw new Error(`Invalid template literal part: ${o}`);n._zod.pattern=new RegExp(`^${i.join("")}$`),n._zod.parse=(o,e)=>typeof o.value!="string"?(o.issues.push({input:o.value,inst:n,expected:"template_literal",code:"invalid_type"}),o):(n._zod.pattern.lastIndex=0,n._zod.pattern.test(o.value)||o.issues.push({input:o.value,inst:n,code:"invalid_format",format:"template_literal",pattern:n._zod.pattern.source}),o)}),wo=c("$ZodPromise",(n,t)=>{k.init(n,t),n._zod.parse=(i,o)=>Promise.resolve(i.value).then(e=>t.innerType._zod.run({value:e,issues:[]},o))}),zo=c("$ZodLazy",(n,t)=>{k.init(n,t),x(n._zod,"innerType",()=>t.getter()),x(n._zod,"pattern",()=>n._zod.innerType._zod.pattern),x(n._zod,"propValues",()=>n._zod.innerType._zod.propValues),x(n._zod,"optin",()=>n._zod.innerType._zod.optin),x(n._zod,"optout",()=>n._zod.innerType._zod.optout),n._zod.parse=(i,o)=>n._zod.innerType._zod.run(i,o)}),So=c("$ZodCustom",(n,t)=>{P.init(n,t),k.init(n,t),n._zod.parse=(i,o)=>i,n._zod.check=i=>{const o=i.value,e=t.fn(o);if(e instanceof Promise)return e.then(r=>Ht(r,i,o,n));Ht(e,i,o,n)}});function Ht(n,t,i,o){if(!n){const e={code:"custom",input:i,inst:o,path:[...o._zod.def.path??[]],continue:!o._zod.def.abort};o._zod.def.params&&(e.params=o._zod.def.params),t.issues.push(ee(e))}}const Wu=()=>{const n={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return e=>{switch(e.code){case"invalid_type":return`مدخلات غير مقبولة: يفترض إدخال ${e.expected}، ولكن تم إدخال ${i(e.input)}`;case"invalid_value":return e.values.length===1?`مدخلات غير مقبولة: يفترض إدخال ${I(e.values[0])}`:`اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?` أكبر من اللازم: يفترض أن تكون ${e.origin??"القيمة"} ${r} ${e.maximum.toString()} ${a.unit??"عنصر"}`:`أكبر من اللازم: يفترض أن تكون ${e.origin??"القيمة"} ${r} ${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`أصغر من اللازم: يفترض لـ ${e.origin} أن يكون ${r} ${e.minimum.toString()} ${a.unit}`:`أصغر من اللازم: يفترض لـ ${e.origin} أن يكون ${r} ${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`نَص غير مقبول: يجب أن يبدأ بـ "${e.prefix}"`:r.format==="ends_with"?`نَص غير مقبول: يجب أن ينتهي بـ "${r.suffix}"`:r.format==="includes"?`نَص غير مقبول: يجب أن يتضمَّن "${r.includes}"`:r.format==="regex"?`نَص غير مقبول: يجب أن يطابق النمط ${r.pattern}`:`${o[r.format]??e.format} غير مقبول`}case"not_multiple_of":return`رقم غير مقبول: يجب أن يكون من مضاعفات ${e.divisor}`;case"unrecognized_keys":return`معرف${e.keys.length>1?"ات":""} غريب${e.keys.length>1?"ة":""}: ${v(e.keys,"، ")}`;case"invalid_key":return`معرف غير مقبول في ${e.origin}`;case"invalid_union":return"مدخل غير مقبول";case"invalid_element":return`مدخل غير مقبول في ${e.origin}`;default:return"مدخل غير مقبول"}}};function Bu(){return{localeError:Wu()}}const Ku=()=>{const n={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return e=>{switch(e.code){case"invalid_type":return`Yanlış dəyər: gözlənilən ${e.expected}, daxil olan ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Yanlış dəyər: gözlənilən ${I(e.values[0])}`:`Yanlış seçim: aşağıdakılardan biri olmalıdır: ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Çox böyük: gözlənilən ${e.origin??"dəyər"} ${r}${e.maximum.toString()} ${a.unit??"element"}`:`Çox böyük: gözlənilən ${e.origin??"dəyər"} ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Çox kiçik: gözlənilən ${e.origin} ${r}${e.minimum.toString()} ${a.unit}`:`Çox kiçik: gözlənilən ${e.origin} ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Yanlış mətn: "${r.prefix}" ilə başlamalıdır`:r.format==="ends_with"?`Yanlış mətn: "${r.suffix}" ilə bitməlidir`:r.format==="includes"?`Yanlış mətn: "${r.includes}" daxil olmalıdır`:r.format==="regex"?`Yanlış mətn: ${r.pattern} şablonuna uyğun olmalıdır`:`Yanlış ${o[r.format]??e.format}`}case"not_multiple_of":return`Yanlış ədəd: ${e.divisor} ilə bölünə bilən olmalıdır`;case"unrecognized_keys":return`Tanınmayan açar${e.keys.length>1?"lar":""}: ${v(e.keys,", ")}`;case"invalid_key":return`${e.origin} daxilində yanlış açar`;case"invalid_union":return"Yanlış dəyər";case"invalid_element":return`${e.origin} daxilində yanlış dəyər`;default:return"Yanlış dəyər"}}};function Xu(){return{localeError:Ku()}}function Qt(n,t,i,o){const e=Math.abs(n),r=e%10,a=e%100;return a>=11&&a<=19?o:r===1?t:r>=2&&r<=4?i:o}const qu=()=>{const n={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"лік";case"object":{if(Array.isArray(e))return"масіў";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return e=>{switch(e.code){case"invalid_type":return`Няправільны ўвод: чакаўся ${e.expected}, атрымана ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Няправільны ўвод: чакалася ${I(e.values[0])}`:`Няправільны варыянт: чакаўся адзін з ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);if(a){const u=Number(e.maximum),l=Qt(u,a.unit.one,a.unit.few,a.unit.many);return`Занадта вялікі: чакалася, што ${e.origin??"значэнне"} павінна ${a.verb} ${r}${e.maximum.toString()} ${l}`}return`Занадта вялікі: чакалася, што ${e.origin??"значэнне"} павінна быць ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);if(a){const u=Number(e.minimum),l=Qt(u,a.unit.one,a.unit.few,a.unit.many);return`Занадта малы: чакалася, што ${e.origin} павінна ${a.verb} ${r}${e.minimum.toString()} ${l}`}return`Занадта малы: чакалася, што ${e.origin} павінна быць ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Няправільны радок: павінен пачынацца з "${r.prefix}"`:r.format==="ends_with"?`Няправільны радок: павінен заканчвацца на "${r.suffix}"`:r.format==="includes"?`Няправільны радок: павінен змяшчаць "${r.includes}"`:r.format==="regex"?`Няправільны радок: павінен адпавядаць шаблону ${r.pattern}`:`Няправільны ${o[r.format]??e.format}`}case"not_multiple_of":return`Няправільны лік: павінен быць кратным ${e.divisor}`;case"unrecognized_keys":return`Нераспазнаны ${e.keys.length>1?"ключы":"ключ"}: ${v(e.keys,", ")}`;case"invalid_key":return`Няправільны ключ у ${e.origin}`;case"invalid_union":return"Няправільны ўвод";case"invalid_element":return`Няправільнае значэнне ў ${e.origin}`;default:return"Няправільны ўвод"}}};function Yu(){return{localeError:qu()}}const Hu=()=>{const n={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{switch(e.code){case"invalid_type":return`Tipus invàlid: s'esperava ${e.expected}, s'ha rebut ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Valor invàlid: s'esperava ${I(e.values[0])}`:`Opció invàlida: s'esperava una de ${v(e.values," o ")}`;case"too_big":{const r=e.inclusive?"com a màxim":"menys de",a=t(e.origin);return a?`Massa gran: s'esperava que ${e.origin??"el valor"} contingués ${r} ${e.maximum.toString()} ${a.unit??"elements"}`:`Massa gran: s'esperava que ${e.origin??"el valor"} fos ${r} ${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?"com a mínim":"més de",a=t(e.origin);return a?`Massa petit: s'esperava que ${e.origin} contingués ${r} ${e.minimum.toString()} ${a.unit}`:`Massa petit: s'esperava que ${e.origin} fos ${r} ${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Format invàlid: ha de començar amb "${r.prefix}"`:r.format==="ends_with"?`Format invàlid: ha d'acabar amb "${r.suffix}"`:r.format==="includes"?`Format invàlid: ha d'incloure "${r.includes}"`:r.format==="regex"?`Format invàlid: ha de coincidir amb el patró ${r.pattern}`:`Format invàlid per a ${o[r.format]??e.format}`}case"not_multiple_of":return`Número invàlid: ha de ser múltiple de ${e.divisor}`;case"unrecognized_keys":return`Clau${e.keys.length>1?"s":""} no reconeguda${e.keys.length>1?"s":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Clau invàlida a ${e.origin}`;case"invalid_union":return"Entrada invàlida";case"invalid_element":return`Element invàlid a ${e.origin}`;default:return"Entrada invàlida"}}};function Qu(){return{localeError:Hu()}}const ec=()=>{const n={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":{if(Array.isArray(e))return"pole";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return e=>{switch(e.code){case"invalid_type":return`Neplatný vstup: očekáváno ${e.expected}, obdrženo ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Neplatný vstup: očekáváno ${I(e.values[0])}`:`Neplatná možnost: očekávána jedna z hodnot ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Hodnota je příliš velká: ${e.origin??"hodnota"} musí mít ${r}${e.maximum.toString()} ${a.unit??"prvků"}`:`Hodnota je příliš velká: ${e.origin??"hodnota"} musí být ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Hodnota je příliš malá: ${e.origin??"hodnota"} musí mít ${r}${e.minimum.toString()} ${a.unit??"prvků"}`:`Hodnota je příliš malá: ${e.origin??"hodnota"} musí být ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Neplatný řetězec: musí začínat na "${r.prefix}"`:r.format==="ends_with"?`Neplatný řetězec: musí končit na "${r.suffix}"`:r.format==="includes"?`Neplatný řetězec: musí obsahovat "${r.includes}"`:r.format==="regex"?`Neplatný řetězec: musí odpovídat vzoru ${r.pattern}`:`Neplatný formát ${o[r.format]??e.format}`}case"not_multiple_of":return`Neplatné číslo: musí být násobkem ${e.divisor}`;case"unrecognized_keys":return`Neznámé klíče: ${v(e.keys,", ")}`;case"invalid_key":return`Neplatný klíč v ${e.origin}`;case"invalid_union":return"Neplatný vstup";case"invalid_element":return`Neplatná hodnota v ${e.origin}`;default:return"Neplatný vstup"}}};function nc(){return{localeError:ec()}}const tc=()=>{const n={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"Zahl";case"object":{if(Array.isArray(e))return"Array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return e=>{switch(e.code){case"invalid_type":return`Ungültige Eingabe: erwartet ${e.expected}, erhalten ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Ungültige Eingabe: erwartet ${I(e.values[0])}`:`Ungültige Option: erwartet eine von ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Zu groß: erwartet, dass ${e.origin??"Wert"} ${r}${e.maximum.toString()} ${a.unit??"Elemente"} hat`:`Zu groß: erwartet, dass ${e.origin??"Wert"} ${r}${e.maximum.toString()} ist`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Zu klein: erwartet, dass ${e.origin} ${r}${e.minimum.toString()} ${a.unit} hat`:`Zu klein: erwartet, dass ${e.origin} ${r}${e.minimum.toString()} ist`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Ungültiger String: muss mit "${r.prefix}" beginnen`:r.format==="ends_with"?`Ungültiger String: muss mit "${r.suffix}" enden`:r.format==="includes"?`Ungültiger String: muss "${r.includes}" enthalten`:r.format==="regex"?`Ungültiger String: muss dem Muster ${r.pattern} entsprechen`:`Ungültig: ${o[r.format]??e.format}`}case"not_multiple_of":return`Ungültige Zahl: muss ein Vielfaches von ${e.divisor} sein`;case"unrecognized_keys":return`${e.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel"}: ${v(e.keys,", ")}`;case"invalid_key":return`Ungültiger Schlüssel in ${e.origin}`;case"invalid_union":return"Ungültige Eingabe";case"invalid_element":return`Ungültiger Wert in ${e.origin}`;default:return"Ungültige Eingabe"}}};function rc(){return{localeError:tc()}}const ic=n=>{const t=typeof n;switch(t){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return t},oc=()=>{const n={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}};function t(o){return n[o]??null}const i={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return o=>{switch(o.code){case"invalid_type":return`Invalid input: expected ${o.expected}, received ${ic(o.input)}`;case"invalid_value":return o.values.length===1?`Invalid input: expected ${I(o.values[0])}`:`Invalid option: expected one of ${v(o.values,"|")}`;case"too_big":{const e=o.inclusive?"<=":"<",r=t(o.origin);return r?`Too big: expected ${o.origin??"value"} to have ${e}${o.maximum.toString()} ${r.unit??"elements"}`:`Too big: expected ${o.origin??"value"} to be ${e}${o.maximum.toString()}`}case"too_small":{const e=o.inclusive?">=":">",r=t(o.origin);return r?`Too small: expected ${o.origin} to have ${e}${o.minimum.toString()} ${r.unit}`:`Too small: expected ${o.origin} to be ${e}${o.minimum.toString()}`}case"invalid_format":{const e=o;return e.format==="starts_with"?`Invalid string: must start with "${e.prefix}"`:e.format==="ends_with"?`Invalid string: must end with "${e.suffix}"`:e.format==="includes"?`Invalid string: must include "${e.includes}"`:e.format==="regex"?`Invalid string: must match pattern ${e.pattern}`:`Invalid ${i[e.format]??o.format}`}case"not_multiple_of":return`Invalid number: must be a multiple of ${o.divisor}`;case"unrecognized_keys":return`Unrecognized key${o.keys.length>1?"s":""}: ${v(o.keys,", ")}`;case"invalid_key":return`Invalid key in ${o.origin}`;case"invalid_union":return"Invalid input";case"invalid_element":return`Invalid value in ${o.origin}`;default:return"Invalid input"}}};function jo(){return{localeError:oc()}}const ac=n=>{const t=typeof n;switch(t){case"number":return Number.isNaN(n)?"NaN":"nombro";case"object":{if(Array.isArray(n))return"tabelo";if(n===null)return"senvalora";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return t},uc=()=>{const n={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}};function t(o){return n[o]??null}const i={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return o=>{switch(o.code){case"invalid_type":return`Nevalida enigo: atendiĝis ${o.expected}, riceviĝis ${ac(o.input)}`;case"invalid_value":return o.values.length===1?`Nevalida enigo: atendiĝis ${I(o.values[0])}`:`Nevalida opcio: atendiĝis unu el ${v(o.values,"|")}`;case"too_big":{const e=o.inclusive?"<=":"<",r=t(o.origin);return r?`Tro granda: atendiĝis ke ${o.origin??"valoro"} havu ${e}${o.maximum.toString()} ${r.unit??"elementojn"}`:`Tro granda: atendiĝis ke ${o.origin??"valoro"} havu ${e}${o.maximum.toString()}`}case"too_small":{const e=o.inclusive?">=":">",r=t(o.origin);return r?`Tro malgranda: atendiĝis ke ${o.origin} havu ${e}${o.minimum.toString()} ${r.unit}`:`Tro malgranda: atendiĝis ke ${o.origin} estu ${e}${o.minimum.toString()}`}case"invalid_format":{const e=o;return e.format==="starts_with"?`Nevalida karaktraro: devas komenciĝi per "${e.prefix}"`:e.format==="ends_with"?`Nevalida karaktraro: devas finiĝi per "${e.suffix}"`:e.format==="includes"?`Nevalida karaktraro: devas inkluzivi "${e.includes}"`:e.format==="regex"?`Nevalida karaktraro: devas kongrui kun la modelo ${e.pattern}`:`Nevalida ${i[e.format]??o.format}`}case"not_multiple_of":return`Nevalida nombro: devas esti oblo de ${o.divisor}`;case"unrecognized_keys":return`Nekonata${o.keys.length>1?"j":""} ŝlosilo${o.keys.length>1?"j":""}: ${v(o.keys,", ")}`;case"invalid_key":return`Nevalida ŝlosilo en ${o.origin}`;case"invalid_union":return"Nevalida enigo";case"invalid_element":return`Nevalida valoro en ${o.origin}`;default:return"Nevalida enigo"}}};function cc(){return{localeError:uc()}}const lc=()=>{const n={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"número";case"object":{if(Array.isArray(e))return"arreglo";if(e===null)return"nulo";if(Object.getPrototypeOf(e)!==Object.prototype)return e.constructor.name}}return r},o={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{switch(e.code){case"invalid_type":return`Entrada inválida: se esperaba ${e.expected}, recibido ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Entrada inválida: se esperaba ${I(e.values[0])}`:`Opción inválida: se esperaba una de ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Demasiado grande: se esperaba que ${e.origin??"valor"} tuviera ${r}${e.maximum.toString()} ${a.unit??"elementos"}`:`Demasiado grande: se esperaba que ${e.origin??"valor"} fuera ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Demasiado pequeño: se esperaba que ${e.origin} tuviera ${r}${e.minimum.toString()} ${a.unit}`:`Demasiado pequeño: se esperaba que ${e.origin} fuera ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Cadena inválida: debe comenzar con "${r.prefix}"`:r.format==="ends_with"?`Cadena inválida: debe terminar en "${r.suffix}"`:r.format==="includes"?`Cadena inválida: debe incluir "${r.includes}"`:r.format==="regex"?`Cadena inválida: debe coincidir con el patrón ${r.pattern}`:`Inválido ${o[r.format]??e.format}`}case"not_multiple_of":return`Número inválido: debe ser múltiplo de ${e.divisor}`;case"unrecognized_keys":return`Llave${e.keys.length>1?"s":""} desconocida${e.keys.length>1?"s":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Llave inválida en ${e.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inválido en ${e.origin}`;default:return"Entrada inválida"}}};function sc(){return{localeError:lc()}}const dc=()=>{const n={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"عدد";case"object":{if(Array.isArray(e))return"آرایه";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return e=>{switch(e.code){case"invalid_type":return`ورودی نامعتبر: می‌بایست ${e.expected} می‌بود، ${i(e.input)} دریافت شد`;case"invalid_value":return e.values.length===1?`ورودی نامعتبر: می‌بایست ${I(e.values[0])} می‌بود`:`گزینه نامعتبر: می‌بایست یکی از ${v(e.values,"|")} می‌بود`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`خیلی بزرگ: ${e.origin??"مقدار"} باید ${r}${e.maximum.toString()} ${a.unit??"عنصر"} باشد`:`خیلی بزرگ: ${e.origin??"مقدار"} باید ${r}${e.maximum.toString()} باشد`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`خیلی کوچک: ${e.origin} باید ${r}${e.minimum.toString()} ${a.unit} باشد`:`خیلی کوچک: ${e.origin} باید ${r}${e.minimum.toString()} باشد`}case"invalid_format":{const r=e;return r.format==="starts_with"?`رشته نامعتبر: باید با "${r.prefix}" شروع شود`:r.format==="ends_with"?`رشته نامعتبر: باید با "${r.suffix}" تمام شود`:r.format==="includes"?`رشته نامعتبر: باید شامل "${r.includes}" باشد`:r.format==="regex"?`رشته نامعتبر: باید با الگوی ${r.pattern} مطابقت داشته باشد`:`${o[r.format]??e.format} نامعتبر`}case"not_multiple_of":return`عدد نامعتبر: باید مضرب ${e.divisor} باشد`;case"unrecognized_keys":return`کلید${e.keys.length>1?"های":""} ناشناس: ${v(e.keys,", ")}`;case"invalid_key":return`کلید ناشناس در ${e.origin}`;case"invalid_union":return"ورودی نامعتبر";case"invalid_element":return`مقدار نامعتبر در ${e.origin}`;default:return"ورودی نامعتبر"}}};function mc(){return{localeError:dc()}}const fc=()=>{const n={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return e=>{switch(e.code){case"invalid_type":return`Virheellinen tyyppi: odotettiin ${e.expected}, oli ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Virheellinen syöte: täytyy olla ${I(e.values[0])}`:`Virheellinen valinta: täytyy olla yksi seuraavista: ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Liian suuri: ${a.subject} täytyy olla ${r}${e.maximum.toString()} ${a.unit}`.trim():`Liian suuri: arvon täytyy olla ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Liian pieni: ${a.subject} täytyy olla ${r}${e.minimum.toString()} ${a.unit}`.trim():`Liian pieni: arvon täytyy olla ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Virheellinen syöte: täytyy alkaa "${r.prefix}"`:r.format==="ends_with"?`Virheellinen syöte: täytyy loppua "${r.suffix}"`:r.format==="includes"?`Virheellinen syöte: täytyy sisältää "${r.includes}"`:r.format==="regex"?`Virheellinen syöte: täytyy vastata säännöllistä lauseketta ${r.pattern}`:`Virheellinen ${o[r.format]??e.format}`}case"not_multiple_of":return`Virheellinen luku: täytyy olla luvun ${e.divisor} monikerta`;case"unrecognized_keys":return`${e.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain"}: ${v(e.keys,", ")}`;case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return"Virheellinen syöte"}}};function vc(){return{localeError:fc()}}const pc=()=>{const n={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"nombre";case"object":{if(Array.isArray(e))return"tableau";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return e=>{switch(e.code){case"invalid_type":return`Entrée invalide : ${e.expected} attendu, ${i(e.input)} reçu`;case"invalid_value":return e.values.length===1?`Entrée invalide : ${I(e.values[0])} attendu`:`Option invalide : une valeur parmi ${v(e.values,"|")} attendue`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Trop grand : ${e.origin??"valeur"} doit ${a.verb} ${r}${e.maximum.toString()} ${a.unit??"élément(s)"}`:`Trop grand : ${e.origin??"valeur"} doit être ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Trop petit : ${e.origin} doit ${a.verb} ${r}${e.minimum.toString()} ${a.unit}`:`Trop petit : ${e.origin} doit être ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Chaîne invalide : doit commencer par "${r.prefix}"`:r.format==="ends_with"?`Chaîne invalide : doit se terminer par "${r.suffix}"`:r.format==="includes"?`Chaîne invalide : doit inclure "${r.includes}"`:r.format==="regex"?`Chaîne invalide : doit correspondre au modèle ${r.pattern}`:`${o[r.format]??e.format} invalide`}case"not_multiple_of":return`Nombre invalide : doit être un multiple de ${e.divisor}`;case"unrecognized_keys":return`Clé${e.keys.length>1?"s":""} non reconnue${e.keys.length>1?"s":""} : ${v(e.keys,", ")}`;case"invalid_key":return`Clé invalide dans ${e.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${e.origin}`;default:return"Entrée invalide"}}};function gc(){return{localeError:pc()}}const hc=()=>{const n={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return e=>{switch(e.code){case"invalid_type":return`Entrée invalide : attendu ${e.expected}, reçu ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Entrée invalide : attendu ${I(e.values[0])}`:`Option invalide : attendu l'une des valeurs suivantes ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"≤":"<",a=t(e.origin);return a?`Trop grand : attendu que ${e.origin??"la valeur"} ait ${r}${e.maximum.toString()} ${a.unit}`:`Trop grand : attendu que ${e.origin??"la valeur"} soit ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?"≥":">",a=t(e.origin);return a?`Trop petit : attendu que ${e.origin} ait ${r}${e.minimum.toString()} ${a.unit}`:`Trop petit : attendu que ${e.origin} soit ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Chaîne invalide : doit commencer par "${r.prefix}"`:r.format==="ends_with"?`Chaîne invalide : doit se terminer par "${r.suffix}"`:r.format==="includes"?`Chaîne invalide : doit inclure "${r.includes}"`:r.format==="regex"?`Chaîne invalide : doit correspondre au motif ${r.pattern}`:`${o[r.format]??e.format} invalide`}case"not_multiple_of":return`Nombre invalide : doit être un multiple de ${e.divisor}`;case"unrecognized_keys":return`Clé${e.keys.length>1?"s":""} non reconnue${e.keys.length>1?"s":""} : ${v(e.keys,", ")}`;case"invalid_key":return`Clé invalide dans ${e.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${e.origin}`;default:return"Entrée invalide"}}};function $c(){return{localeError:hc()}}const _c=()=>{const n={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return e=>{switch(e.code){case"invalid_type":return`קלט לא תקין: צריך ${e.expected}, התקבל ${i(e.input)}`;case"invalid_value":return e.values.length===1?`קלט לא תקין: צריך ${I(e.values[0])}`:`קלט לא תקין: צריך אחת מהאפשרויות  ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`גדול מדי: ${e.origin??"value"} צריך להיות ${r}${e.maximum.toString()} ${a.unit??"elements"}`:`גדול מדי: ${e.origin??"value"} צריך להיות ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`קטן מדי: ${e.origin} צריך להיות ${r}${e.minimum.toString()} ${a.unit}`:`קטן מדי: ${e.origin} צריך להיות ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`מחרוזת לא תקינה: חייבת להתחיל ב"${r.prefix}"`:r.format==="ends_with"?`מחרוזת לא תקינה: חייבת להסתיים ב "${r.suffix}"`:r.format==="includes"?`מחרוזת לא תקינה: חייבת לכלול "${r.includes}"`:r.format==="regex"?`מחרוזת לא תקינה: חייבת להתאים לתבנית ${r.pattern}`:`${o[r.format]??e.format} לא תקין`}case"not_multiple_of":return`מספר לא תקין: חייב להיות מכפלה של ${e.divisor}`;case"unrecognized_keys":return`מפתח${e.keys.length>1?"ות":""} לא מזוה${e.keys.length>1?"ים":"ה"}: ${v(e.keys,", ")}`;case"invalid_key":return`מפתח לא תקין ב${e.origin}`;case"invalid_union":return"קלט לא תקין";case"invalid_element":return`ערך לא תקין ב${e.origin}`;default:return"קלט לא תקין"}}};function bc(){return{localeError:_c()}}const yc=()=>{const n={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"szám";case"object":{if(Array.isArray(e))return"tömb";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return e=>{switch(e.code){case"invalid_type":return`Érvénytelen bemenet: a várt érték ${e.expected}, a kapott érték ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Érvénytelen bemenet: a várt érték ${I(e.values[0])}`:`Érvénytelen opció: valamelyik érték várt ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Túl nagy: ${e.origin??"érték"} mérete túl nagy ${r}${e.maximum.toString()} ${a.unit??"elem"}`:`Túl nagy: a bemeneti érték ${e.origin??"érték"} túl nagy: ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Túl kicsi: a bemeneti érték ${e.origin} mérete túl kicsi ${r}${e.minimum.toString()} ${a.unit}`:`Túl kicsi: a bemeneti érték ${e.origin} túl kicsi ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Érvénytelen string: "${r.prefix}" értékkel kell kezdődnie`:r.format==="ends_with"?`Érvénytelen string: "${r.suffix}" értékkel kell végződnie`:r.format==="includes"?`Érvénytelen string: "${r.includes}" értéket kell tartalmaznia`:r.format==="regex"?`Érvénytelen string: ${r.pattern} mintának kell megfelelnie`:`Érvénytelen ${o[r.format]??e.format}`}case"not_multiple_of":return`Érvénytelen szám: ${e.divisor} többszörösének kell lennie`;case"unrecognized_keys":return`Ismeretlen kulcs${e.keys.length>1?"s":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Érvénytelen kulcs ${e.origin}`;case"invalid_union":return"Érvénytelen bemenet";case"invalid_element":return`Érvénytelen érték: ${e.origin}`;default:return"Érvénytelen bemenet"}}};function kc(){return{localeError:yc()}}const Ic=()=>{const n={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return e=>{switch(e.code){case"invalid_type":return`Input tidak valid: diharapkan ${e.expected}, diterima ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Input tidak valid: diharapkan ${I(e.values[0])}`:`Pilihan tidak valid: diharapkan salah satu dari ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Terlalu besar: diharapkan ${e.origin??"value"} memiliki ${r}${e.maximum.toString()} ${a.unit??"elemen"}`:`Terlalu besar: diharapkan ${e.origin??"value"} menjadi ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Terlalu kecil: diharapkan ${e.origin} memiliki ${r}${e.minimum.toString()} ${a.unit}`:`Terlalu kecil: diharapkan ${e.origin} menjadi ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`String tidak valid: harus dimulai dengan "${r.prefix}"`:r.format==="ends_with"?`String tidak valid: harus berakhir dengan "${r.suffix}"`:r.format==="includes"?`String tidak valid: harus menyertakan "${r.includes}"`:r.format==="regex"?`String tidak valid: harus sesuai pola ${r.pattern}`:`${o[r.format]??e.format} tidak valid`}case"not_multiple_of":return`Angka tidak valid: harus kelipatan dari ${e.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali ${e.keys.length>1?"s":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Kunci tidak valid di ${e.origin}`;case"invalid_union":return"Input tidak valid";case"invalid_element":return`Nilai tidak valid di ${e.origin}`;default:return"Input tidak valid"}}};function wc(){return{localeError:Ic()}}const zc=()=>{const n={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"numero";case"object":{if(Array.isArray(e))return"vettore";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return e=>{switch(e.code){case"invalid_type":return`Input non valido: atteso ${e.expected}, ricevuto ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Input non valido: atteso ${I(e.values[0])}`:`Opzione non valida: atteso uno tra ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Troppo grande: ${e.origin??"valore"} deve avere ${r}${e.maximum.toString()} ${a.unit??"elementi"}`:`Troppo grande: ${e.origin??"valore"} deve essere ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Troppo piccolo: ${e.origin} deve avere ${r}${e.minimum.toString()} ${a.unit}`:`Troppo piccolo: ${e.origin} deve essere ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Stringa non valida: deve iniziare con "${r.prefix}"`:r.format==="ends_with"?`Stringa non valida: deve terminare con "${r.suffix}"`:r.format==="includes"?`Stringa non valida: deve includere "${r.includes}"`:r.format==="regex"?`Stringa non valida: deve corrispondere al pattern ${r.pattern}`:`Invalid ${o[r.format]??e.format}`}case"not_multiple_of":return`Numero non valido: deve essere un multiplo di ${e.divisor}`;case"unrecognized_keys":return`Chiav${e.keys.length>1?"i":"e"} non riconosciut${e.keys.length>1?"e":"a"}: ${v(e.keys,", ")}`;case"invalid_key":return`Chiave non valida in ${e.origin}`;case"invalid_union":return"Input non valido";case"invalid_element":return`Valore non valido in ${e.origin}`;default:return"Input non valido"}}};function Sc(){return{localeError:zc()}}const jc=()=>{const n={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"数値";case"object":{if(Array.isArray(e))return"配列";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return e=>{switch(e.code){case"invalid_type":return`無効な入力: ${e.expected}が期待されましたが、${i(e.input)}が入力されました`;case"invalid_value":return e.values.length===1?`無効な入力: ${I(e.values[0])}が期待されました`:`無効な選択: ${v(e.values,"、")}のいずれかである必要があります`;case"too_big":{const r=e.inclusive?"以下である":"より小さい",a=t(e.origin);return a?`大きすぎる値: ${e.origin??"値"}は${e.maximum.toString()}${a.unit??"要素"}${r}必要があります`:`大きすぎる値: ${e.origin??"値"}は${e.maximum.toString()}${r}必要があります`}case"too_small":{const r=e.inclusive?"以上である":"より大きい",a=t(e.origin);return a?`小さすぎる値: ${e.origin}は${e.minimum.toString()}${a.unit}${r}必要があります`:`小さすぎる値: ${e.origin}は${e.minimum.toString()}${r}必要があります`}case"invalid_format":{const r=e;return r.format==="starts_with"?`無効な文字列: "${r.prefix}"で始まる必要があります`:r.format==="ends_with"?`無効な文字列: "${r.suffix}"で終わる必要があります`:r.format==="includes"?`無効な文字列: "${r.includes}"を含む必要があります`:r.format==="regex"?`無効な文字列: パターン${r.pattern}に一致する必要があります`:`無効な${o[r.format]??e.format}`}case"not_multiple_of":return`無効な数値: ${e.divisor}の倍数である必要があります`;case"unrecognized_keys":return`認識されていないキー${e.keys.length>1?"群":""}: ${v(e.keys,"、")}`;case"invalid_key":return`${e.origin}内の無効なキー`;case"invalid_union":return"無効な入力";case"invalid_element":return`${e.origin}内の無効な値`;default:return"無効な入力"}}};function xc(){return{localeError:jc()}}const Nc=()=>{const n={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":{if(Array.isArray(e))return"អារេ (Array)";if(e===null)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return e=>{switch(e.code){case"invalid_type":return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${e.expected} ប៉ុន្តែទទួលបាន ${i(e.input)}`;case"invalid_value":return e.values.length===1?`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${I(e.values[0])}`:`ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`ធំពេក៖ ត្រូវការ ${e.origin??"តម្លៃ"} ${r} ${e.maximum.toString()} ${a.unit??"ធាតុ"}`:`ធំពេក៖ ត្រូវការ ${e.origin??"តម្លៃ"} ${r} ${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`តូចពេក៖ ត្រូវការ ${e.origin} ${r} ${e.minimum.toString()} ${a.unit}`:`តូចពេក៖ ត្រូវការ ${e.origin} ${r} ${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "${r.prefix}"`:r.format==="ends_with"?`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "${r.suffix}"`:r.format==="includes"?`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "${r.includes}"`:r.format==="regex"?`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ${r.pattern}`:`មិនត្រឹមត្រូវ៖ ${o[r.format]??e.format}`}case"not_multiple_of":return`លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ${e.divisor}`;case"unrecognized_keys":return`រកឃើញសោមិនស្គាល់៖ ${v(e.keys,", ")}`;case"invalid_key":return`សោមិនត្រឹមត្រូវនៅក្នុង ${e.origin}`;case"invalid_union":return"ទិន្នន័យមិនត្រឹមត្រូវ";case"invalid_element":return`ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ${e.origin}`;default:return"ទិន្នន័យមិនត្រឹមត្រូវ"}}};function Oc(){return{localeError:Nc()}}const Uc=()=>{const n={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return e=>{switch(e.code){case"invalid_type":return`잘못된 입력: 예상 타입은 ${e.expected}, 받은 타입은 ${i(e.input)}입니다`;case"invalid_value":return e.values.length===1?`잘못된 입력: 값은 ${I(e.values[0])} 이어야 합니다`:`잘못된 옵션: ${v(e.values,"또는 ")} 중 하나여야 합니다`;case"too_big":{const r=e.inclusive?"이하":"미만",a=r==="미만"?"이어야 합니다":"여야 합니다",u=t(e.origin),l=(u==null?void 0:u.unit)??"요소";return u?`${e.origin??"값"}이 너무 큽니다: ${e.maximum.toString()}${l} ${r}${a}`:`${e.origin??"값"}이 너무 큽니다: ${e.maximum.toString()} ${r}${a}`}case"too_small":{const r=e.inclusive?"이상":"초과",a=r==="이상"?"이어야 합니다":"여야 합니다",u=t(e.origin),l=(u==null?void 0:u.unit)??"요소";return u?`${e.origin??"값"}이 너무 작습니다: ${e.minimum.toString()}${l} ${r}${a}`:`${e.origin??"값"}이 너무 작습니다: ${e.minimum.toString()} ${r}${a}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`잘못된 문자열: "${r.prefix}"(으)로 시작해야 합니다`:r.format==="ends_with"?`잘못된 문자열: "${r.suffix}"(으)로 끝나야 합니다`:r.format==="includes"?`잘못된 문자열: "${r.includes}"을(를) 포함해야 합니다`:r.format==="regex"?`잘못된 문자열: 정규식 ${r.pattern} 패턴과 일치해야 합니다`:`잘못된 ${o[r.format]??e.format}`}case"not_multiple_of":return`잘못된 숫자: ${e.divisor}의 배수여야 합니다`;case"unrecognized_keys":return`인식할 수 없는 키: ${v(e.keys,", ")}`;case"invalid_key":return`잘못된 키: ${e.origin}`;case"invalid_union":return"잘못된 입력";case"invalid_element":return`잘못된 값: ${e.origin}`;default:return"잘못된 입력"}}};function Pc(){return{localeError:Uc()}}const Dc=()=>{const n={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"број";case"object":{if(Array.isArray(e))return"низа";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return e=>{switch(e.code){case"invalid_type":return`Грешен внес: се очекува ${e.expected}, примено ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Invalid input: expected ${I(e.values[0])}`:`Грешана опција: се очекува една ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Премногу голем: се очекува ${e.origin??"вредноста"} да има ${r}${e.maximum.toString()} ${a.unit??"елементи"}`:`Премногу голем: се очекува ${e.origin??"вредноста"} да биде ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Премногу мал: се очекува ${e.origin} да има ${r}${e.minimum.toString()} ${a.unit}`:`Премногу мал: се очекува ${e.origin} да биде ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Неважечка низа: мора да започнува со "${r.prefix}"`:r.format==="ends_with"?`Неважечка низа: мора да завршува со "${r.suffix}"`:r.format==="includes"?`Неважечка низа: мора да вклучува "${r.includes}"`:r.format==="regex"?`Неважечка низа: мора да одгоара на патернот ${r.pattern}`:`Invalid ${o[r.format]??e.format}`}case"not_multiple_of":return`Грешен број: мора да биде делив со ${e.divisor}`;case"unrecognized_keys":return`${e.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч"}: ${v(e.keys,", ")}`;case"invalid_key":return`Грешен клуч во ${e.origin}`;case"invalid_union":return"Грешен внес";case"invalid_element":return`Грешна вредност во ${e.origin}`;default:return"Грешен внес"}}};function Zc(){return{localeError:Dc()}}const Tc=()=>{const n={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"nombor";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return e=>{switch(e.code){case"invalid_type":return`Input tidak sah: dijangka ${e.expected}, diterima ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Input tidak sah: dijangka ${I(e.values[0])}`:`Pilihan tidak sah: dijangka salah satu daripada ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Terlalu besar: dijangka ${e.origin??"nilai"} ${a.verb} ${r}${e.maximum.toString()} ${a.unit??"elemen"}`:`Terlalu besar: dijangka ${e.origin??"nilai"} adalah ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Terlalu kecil: dijangka ${e.origin} ${a.verb} ${r}${e.minimum.toString()} ${a.unit}`:`Terlalu kecil: dijangka ${e.origin} adalah ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`String tidak sah: mesti bermula dengan "${r.prefix}"`:r.format==="ends_with"?`String tidak sah: mesti berakhir dengan "${r.suffix}"`:r.format==="includes"?`String tidak sah: mesti mengandungi "${r.includes}"`:r.format==="regex"?`String tidak sah: mesti sepadan dengan corak ${r.pattern}`:`${o[r.format]??e.format} tidak sah`}case"not_multiple_of":return`Nombor tidak sah: perlu gandaan ${e.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali: ${v(e.keys,", ")}`;case"invalid_key":return`Kunci tidak sah dalam ${e.origin}`;case"invalid_union":return"Input tidak sah";case"invalid_element":return`Nilai tidak sah dalam ${e.origin}`;default:return"Input tidak sah"}}};function Ec(){return{localeError:Tc()}}const Ac=()=>{const n={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"getal";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return e=>{switch(e.code){case"invalid_type":return`Ongeldige invoer: verwacht ${e.expected}, ontving ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Ongeldige invoer: verwacht ${I(e.values[0])}`:`Ongeldige optie: verwacht één van ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Te lang: verwacht dat ${e.origin??"waarde"} ${r}${e.maximum.toString()} ${a.unit??"elementen"} bevat`:`Te lang: verwacht dat ${e.origin??"waarde"} ${r}${e.maximum.toString()} is`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Te kort: verwacht dat ${e.origin} ${r}${e.minimum.toString()} ${a.unit} bevat`:`Te kort: verwacht dat ${e.origin} ${r}${e.minimum.toString()} is`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Ongeldige tekst: moet met "${r.prefix}" beginnen`:r.format==="ends_with"?`Ongeldige tekst: moet op "${r.suffix}" eindigen`:r.format==="includes"?`Ongeldige tekst: moet "${r.includes}" bevatten`:r.format==="regex"?`Ongeldige tekst: moet overeenkomen met patroon ${r.pattern}`:`Ongeldig: ${o[r.format]??e.format}`}case"not_multiple_of":return`Ongeldig getal: moet een veelvoud van ${e.divisor} zijn`;case"unrecognized_keys":return`Onbekende key${e.keys.length>1?"s":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Ongeldige key in ${e.origin}`;case"invalid_union":return"Ongeldige invoer";case"invalid_element":return`Ongeldige waarde in ${e.origin}`;default:return"Ongeldige invoer"}}};function Lc(){return{localeError:Ac()}}const Cc=()=>{const n={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"tall";case"object":{if(Array.isArray(e))return"liste";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return e=>{switch(e.code){case"invalid_type":return`Ugyldig input: forventet ${e.expected}, fikk ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Ugyldig verdi: forventet ${I(e.values[0])}`:`Ugyldig valg: forventet en av ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`For stor(t): forventet ${e.origin??"value"} til å ha ${r}${e.maximum.toString()} ${a.unit??"elementer"}`:`For stor(t): forventet ${e.origin??"value"} til å ha ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`For lite(n): forventet ${e.origin} til å ha ${r}${e.minimum.toString()} ${a.unit}`:`For lite(n): forventet ${e.origin} til å ha ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Ugyldig streng: må starte med "${r.prefix}"`:r.format==="ends_with"?`Ugyldig streng: må ende med "${r.suffix}"`:r.format==="includes"?`Ugyldig streng: må inneholde "${r.includes}"`:r.format==="regex"?`Ugyldig streng: må matche mønsteret ${r.pattern}`:`Ugyldig ${o[r.format]??e.format}`}case"not_multiple_of":return`Ugyldig tall: må være et multiplum av ${e.divisor}`;case"unrecognized_keys":return`${e.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel"}: ${v(e.keys,", ")}`;case"invalid_key":return`Ugyldig nøkkel i ${e.origin}`;case"invalid_union":return"Ugyldig input";case"invalid_element":return`Ugyldig verdi i ${e.origin}`;default:return"Ugyldig input"}}};function Rc(){return{localeError:Cc()}}const Fc=()=>{const n={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"numara";case"object":{if(Array.isArray(e))return"saf";if(e===null)return"gayb";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return e=>{switch(e.code){case"invalid_type":return`Fâsit giren: umulan ${e.expected}, alınan ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Fâsit giren: umulan ${I(e.values[0])}`:`Fâsit tercih: mûteberler ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Fazla büyük: ${e.origin??"value"}, ${r}${e.maximum.toString()} ${a.unit??"elements"} sahip olmalıydı.`:`Fazla büyük: ${e.origin??"value"}, ${r}${e.maximum.toString()} olmalıydı.`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Fazla küçük: ${e.origin}, ${r}${e.minimum.toString()} ${a.unit} sahip olmalıydı.`:`Fazla küçük: ${e.origin}, ${r}${e.minimum.toString()} olmalıydı.`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Fâsit metin: "${r.prefix}" ile başlamalı.`:r.format==="ends_with"?`Fâsit metin: "${r.suffix}" ile bitmeli.`:r.format==="includes"?`Fâsit metin: "${r.includes}" ihtivâ etmeli.`:r.format==="regex"?`Fâsit metin: ${r.pattern} nakşına uymalı.`:`Fâsit ${o[r.format]??e.format}`}case"not_multiple_of":return`Fâsit sayı: ${e.divisor} katı olmalıydı.`;case"unrecognized_keys":return`Tanınmayan anahtar ${e.keys.length>1?"s":""}: ${v(e.keys,", ")}`;case"invalid_key":return`${e.origin} için tanınmayan anahtar var.`;case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return`${e.origin} için tanınmayan kıymet var.`;default:return"Kıymet tanınamadı."}}};function Jc(){return{localeError:Fc()}}const Mc=()=>{const n={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"عدد";case"object":{if(Array.isArray(e))return"ارې";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return e=>{switch(e.code){case"invalid_type":return`ناسم ورودي: باید ${e.expected} وای, مګر ${i(e.input)} ترلاسه شو`;case"invalid_value":return e.values.length===1?`ناسم ورودي: باید ${I(e.values[0])} وای`:`ناسم انتخاب: باید یو له ${v(e.values,"|")} څخه وای`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`ډیر لوی: ${e.origin??"ارزښت"} باید ${r}${e.maximum.toString()} ${a.unit??"عنصرونه"} ولري`:`ډیر لوی: ${e.origin??"ارزښت"} باید ${r}${e.maximum.toString()} وي`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`ډیر کوچنی: ${e.origin} باید ${r}${e.minimum.toString()} ${a.unit} ولري`:`ډیر کوچنی: ${e.origin} باید ${r}${e.minimum.toString()} وي`}case"invalid_format":{const r=e;return r.format==="starts_with"?`ناسم متن: باید د "${r.prefix}" سره پیل شي`:r.format==="ends_with"?`ناسم متن: باید د "${r.suffix}" سره پای ته ورسيږي`:r.format==="includes"?`ناسم متن: باید "${r.includes}" ولري`:r.format==="regex"?`ناسم متن: باید د ${r.pattern} سره مطابقت ولري`:`${o[r.format]??e.format} ناسم دی`}case"not_multiple_of":return`ناسم عدد: باید د ${e.divisor} مضرب وي`;case"unrecognized_keys":return`ناسم ${e.keys.length>1?"کلیډونه":"کلیډ"}: ${v(e.keys,", ")}`;case"invalid_key":return`ناسم کلیډ په ${e.origin} کې`;case"invalid_union":return"ناسمه ورودي";case"invalid_element":return`ناسم عنصر په ${e.origin} کې`;default:return"ناسمه ورودي"}}};function Vc(){return{localeError:Mc()}}const Gc=()=>{const n={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"liczba";case"object":{if(Array.isArray(e))return"tablica";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return e=>{switch(e.code){case"invalid_type":return`Nieprawidłowe dane wejściowe: oczekiwano ${e.expected}, otrzymano ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Nieprawidłowe dane wejściowe: oczekiwano ${I(e.values[0])}`:`Nieprawidłowa opcja: oczekiwano jednej z wartości ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Za duża wartość: oczekiwano, że ${e.origin??"wartość"} będzie mieć ${r}${e.maximum.toString()} ${a.unit??"elementów"}`:`Zbyt duż(y/a/e): oczekiwano, że ${e.origin??"wartość"} będzie wynosić ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Za mała wartość: oczekiwano, że ${e.origin??"wartość"} będzie mieć ${r}${e.minimum.toString()} ${a.unit??"elementów"}`:`Zbyt mał(y/a/e): oczekiwano, że ${e.origin??"wartość"} będzie wynosić ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Nieprawidłowy ciąg znaków: musi zaczynać się od "${r.prefix}"`:r.format==="ends_with"?`Nieprawidłowy ciąg znaków: musi kończyć się na "${r.suffix}"`:r.format==="includes"?`Nieprawidłowy ciąg znaków: musi zawierać "${r.includes}"`:r.format==="regex"?`Nieprawidłowy ciąg znaków: musi odpowiadać wzorcowi ${r.pattern}`:`Nieprawidłow(y/a/e) ${o[r.format]??e.format}`}case"not_multiple_of":return`Nieprawidłowa liczba: musi być wielokrotnością ${e.divisor}`;case"unrecognized_keys":return`Nierozpoznane klucze${e.keys.length>1?"s":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Nieprawidłowy klucz w ${e.origin}`;case"invalid_union":return"Nieprawidłowe dane wejściowe";case"invalid_element":return`Nieprawidłowa wartość w ${e.origin}`;default:return"Nieprawidłowe dane wejściowe"}}};function Wc(){return{localeError:Gc()}}const Bc=()=>{const n={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"número";case"object":{if(Array.isArray(e))return"array";if(e===null)return"nulo";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{switch(e.code){case"invalid_type":return`Tipo inválido: esperado ${e.expected}, recebido ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Entrada inválida: esperado ${I(e.values[0])}`:`Opção inválida: esperada uma das ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Muito grande: esperado que ${e.origin??"valor"} tivesse ${r}${e.maximum.toString()} ${a.unit??"elementos"}`:`Muito grande: esperado que ${e.origin??"valor"} fosse ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Muito pequeno: esperado que ${e.origin} tivesse ${r}${e.minimum.toString()} ${a.unit}`:`Muito pequeno: esperado que ${e.origin} fosse ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Texto inválido: deve começar com "${r.prefix}"`:r.format==="ends_with"?`Texto inválido: deve terminar com "${r.suffix}"`:r.format==="includes"?`Texto inválido: deve incluir "${r.includes}"`:r.format==="regex"?`Texto inválido: deve corresponder ao padrão ${r.pattern}`:`${o[r.format]??e.format} inválido`}case"not_multiple_of":return`Número inválido: deve ser múltiplo de ${e.divisor}`;case"unrecognized_keys":return`Chave${e.keys.length>1?"s":""} desconhecida${e.keys.length>1?"s":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Chave inválida em ${e.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inválido em ${e.origin}`;default:return"Campo inválido"}}};function Kc(){return{localeError:Bc()}}function er(n,t,i,o){const e=Math.abs(n),r=e%10,a=e%100;return a>=11&&a<=19?o:r===1?t:r>=2&&r<=4?i:o}const Xc=()=>{const n={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"число";case"object":{if(Array.isArray(e))return"массив";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return e=>{switch(e.code){case"invalid_type":return`Неверный ввод: ожидалось ${e.expected}, получено ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Неверный ввод: ожидалось ${I(e.values[0])}`:`Неверный вариант: ожидалось одно из ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);if(a){const u=Number(e.maximum),l=er(u,a.unit.one,a.unit.few,a.unit.many);return`Слишком большое значение: ожидалось, что ${e.origin??"значение"} будет иметь ${r}${e.maximum.toString()} ${l}`}return`Слишком большое значение: ожидалось, что ${e.origin??"значение"} будет ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);if(a){const u=Number(e.minimum),l=er(u,a.unit.one,a.unit.few,a.unit.many);return`Слишком маленькое значение: ожидалось, что ${e.origin} будет иметь ${r}${e.minimum.toString()} ${l}`}return`Слишком маленькое значение: ожидалось, что ${e.origin} будет ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Неверная строка: должна начинаться с "${r.prefix}"`:r.format==="ends_with"?`Неверная строка: должна заканчиваться на "${r.suffix}"`:r.format==="includes"?`Неверная строка: должна содержать "${r.includes}"`:r.format==="regex"?`Неверная строка: должна соответствовать шаблону ${r.pattern}`:`Неверный ${o[r.format]??e.format}`}case"not_multiple_of":return`Неверное число: должно быть кратным ${e.divisor}`;case"unrecognized_keys":return`Нераспознанн${e.keys.length>1?"ые":"ый"} ключ${e.keys.length>1?"и":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Неверный ключ в ${e.origin}`;case"invalid_union":return"Неверные входные данные";case"invalid_element":return`Неверное значение в ${e.origin}`;default:return"Неверные входные данные"}}};function qc(){return{localeError:Xc()}}const Yc=()=>{const n={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"število";case"object":{if(Array.isArray(e))return"tabela";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return e=>{switch(e.code){case"invalid_type":return`Neveljaven vnos: pričakovano ${e.expected}, prejeto ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Neveljaven vnos: pričakovano ${I(e.values[0])}`:`Neveljavna možnost: pričakovano eno izmed ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Preveliko: pričakovano, da bo ${e.origin??"vrednost"} imelo ${r}${e.maximum.toString()} ${a.unit??"elementov"}`:`Preveliko: pričakovano, da bo ${e.origin??"vrednost"} ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Premajhno: pričakovano, da bo ${e.origin} imelo ${r}${e.minimum.toString()} ${a.unit}`:`Premajhno: pričakovano, da bo ${e.origin} ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Neveljaven niz: mora se začeti z "${r.prefix}"`:r.format==="ends_with"?`Neveljaven niz: mora se končati z "${r.suffix}"`:r.format==="includes"?`Neveljaven niz: mora vsebovati "${r.includes}"`:r.format==="regex"?`Neveljaven niz: mora ustrezati vzorcu ${r.pattern}`:`Neveljaven ${o[r.format]??e.format}`}case"not_multiple_of":return`Neveljavno število: mora biti večkratnik ${e.divisor}`;case"unrecognized_keys":return`Neprepoznan${e.keys.length>1?"i ključi":" ključ"}: ${v(e.keys,", ")}`;case"invalid_key":return`Neveljaven ključ v ${e.origin}`;case"invalid_union":return"Neveljaven vnos";case"invalid_element":return`Neveljavna vrednost v ${e.origin}`;default:return"Neveljaven vnos"}}};function Hc(){return{localeError:Yc()}}const Qc=()=>{const n={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"antal";case"object":{if(Array.isArray(e))return"lista";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return e=>{switch(e.code){case"invalid_type":return`Ogiltig inmatning: förväntat ${e.expected}, fick ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Ogiltig inmatning: förväntat ${I(e.values[0])}`:`Ogiltigt val: förväntade en av ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`För stor(t): förväntade ${e.origin??"värdet"} att ha ${r}${e.maximum.toString()} ${a.unit??"element"}`:`För stor(t): förväntat ${e.origin??"värdet"} att ha ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`För lite(t): förväntade ${e.origin??"värdet"} att ha ${r}${e.minimum.toString()} ${a.unit}`:`För lite(t): förväntade ${e.origin??"värdet"} att ha ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Ogiltig sträng: måste börja med "${r.prefix}"`:r.format==="ends_with"?`Ogiltig sträng: måste sluta med "${r.suffix}"`:r.format==="includes"?`Ogiltig sträng: måste innehålla "${r.includes}"`:r.format==="regex"?`Ogiltig sträng: måste matcha mönstret "${r.pattern}"`:`Ogiltig(t) ${o[r.format]??e.format}`}case"not_multiple_of":return`Ogiltigt tal: måste vara en multipel av ${e.divisor}`;case"unrecognized_keys":return`${e.keys.length>1?"Okända nycklar":"Okänd nyckel"}: ${v(e.keys,", ")}`;case"invalid_key":return`Ogiltig nyckel i ${e.origin??"värdet"}`;case"invalid_union":return"Ogiltig input";case"invalid_element":return`Ogiltigt värde i ${e.origin??"värdet"}`;default:return"Ogiltig input"}}};function el(){return{localeError:Qc()}}const nl=()=>{const n={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"எண் அல்லாதது":"எண்";case"object":{if(Array.isArray(e))return"அணி";if(e===null)return"வெறுமை";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return e=>{switch(e.code){case"invalid_type":return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${e.expected}, பெறப்பட்டது ${i(e.input)}`;case"invalid_value":return e.values.length===1?`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${I(e.values[0])}`:`தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ${v(e.values,"|")} இல் ஒன்று`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${e.origin??"மதிப்பு"} ${r}${e.maximum.toString()} ${a.unit??"உறுப்புகள்"} ஆக இருக்க வேண்டும்`:`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${e.origin??"மதிப்பு"} ${r}${e.maximum.toString()} ஆக இருக்க வேண்டும்`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${e.origin} ${r}${e.minimum.toString()} ${a.unit} ஆக இருக்க வேண்டும்`:`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${e.origin} ${r}${e.minimum.toString()} ஆக இருக்க வேண்டும்`}case"invalid_format":{const r=e;return r.format==="starts_with"?`தவறான சரம்: "${r.prefix}" இல் தொடங்க வேண்டும்`:r.format==="ends_with"?`தவறான சரம்: "${r.suffix}" இல் முடிவடைய வேண்டும்`:r.format==="includes"?`தவறான சரம்: "${r.includes}" ஐ உள்ளடக்க வேண்டும்`:r.format==="regex"?`தவறான சரம்: ${r.pattern} முறைபாட்டுடன் பொருந்த வேண்டும்`:`தவறான ${o[r.format]??e.format}`}case"not_multiple_of":return`தவறான எண்: ${e.divisor} இன் பலமாக இருக்க வேண்டும்`;case"unrecognized_keys":return`அடையாளம் தெரியாத விசை${e.keys.length>1?"கள்":""}: ${v(e.keys,", ")}`;case"invalid_key":return`${e.origin} இல் தவறான விசை`;case"invalid_union":return"தவறான உள்ளீடு";case"invalid_element":return`${e.origin} இல் தவறான மதிப்பு`;default:return"தவறான உள்ளீடு"}}};function tl(){return{localeError:nl()}}const rl=()=>{const n={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":{if(Array.isArray(e))return"อาร์เรย์ (Array)";if(e===null)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return e=>{switch(e.code){case"invalid_type":return`ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ${e.expected} แต่ได้รับ ${i(e.input)}`;case"invalid_value":return e.values.length===1?`ค่าไม่ถูกต้อง: ควรเป็น ${I(e.values[0])}`:`ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"ไม่เกิน":"น้อยกว่า",a=t(e.origin);return a?`เกินกำหนด: ${e.origin??"ค่า"} ควรมี${r} ${e.maximum.toString()} ${a.unit??"รายการ"}`:`เกินกำหนด: ${e.origin??"ค่า"} ควรมี${r} ${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?"อย่างน้อย":"มากกว่า",a=t(e.origin);return a?`น้อยกว่ากำหนด: ${e.origin} ควรมี${r} ${e.minimum.toString()} ${a.unit}`:`น้อยกว่ากำหนด: ${e.origin} ควรมี${r} ${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "${r.prefix}"`:r.format==="ends_with"?`รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "${r.suffix}"`:r.format==="includes"?`รูปแบบไม่ถูกต้อง: ข้อความต้องมี "${r.includes}" อยู่ในข้อความ`:r.format==="regex"?`รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ${r.pattern}`:`รูปแบบไม่ถูกต้อง: ${o[r.format]??e.format}`}case"not_multiple_of":return`ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ${e.divisor} ได้ลงตัว`;case"unrecognized_keys":return`พบคีย์ที่ไม่รู้จัก: ${v(e.keys,", ")}`;case"invalid_key":return`คีย์ไม่ถูกต้องใน ${e.origin}`;case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return`ข้อมูลไม่ถูกต้องใน ${e.origin}`;default:return"ข้อมูลไม่ถูกต้อง"}}};function il(){return{localeError:rl()}}const ol=n=>{const t=typeof n;switch(t){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return t},al=()=>{const n={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}};function t(o){return n[o]??null}const i={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return o=>{switch(o.code){case"invalid_type":return`Geçersiz değer: beklenen ${o.expected}, alınan ${ol(o.input)}`;case"invalid_value":return o.values.length===1?`Geçersiz değer: beklenen ${I(o.values[0])}`:`Geçersiz seçenek: aşağıdakilerden biri olmalı: ${v(o.values,"|")}`;case"too_big":{const e=o.inclusive?"<=":"<",r=t(o.origin);return r?`Çok büyük: beklenen ${o.origin??"değer"} ${e}${o.maximum.toString()} ${r.unit??"öğe"}`:`Çok büyük: beklenen ${o.origin??"değer"} ${e}${o.maximum.toString()}`}case"too_small":{const e=o.inclusive?">=":">",r=t(o.origin);return r?`Çok küçük: beklenen ${o.origin} ${e}${o.minimum.toString()} ${r.unit}`:`Çok küçük: beklenen ${o.origin} ${e}${o.minimum.toString()}`}case"invalid_format":{const e=o;return e.format==="starts_with"?`Geçersiz metin: "${e.prefix}" ile başlamalı`:e.format==="ends_with"?`Geçersiz metin: "${e.suffix}" ile bitmeli`:e.format==="includes"?`Geçersiz metin: "${e.includes}" içermeli`:e.format==="regex"?`Geçersiz metin: ${e.pattern} desenine uymalı`:`Geçersiz ${i[e.format]??o.format}`}case"not_multiple_of":return`Geçersiz sayı: ${o.divisor} ile tam bölünebilmeli`;case"unrecognized_keys":return`Tanınmayan anahtar${o.keys.length>1?"lar":""}: ${v(o.keys,", ")}`;case"invalid_key":return`${o.origin} içinde geçersiz anahtar`;case"invalid_union":return"Geçersiz değer";case"invalid_element":return`${o.origin} içinde geçersiz değer`;default:return"Geçersiz değer"}}};function ul(){return{localeError:al()}}const cl=()=>{const n={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"число";case"object":{if(Array.isArray(e))return"масив";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return e=>{switch(e.code){case"invalid_type":return`Неправильні вхідні дані: очікується ${e.expected}, отримано ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Неправильні вхідні дані: очікується ${I(e.values[0])}`:`Неправильна опція: очікується одне з ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Занадто велике: очікується, що ${e.origin??"значення"} ${a.verb} ${r}${e.maximum.toString()} ${a.unit??"елементів"}`:`Занадто велике: очікується, що ${e.origin??"значення"} буде ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Занадто мале: очікується, що ${e.origin} ${a.verb} ${r}${e.minimum.toString()} ${a.unit}`:`Занадто мале: очікується, що ${e.origin} буде ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Неправильний рядок: повинен починатися з "${r.prefix}"`:r.format==="ends_with"?`Неправильний рядок: повинен закінчуватися на "${r.suffix}"`:r.format==="includes"?`Неправильний рядок: повинен містити "${r.includes}"`:r.format==="regex"?`Неправильний рядок: повинен відповідати шаблону ${r.pattern}`:`Неправильний ${o[r.format]??e.format}`}case"not_multiple_of":return`Неправильне число: повинно бути кратним ${e.divisor}`;case"unrecognized_keys":return`Нерозпізнаний ключ${e.keys.length>1?"і":""}: ${v(e.keys,", ")}`;case"invalid_key":return`Неправильний ключ у ${e.origin}`;case"invalid_union":return"Неправильні вхідні дані";case"invalid_element":return`Неправильне значення у ${e.origin}`;default:return"Неправильні вхідні дані"}}};function ll(){return{localeError:cl()}}const sl=()=>{const n={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"نمبر";case"object":{if(Array.isArray(e))return"آرے";if(e===null)return"نل";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return e=>{switch(e.code){case"invalid_type":return`غلط ان پٹ: ${e.expected} متوقع تھا، ${i(e.input)} موصول ہوا`;case"invalid_value":return e.values.length===1?`غلط ان پٹ: ${I(e.values[0])} متوقع تھا`:`غلط آپشن: ${v(e.values,"|")} میں سے ایک متوقع تھا`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`بہت بڑا: ${e.origin??"ویلیو"} کے ${r}${e.maximum.toString()} ${a.unit??"عناصر"} ہونے متوقع تھے`:`بہت بڑا: ${e.origin??"ویلیو"} کا ${r}${e.maximum.toString()} ہونا متوقع تھا`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`بہت چھوٹا: ${e.origin} کے ${r}${e.minimum.toString()} ${a.unit} ہونے متوقع تھے`:`بہت چھوٹا: ${e.origin} کا ${r}${e.minimum.toString()} ہونا متوقع تھا`}case"invalid_format":{const r=e;return r.format==="starts_with"?`غلط سٹرنگ: "${r.prefix}" سے شروع ہونا چاہیے`:r.format==="ends_with"?`غلط سٹرنگ: "${r.suffix}" پر ختم ہونا چاہیے`:r.format==="includes"?`غلط سٹرنگ: "${r.includes}" شامل ہونا چاہیے`:r.format==="regex"?`غلط سٹرنگ: پیٹرن ${r.pattern} سے میچ ہونا چاہیے`:`غلط ${o[r.format]??e.format}`}case"not_multiple_of":return`غلط نمبر: ${e.divisor} کا مضاعف ہونا چاہیے`;case"unrecognized_keys":return`غیر تسلیم شدہ کی${e.keys.length>1?"ز":""}: ${v(e.keys,"، ")}`;case"invalid_key":return`${e.origin} میں غلط کی`;case"invalid_union":return"غلط ان پٹ";case"invalid_element":return`${e.origin} میں غلط ویلیو`;default:return"غلط ان پٹ"}}};function dl(){return{localeError:sl()}}const ml=()=>{const n={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"số";case"object":{if(Array.isArray(e))return"mảng";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return e=>{switch(e.code){case"invalid_type":return`Đầu vào không hợp lệ: mong đợi ${e.expected}, nhận được ${i(e.input)}`;case"invalid_value":return e.values.length===1?`Đầu vào không hợp lệ: mong đợi ${I(e.values[0])}`:`Tùy chọn không hợp lệ: mong đợi một trong các giá trị ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`Quá lớn: mong đợi ${e.origin??"giá trị"} ${a.verb} ${r}${e.maximum.toString()} ${a.unit??"phần tử"}`:`Quá lớn: mong đợi ${e.origin??"giá trị"} ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`Quá nhỏ: mong đợi ${e.origin} ${a.verb} ${r}${e.minimum.toString()} ${a.unit}`:`Quá nhỏ: mong đợi ${e.origin} ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`Chuỗi không hợp lệ: phải bắt đầu bằng "${r.prefix}"`:r.format==="ends_with"?`Chuỗi không hợp lệ: phải kết thúc bằng "${r.suffix}"`:r.format==="includes"?`Chuỗi không hợp lệ: phải bao gồm "${r.includes}"`:r.format==="regex"?`Chuỗi không hợp lệ: phải khớp với mẫu ${r.pattern}`:`${o[r.format]??e.format} không hợp lệ`}case"not_multiple_of":return`Số không hợp lệ: phải là bội số của ${e.divisor}`;case"unrecognized_keys":return`Khóa không được nhận dạng: ${v(e.keys,", ")}`;case"invalid_key":return`Khóa không hợp lệ trong ${e.origin}`;case"invalid_union":return"Đầu vào không hợp lệ";case"invalid_element":return`Giá trị không hợp lệ trong ${e.origin}`;default:return"Đầu vào không hợp lệ"}}};function fl(){return{localeError:ml()}}const vl=()=>{const n={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"非数字(NaN)":"数字";case"object":{if(Array.isArray(e))return"数组";if(e===null)return"空值(null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return e=>{switch(e.code){case"invalid_type":return`无效输入：期望 ${e.expected}，实际接收 ${i(e.input)}`;case"invalid_value":return e.values.length===1?`无效输入：期望 ${I(e.values[0])}`:`无效选项：期望以下之一 ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`数值过大：期望 ${e.origin??"值"} ${r}${e.maximum.toString()} ${a.unit??"个元素"}`:`数值过大：期望 ${e.origin??"值"} ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`数值过小：期望 ${e.origin} ${r}${e.minimum.toString()} ${a.unit}`:`数值过小：期望 ${e.origin} ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`无效字符串：必须以 "${r.prefix}" 开头`:r.format==="ends_with"?`无效字符串：必须以 "${r.suffix}" 结尾`:r.format==="includes"?`无效字符串：必须包含 "${r.includes}"`:r.format==="regex"?`无效字符串：必须满足正则表达式 ${r.pattern}`:`无效${o[r.format]??e.format}`}case"not_multiple_of":return`无效数字：必须是 ${e.divisor} 的倍数`;case"unrecognized_keys":return`出现未知的键(key): ${v(e.keys,", ")}`;case"invalid_key":return`${e.origin} 中的键(key)无效`;case"invalid_union":return"无效输入";case"invalid_element":return`${e.origin} 中包含无效值(value)`;default:return"无效输入"}}};function pl(){return{localeError:vl()}}const gl=()=>{const n={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}};function t(e){return n[e]??null}const i=e=>{const r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return r},o={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return e=>{switch(e.code){case"invalid_type":return`無效的輸入值：預期為 ${e.expected}，但收到 ${i(e.input)}`;case"invalid_value":return e.values.length===1?`無效的輸入值：預期為 ${I(e.values[0])}`:`無效的選項：預期為以下其中之一 ${v(e.values,"|")}`;case"too_big":{const r=e.inclusive?"<=":"<",a=t(e.origin);return a?`數值過大：預期 ${e.origin??"值"} 應為 ${r}${e.maximum.toString()} ${a.unit??"個元素"}`:`數值過大：預期 ${e.origin??"值"} 應為 ${r}${e.maximum.toString()}`}case"too_small":{const r=e.inclusive?">=":">",a=t(e.origin);return a?`數值過小：預期 ${e.origin} 應為 ${r}${e.minimum.toString()} ${a.unit}`:`數值過小：預期 ${e.origin} 應為 ${r}${e.minimum.toString()}`}case"invalid_format":{const r=e;return r.format==="starts_with"?`無效的字串：必須以 "${r.prefix}" 開頭`:r.format==="ends_with"?`無效的字串：必須以 "${r.suffix}" 結尾`:r.format==="includes"?`無效的字串：必須包含 "${r.includes}"`:r.format==="regex"?`無效的字串：必須符合格式 ${r.pattern}`:`無效的 ${o[r.format]??e.format}`}case"not_multiple_of":return`無效的數字：必須為 ${e.divisor} 的倍數`;case"unrecognized_keys":return`無法識別的鍵值${e.keys.length>1?"們":""}：${v(e.keys,"、")}`;case"invalid_key":return`${e.origin} 中有無效的鍵值`;case"invalid_union":return"無效的輸入值";case"invalid_element":return`${e.origin} 中有無效的值`;default:return"無效的輸入值"}}};function hl(){return{localeError:gl()}}const xo=Object.freeze(Object.defineProperty({__proto__:null,ar:Bu,az:Xu,be:Yu,ca:Qu,cs:nc,de:rc,en:jo,eo:cc,es:sc,fa:mc,fi:vc,fr:gc,frCA:$c,he:bc,hu:kc,id:wc,it:Sc,ja:xc,kh:Oc,ko:Pc,mk:Zc,ms:Ec,nl:Lc,no:Rc,ota:Jc,pl:Wc,ps:Vc,pt:Kc,ru:qc,sl:Hc,sv:el,ta:tl,th:il,tr:ul,ua:ll,ur:dl,vi:fl,zhCN:pl,zhTW:hl},Symbol.toStringTag,{value:"Module"})),No=Symbol("ZodOutput"),Oo=Symbol("ZodInput");class bn{constructor(){this._map=new Map,this._idmap=new Map}add(t,...i){const o=i[0];if(this._map.set(t,o),o&&typeof o=="object"&&"id"in o){if(this._idmap.has(o.id))throw new Error(`ID ${o.id} already exists in the registry`);this._idmap.set(o.id,t)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(t){const i=this._map.get(t);return i&&typeof i=="object"&&"id"in i&&this._idmap.delete(i.id),this._map.delete(t),this}get(t){const i=t._zod.parent;if(i){const o={...this.get(i)??{}};return delete o.id,{...o,...this._map.get(t)}}return this._map.get(t)}has(t){return this._map.has(t)}}function yn(){return new bn}const V=yn();function Uo(n,t){return new n({type:"string",...m(t)})}function Po(n,t){return new n({type:"string",coerce:!0,...m(t)})}function kn(n,t){return new n({type:"string",format:"email",check:"string_format",abort:!1,...m(t)})}function be(n,t){return new n({type:"string",format:"guid",check:"string_format",abort:!1,...m(t)})}function In(n,t){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,...m(t)})}function wn(n,t){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...m(t)})}function zn(n,t){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...m(t)})}function Sn(n,t){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...m(t)})}function jn(n,t){return new n({type:"string",format:"url",check:"string_format",abort:!1,...m(t)})}function xn(n,t){return new n({type:"string",format:"emoji",check:"string_format",abort:!1,...m(t)})}function Nn(n,t){return new n({type:"string",format:"nanoid",check:"string_format",abort:!1,...m(t)})}function On(n,t){return new n({type:"string",format:"cuid",check:"string_format",abort:!1,...m(t)})}function Un(n,t){return new n({type:"string",format:"cuid2",check:"string_format",abort:!1,...m(t)})}function Pn(n,t){return new n({type:"string",format:"ulid",check:"string_format",abort:!1,...m(t)})}function Dn(n,t){return new n({type:"string",format:"xid",check:"string_format",abort:!1,...m(t)})}function Zn(n,t){return new n({type:"string",format:"ksuid",check:"string_format",abort:!1,...m(t)})}function Tn(n,t){return new n({type:"string",format:"ipv4",check:"string_format",abort:!1,...m(t)})}function En(n,t){return new n({type:"string",format:"ipv6",check:"string_format",abort:!1,...m(t)})}function An(n,t){return new n({type:"string",format:"cidrv4",check:"string_format",abort:!1,...m(t)})}function Ln(n,t){return new n({type:"string",format:"cidrv6",check:"string_format",abort:!1,...m(t)})}function Cn(n,t){return new n({type:"string",format:"base64",check:"string_format",abort:!1,...m(t)})}function Rn(n,t){return new n({type:"string",format:"base64url",check:"string_format",abort:!1,...m(t)})}function Fn(n,t){return new n({type:"string",format:"e164",check:"string_format",abort:!1,...m(t)})}function Jn(n,t){return new n({type:"string",format:"jwt",check:"string_format",abort:!1,...m(t)})}const Do={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function Zo(n,t){return new n({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...m(t)})}function To(n,t){return new n({type:"string",format:"date",check:"string_format",...m(t)})}function Eo(n,t){return new n({type:"string",format:"time",check:"string_format",precision:null,...m(t)})}function Ao(n,t){return new n({type:"string",format:"duration",check:"string_format",...m(t)})}function Lo(n,t){return new n({type:"number",checks:[],...m(t)})}function Co(n,t){return new n({type:"number",coerce:!0,checks:[],...m(t)})}function Ro(n,t){return new n({type:"number",check:"number_format",abort:!1,format:"safeint",...m(t)})}function Fo(n,t){return new n({type:"number",check:"number_format",abort:!1,format:"float32",...m(t)})}function Jo(n,t){return new n({type:"number",check:"number_format",abort:!1,format:"float64",...m(t)})}function Mo(n,t){return new n({type:"number",check:"number_format",abort:!1,format:"int32",...m(t)})}function Vo(n,t){return new n({type:"number",check:"number_format",abort:!1,format:"uint32",...m(t)})}function Go(n,t){return new n({type:"boolean",...m(t)})}function Wo(n,t){return new n({type:"boolean",coerce:!0,...m(t)})}function Bo(n,t){return new n({type:"bigint",...m(t)})}function Ko(n,t){return new n({type:"bigint",coerce:!0,...m(t)})}function Xo(n,t){return new n({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...m(t)})}function qo(n,t){return new n({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...m(t)})}function Yo(n,t){return new n({type:"symbol",...m(t)})}function Ho(n,t){return new n({type:"undefined",...m(t)})}function Qo(n,t){return new n({type:"null",...m(t)})}function ea(n){return new n({type:"any"})}function ye(n){return new n({type:"unknown"})}function na(n,t){return new n({type:"never",...m(t)})}function ta(n,t){return new n({type:"void",...m(t)})}function ra(n,t){return new n({type:"date",...m(t)})}function ia(n,t){return new n({type:"date",coerce:!0,...m(t)})}function oa(n,t){return new n({type:"nan",...m(t)})}function G(n,t){return new sn({check:"less_than",...m(t),value:n,inclusive:!1})}function C(n,t){return new sn({check:"less_than",...m(t),value:n,inclusive:!0})}function W(n,t){return new dn({check:"greater_than",...m(t),value:n,inclusive:!1})}function A(n,t){return new dn({check:"greater_than",...m(t),value:n,inclusive:!0})}function aa(n){return W(0,n)}function ua(n){return G(0,n)}function ca(n){return C(0,n)}function la(n){return A(0,n)}function ce(n,t){return new ri({check:"multiple_of",...m(t),value:n})}function Pe(n,t){return new ai({check:"max_size",...m(t),maximum:n})}function le(n,t){return new ui({check:"min_size",...m(t),minimum:n})}function Mn(n,t){return new ci({check:"size_equals",...m(t),size:n})}function De(n,t){return new li({check:"max_length",...m(t),maximum:n})}function te(n,t){return new si({check:"min_length",...m(t),minimum:n})}function Ze(n,t){return new di({check:"length_equals",...m(t),length:n})}function Vn(n,t){return new mi({check:"string_format",format:"regex",...m(t),pattern:n})}function Gn(n){return new fi({check:"string_format",format:"lowercase",...m(n)})}function Wn(n){return new vi({check:"string_format",format:"uppercase",...m(n)})}function Bn(n,t){return new pi({check:"string_format",format:"includes",...m(t),includes:n})}function Kn(n,t){return new gi({check:"string_format",format:"starts_with",...m(t),prefix:n})}function Xn(n,t){return new hi({check:"string_format",format:"ends_with",...m(t),suffix:n})}function sa(n,t,i){return new $i({check:"property",property:n,schema:t,...m(i)})}function qn(n,t){return new _i({check:"mime_type",mime:n,...m(t)})}function X(n){return new bi({check:"overwrite",tx:n})}function Yn(n){return X(t=>t.normalize(n))}function Hn(){return X(n=>n.trim())}function Qn(){return X(n=>n.toLowerCase())}function et(){return X(n=>n.toUpperCase())}function nt(n,t,i){return new n({type:"array",element:t,...m(i)})}function $l(n,t,i){return new n({type:"union",options:t,...m(i)})}function _l(n,t,i,o){return new n({type:"union",options:i,discriminator:t,...m(o)})}function bl(n,t,i){return new n({type:"intersection",left:t,right:i})}function da(n,t,i,o){const e=i instanceof k,r=e?o:i,a=e?i:null;return new n({type:"tuple",items:t,rest:a,...m(r)})}function yl(n,t,i,o){return new n({type:"record",keyType:t,valueType:i,...m(o)})}function kl(n,t,i,o){return new n({type:"map",keyType:t,valueType:i,...m(o)})}function Il(n,t,i){return new n({type:"set",valueType:t,...m(i)})}function wl(n,t,i){const o=Array.isArray(t)?Object.fromEntries(t.map(e=>[e,e])):t;return new n({type:"enum",entries:o,...m(i)})}function zl(n,t,i){return new n({type:"enum",entries:t,...m(i)})}function Sl(n,t,i){return new n({type:"literal",values:Array.isArray(t)?t:[t],...m(i)})}function ma(n,t){return new n({type:"file",...m(t)})}function jl(n,t){return new n({type:"transform",transform:t})}function xl(n,t){return new n({type:"optional",innerType:t})}function Nl(n,t){return new n({type:"nullable",innerType:t})}function Ol(n,t,i){return new n({type:"default",innerType:t,get defaultValue(){return typeof i=="function"?i():i}})}function Ul(n,t,i){return new n({type:"nonoptional",innerType:t,...m(i)})}function Pl(n,t){return new n({type:"success",innerType:t})}function Dl(n,t,i){return new n({type:"catch",innerType:t,catchValue:typeof i=="function"?i:()=>i})}function Zl(n,t,i){return new n({type:"pipe",in:t,out:i})}function Tl(n,t){return new n({type:"readonly",innerType:t})}function El(n,t,i){return new n({type:"template_literal",parts:t,...m(i)})}function Al(n,t){return new n({type:"lazy",getter:t})}function Ll(n,t){return new n({type:"promise",innerType:t})}function fa(n,t,i){const o=m(i);return o.abort??(o.abort=!0),new n({type:"custom",check:"custom",fn:t,...o})}function va(n,t,i){return new n({type:"custom",check:"custom",fn:t,...m(i)})}function pa(n,t){const i=m(t);let o=i.truthy??["true","1","yes","on","y","enabled"],e=i.falsy??["false","0","no","off","n","disabled"];i.case!=="sensitive"&&(o=o.map(h=>typeof h=="string"?h.toLowerCase():h),e=e.map(h=>typeof h=="string"?h.toLowerCase():h));const r=new Set(o),a=new Set(e),u=n.Pipe??_n,l=n.Boolean??vn,s=n.String??fe,g=n.Transform??$n,_=new g({type:"transform",transform:(h,D)=>{let $=h;return i.case!=="sensitive"&&($=$.toLowerCase()),r.has($)?!0:a.has($)?!1:(D.issues.push({code:"invalid_value",expected:"stringbool",values:[...r,...a],input:D.value,inst:_}),{})},error:i.error}),y=new u({type:"pipe",in:new s({type:"string",error:i.error}),out:_,error:i.error});return new u({type:"pipe",in:y,out:new l({type:"boolean",error:i.error}),error:i.error})}function ga(n,t,i,o={}){const e=m(o),r={...m(o),check:"string_format",type:"string",format:t,fn:typeof i=="function"?i:u=>i.test(u),...e};return i instanceof RegExp&&(r.pattern=i),new n(r)}class ha{constructor(t){this._def=t,this.def=t}implement(t){if(typeof t!="function")throw new Error("implement() must be called with a function");const i=(...o)=>{const e=this._def.input?Be(this._def.input,o,void 0,{callee:i}):o;if(!Array.isArray(e))throw new Error("Invalid arguments schema: not an array or tuple schema.");const r=t(...e);return this._def.output?Be(this._def.output,r,void 0,{callee:i}):r};return i}implementAsync(t){if(typeof t!="function")throw new Error("implement() must be called with a function");const i=async(...o)=>{const e=this._def.input?await Ke(this._def.input,o,void 0,{callee:i}):o;if(!Array.isArray(e))throw new Error("Invalid arguments schema: not an array or tuple schema.");const r=await t(...e);return this._def.output?Ke(this._def.output,r,void 0,{callee:i}):r};return i}input(...t){const i=this.constructor;return Array.isArray(t[0])?new i({type:"function",input:new Ue({type:"tuple",items:t[0],rest:t[1]}),output:this._def.output}):new i({type:"function",input:t[0],output:this._def.output})}output(t){const i=this.constructor;return new i({type:"function",input:this._def.input,output:t})}}function $a(n){return new ha({type:"function",input:Array.isArray(n==null?void 0:n.input)?da(Ue,n==null?void 0:n.input):(n==null?void 0:n.input)??nt(gn,ye(_e)),output:(n==null?void 0:n.output)??ye(_e)})}class qe{constructor(t){this.counter=0,this.metadataRegistry=(t==null?void 0:t.metadata)??V,this.target=(t==null?void 0:t.target)??"draft-2020-12",this.unrepresentable=(t==null?void 0:t.unrepresentable)??"throw",this.override=(t==null?void 0:t.override)??(()=>{}),this.io=(t==null?void 0:t.io)??"output",this.seen=new Map}process(t,i={path:[],schemaPath:[]}){var _,y,w;var o;const e=t._zod.def,r={guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""},a=this.seen.get(t);if(a)return a.count++,i.schemaPath.includes(t)&&(a.cycle=i.path),a.schema;const u={schema:{},count:1,cycle:void 0,path:i.path};this.seen.set(t,u);const l=(y=(_=t._zod).toJSONSchema)==null?void 0:y.call(_);if(l)u.schema=l;else{const h={...i,schemaPath:[...i.schemaPath,t],path:i.path},D=t._zod.parent;if(D)u.ref=D,this.process(D,h),this.seen.get(D).isParent=!0;else{const $=u.schema;switch(e.type){case"string":{const d=$;d.type="string";const{minimum:p,maximum:f,format:b,patterns:z,contentEncoding:N}=t._zod.bag;if(typeof p=="number"&&(d.minLength=p),typeof f=="number"&&(d.maxLength=f),b&&(d.format=r[b]??b,d.format===""&&delete d.format),N&&(d.contentEncoding=N),z&&z.size>0){const T=[...z];T.length===1?d.pattern=T[0].source:T.length>1&&(u.schema.allOf=[...T.map(M=>({...this.target==="draft-7"?{type:"string"}:{},pattern:M.source}))])}break}case"number":{const d=$,{minimum:p,maximum:f,format:b,multipleOf:z,exclusiveMaximum:N,exclusiveMinimum:T}=t._zod.bag;typeof b=="string"&&b.includes("int")?d.type="integer":d.type="number",typeof T=="number"&&(d.exclusiveMinimum=T),typeof p=="number"&&(d.minimum=p,typeof T=="number"&&(T>=p?delete d.minimum:delete d.exclusiveMinimum)),typeof N=="number"&&(d.exclusiveMaximum=N),typeof f=="number"&&(d.maximum=f,typeof N=="number"&&(N<=f?delete d.maximum:delete d.exclusiveMaximum)),typeof z=="number"&&(d.multipleOf=z);break}case"boolean":{const d=$;d.type="boolean";break}case"bigint":{if(this.unrepresentable==="throw")throw new Error("BigInt cannot be represented in JSON Schema");break}case"symbol":{if(this.unrepresentable==="throw")throw new Error("Symbols cannot be represented in JSON Schema");break}case"null":{$.type="null";break}case"any":break;case"unknown":break;case"undefined":{if(this.unrepresentable==="throw")throw new Error("Undefined cannot be represented in JSON Schema");break}case"void":{if(this.unrepresentable==="throw")throw new Error("Void cannot be represented in JSON Schema");break}case"never":{$.not={};break}case"date":{if(this.unrepresentable==="throw")throw new Error("Date cannot be represented in JSON Schema");break}case"array":{const d=$,{minimum:p,maximum:f}=t._zod.bag;typeof p=="number"&&(d.minItems=p),typeof f=="number"&&(d.maxItems=f),d.type="array",d.items=this.process(e.element,{...h,path:[...h.path,"items"]});break}case"object":{const d=$;d.type="object",d.properties={};const p=e.shape;for(const z in p)d.properties[z]=this.process(p[z],{...h,path:[...h.path,"properties",z]});const f=new Set(Object.keys(p)),b=new Set([...f].filter(z=>{const N=e.shape[z]._zod;return this.io==="input"?N.optin===void 0:N.optout===void 0}));b.size>0&&(d.required=Array.from(b)),((w=e.catchall)==null?void 0:w._zod.def.type)==="never"?d.additionalProperties=!1:e.catchall?e.catchall&&(d.additionalProperties=this.process(e.catchall,{...h,path:[...h.path,"additionalProperties"]})):this.io==="output"&&(d.additionalProperties=!1);break}case"union":{const d=$;d.anyOf=e.options.map((p,f)=>this.process(p,{...h,path:[...h.path,"anyOf",f]}));break}case"intersection":{const d=$,p=this.process(e.left,{...h,path:[...h.path,"allOf",0]}),f=this.process(e.right,{...h,path:[...h.path,"allOf",1]}),b=N=>"allOf"in N&&Object.keys(N).length===1,z=[...b(p)?p.allOf:[p],...b(f)?f.allOf:[f]];d.allOf=z;break}case"tuple":{const d=$;d.type="array";const p=e.items.map((z,N)=>this.process(z,{...h,path:[...h.path,"prefixItems",N]}));if(this.target==="draft-2020-12"?d.prefixItems=p:d.items=p,e.rest){const z=this.process(e.rest,{...h,path:[...h.path,"items"]});this.target==="draft-2020-12"?d.items=z:d.additionalItems=z}e.rest&&(d.items=this.process(e.rest,{...h,path:[...h.path,"items"]}));const{minimum:f,maximum:b}=t._zod.bag;typeof f=="number"&&(d.minItems=f),typeof b=="number"&&(d.maxItems=b);break}case"record":{const d=$;d.type="object",d.propertyNames=this.process(e.keyType,{...h,path:[...h.path,"propertyNames"]}),d.additionalProperties=this.process(e.valueType,{...h,path:[...h.path,"additionalProperties"]});break}case"map":{if(this.unrepresentable==="throw")throw new Error("Map cannot be represented in JSON Schema");break}case"set":{if(this.unrepresentable==="throw")throw new Error("Set cannot be represented in JSON Schema");break}case"enum":{const d=$,p=Qe(e.entries);p.every(f=>typeof f=="number")&&(d.type="number"),p.every(f=>typeof f=="string")&&(d.type="string"),d.enum=p;break}case"literal":{const d=$,p=[];for(const f of e.values)if(f===void 0){if(this.unrepresentable==="throw")throw new Error("Literal `undefined` cannot be represented in JSON Schema")}else if(typeof f=="bigint"){if(this.unrepresentable==="throw")throw new Error("BigInt literals cannot be represented in JSON Schema");p.push(Number(f))}else p.push(f);if(p.length!==0)if(p.length===1){const f=p[0];d.type=f===null?"null":typeof f,d.const=f}else p.every(f=>typeof f=="number")&&(d.type="number"),p.every(f=>typeof f=="string")&&(d.type="string"),p.every(f=>typeof f=="boolean")&&(d.type="string"),p.every(f=>f===null)&&(d.type="null"),d.enum=p;break}case"file":{const d=$,p={type:"string",format:"binary",contentEncoding:"binary"},{minimum:f,maximum:b,mime:z}=t._zod.bag;f!==void 0&&(p.minLength=f),b!==void 0&&(p.maxLength=b),z?z.length===1?(p.contentMediaType=z[0],Object.assign(d,p)):d.anyOf=z.map(N=>({...p,contentMediaType:N})):Object.assign(d,p);break}case"transform":{if(this.unrepresentable==="throw")throw new Error("Transforms cannot be represented in JSON Schema");break}case"nullable":{const d=this.process(e.innerType,h);$.anyOf=[d,{type:"null"}];break}case"nonoptional":{this.process(e.innerType,h),u.ref=e.innerType;break}case"success":{const d=$;d.type="boolean";break}case"default":{this.process(e.innerType,h),u.ref=e.innerType,$.default=JSON.parse(JSON.stringify(e.defaultValue));break}case"prefault":{this.process(e.innerType,h),u.ref=e.innerType,this.io==="input"&&($._prefault=JSON.parse(JSON.stringify(e.defaultValue)));break}case"catch":{this.process(e.innerType,h),u.ref=e.innerType;let d;try{d=e.catchValue(void 0)}catch{throw new Error("Dynamic catch values are not supported in JSON Schema")}$.default=d;break}case"nan":{if(this.unrepresentable==="throw")throw new Error("NaN cannot be represented in JSON Schema");break}case"template_literal":{const d=$,p=t._zod.pattern;if(!p)throw new Error("Pattern not found in template literal");d.type="string",d.pattern=p.source;break}case"pipe":{const d=this.io==="input"?e.in._zod.def.type==="transform"?e.out:e.in:e.out;this.process(d,h),u.ref=d;break}case"readonly":{this.process(e.innerType,h),u.ref=e.innerType,$.readOnly=!0;break}case"promise":{this.process(e.innerType,h),u.ref=e.innerType;break}case"optional":{this.process(e.innerType,h),u.ref=e.innerType;break}case"lazy":{const d=t._zod.innerType;this.process(d,h),u.ref=d;break}case"custom":{if(this.unrepresentable==="throw")throw new Error("Custom types cannot be represented in JSON Schema");break}}}}const s=this.metadataRegistry.get(t);return s&&Object.assign(u.schema,s),this.io==="input"&&Z(t)&&(delete u.schema.examples,delete u.schema.default),this.io==="input"&&u.schema._prefault&&((o=u.schema).default??(o.default=u.schema._prefault)),delete u.schema._prefault,this.seen.get(t).schema}emit(t,i){var g,_,y,w,h,D;const o={cycles:(i==null?void 0:i.cycles)??"ref",reused:(i==null?void 0:i.reused)??"inline",external:(i==null?void 0:i.external)??void 0},e=this.seen.get(t);if(!e)throw new Error("Unprocessed schema. This is a bug in Zod.");const r=$=>{var z;const d=this.target==="draft-2020-12"?"$defs":"definitions";if(o.external){const N=(z=o.external.registry.get($[0]))==null?void 0:z.id,T=o.external.uri??(hu=>hu);if(N)return{ref:T(N)};const M=$[1].defId??$[1].schema.id??`schema${this.counter++}`;return $[1].defId=M,{defId:M,ref:`${T("__shared")}#/${d}/${M}`}}if($[1]===e)return{ref:"#"};const f=`#/${d}/`,b=$[1].schema.id??`__schema${this.counter++}`;return{defId:b,ref:f+b}},a=$=>{if($[1].schema.$ref)return;const d=$[1],{ref:p,defId:f}=r($);d.def={...d.schema},f&&(d.defId=f);const b=d.schema;for(const z in b)delete b[z];b.$ref=p};if(o.cycles==="throw")for(const $ of this.seen.entries()){const d=$[1];if(d.cycle)throw new Error(`Cycle detected: #/${(g=d.cycle)==null?void 0:g.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(const $ of this.seen.entries()){const d=$[1];if(t===$[0]){a($);continue}if(o.external){const f=(_=o.external.registry.get($[0]))==null?void 0:_.id;if(t!==$[0]&&f){a($);continue}}if((y=this.metadataRegistry.get($[0]))==null?void 0:y.id){a($);continue}if(d.cycle){a($);continue}if(d.count>1&&o.reused==="ref"){a($);continue}}const u=($,d)=>{const p=this.seen.get($),f=p.def??p.schema,b={...f};if(p.ref===null)return;const z=p.ref;if(p.ref=null,z){u(z,d);const N=this.seen.get(z).schema;N.$ref&&d.target==="draft-7"?(f.allOf=f.allOf??[],f.allOf.push(N)):(Object.assign(f,N),Object.assign(f,b))}p.isParent||this.override({zodSchema:$,jsonSchema:f,path:p.path??[]})};for(const $ of[...this.seen.entries()].reverse())u($[0],{target:this.target});const l={};if(this.target==="draft-2020-12"?l.$schema="https://json-schema.org/draft/2020-12/schema":this.target==="draft-7"?l.$schema="http://json-schema.org/draft-07/schema#":console.warn(`Invalid target: ${this.target}`),(w=o.external)!=null&&w.uri){const $=(h=o.external.registry.get(t))==null?void 0:h.id;if(!$)throw new Error("Schema is missing an `id` property");l.$id=o.external.uri($)}Object.assign(l,e.def);const s=((D=o.external)==null?void 0:D.defs)??{};for(const $ of this.seen.entries()){const d=$[1];d.def&&d.defId&&(s[d.defId]=d.def)}o.external||Object.keys(s).length>0&&(this.target==="draft-2020-12"?l.$defs=s:l.definitions=s);try{return JSON.parse(JSON.stringify(l))}catch{throw new Error("Error converting schema to JSON.")}}}function _a(n,t){if(n instanceof bn){const o=new qe(t),e={};for(const u of n._idmap.entries()){const[l,s]=u;o.process(s)}const r={},a={registry:n,uri:t==null?void 0:t.uri,defs:e};for(const u of n._idmap.entries()){const[l,s]=u;r[l]=o.emit(s,{...t,external:a})}if(Object.keys(e).length>0){const u=o.target==="draft-2020-12"?"$defs":"definitions";r.__shared={[u]:e}}return{schemas:r}}const i=new qe(t);return i.process(n),i.emit(n,t)}function Z(n,t){const i=t??{seen:new Set};if(i.seen.has(n))return!1;i.seen.add(n);const e=n._zod.def;switch(e.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":return!1;case"array":return Z(e.element,i);case"object":{for(const r in e.shape)if(Z(e.shape[r],i))return!0;return!1}case"union":{for(const r of e.options)if(Z(r,i))return!0;return!1}case"intersection":return Z(e.left,i)||Z(e.right,i);case"tuple":{for(const r of e.items)if(Z(r,i))return!0;return!!(e.rest&&Z(e.rest,i))}case"record":return Z(e.keyType,i)||Z(e.valueType,i);case"map":return Z(e.keyType,i)||Z(e.valueType,i);case"set":return Z(e.valueType,i);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":return Z(e.innerType,i);case"lazy":return Z(e.getter(),i);case"default":return Z(e.innerType,i);case"prefault":return Z(e.innerType,i);case"custom":return!1;case"transform":return!0;case"pipe":return Z(e.in,i)||Z(e.out,i);case"success":return!1;case"catch":return!1}throw new Error(`Unknown schema type: ${e.type}`)}const Cl=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),Rl=Object.freeze(Object.defineProperty({__proto__:null,$ZodAny:eo,$ZodArray:gn,$ZodAsyncError:Q,$ZodBase64:Ji,$ZodBase64URL:Vi,$ZodBigInt:pn,$ZodBigIntFormat:qi,$ZodBoolean:vn,$ZodCIDRv4:Ri,$ZodCIDRv6:Fi,$ZodCUID:Ni,$ZodCUID2:Oi,$ZodCatch:bo,$ZodCheck:P,$ZodCheckBigIntFormat:oi,$ZodCheckEndsWith:hi,$ZodCheckGreaterThan:dn,$ZodCheckIncludes:pi,$ZodCheckLengthEquals:di,$ZodCheckLessThan:sn,$ZodCheckLowerCase:fi,$ZodCheckMaxLength:li,$ZodCheckMaxSize:ai,$ZodCheckMimeType:_i,$ZodCheckMinLength:si,$ZodCheckMinSize:ui,$ZodCheckMultipleOf:ri,$ZodCheckNumberFormat:ii,$ZodCheckOverwrite:bi,$ZodCheckProperty:$i,$ZodCheckRegex:mi,$ZodCheckSizeEquals:ci,$ZodCheckStartsWith:gi,$ZodCheckStringFormat:me,$ZodCheckUpperCase:vi,$ZodCustom:So,$ZodCustomStringFormat:Ki,$ZodDate:ro,$ZodDefault:go,$ZodDiscriminatedUnion:oo,$ZodE164:Gi,$ZodEmail:zi,$ZodEmoji:ji,$ZodEnum:so,$ZodError:nn,$ZodFile:fo,$ZodFunction:ha,$ZodGUID:Ii,$ZodIPv4:Li,$ZodIPv6:Ci,$ZodISODate:Ti,$ZodISODateTime:Zi,$ZodISODuration:Ai,$ZodISOTime:Ei,$ZodIntersection:ao,$ZodJWT:Bi,$ZodKSUID:Di,$ZodLazy:zo,$ZodLiteral:mo,$ZodMap:co,$ZodNaN:yo,$ZodNanoID:xi,$ZodNever:no,$ZodNonOptional:$o,$ZodNull:Qi,$ZodNullable:po,$ZodNumber:fn,$ZodNumberFormat:Xi,$ZodObject:io,$ZodOptional:vo,$ZodPipe:_n,$ZodPrefault:ho,$ZodPromise:wo,$ZodReadonly:ko,$ZodRealError:de,$ZodRecord:uo,$ZodRegistry:bn,$ZodSet:lo,$ZodString:fe,$ZodStringFormat:O,$ZodSuccess:_o,$ZodSymbol:Yi,$ZodTemplateLiteral:Io,$ZodTransform:$n,$ZodTuple:Ue,$ZodType:k,$ZodULID:Ui,$ZodURL:Si,$ZodUUID:wi,$ZodUndefined:Hi,$ZodUnion:hn,$ZodUnknown:_e,$ZodVoid:to,$ZodXID:Pi,$brand:rr,$constructor:c,$input:Oo,$output:No,Doc:yi,JSONSchema:Cl,JSONSchemaGenerator:qe,NEVER:tr,TimePrecision:Do,_any:ea,_array:nt,_base64:Cn,_base64url:Rn,_bigint:Bo,_boolean:Go,_catch:Dl,_cidrv4:An,_cidrv6:Ln,_coercedBigint:Ko,_coercedBoolean:Wo,_coercedDate:ia,_coercedNumber:Co,_coercedString:Po,_cuid:On,_cuid2:Un,_custom:fa,_date:ra,_default:Ol,_discriminatedUnion:_l,_e164:Fn,_email:kn,_emoji:xn,_endsWith:Xn,_enum:wl,_file:ma,_float32:Fo,_float64:Jo,_gt:W,_gte:A,_guid:be,_includes:Bn,_int:Ro,_int32:Mo,_int64:Xo,_intersection:bl,_ipv4:Tn,_ipv6:En,_isoDate:To,_isoDateTime:Zo,_isoDuration:Ao,_isoTime:Eo,_jwt:Jn,_ksuid:Zn,_lazy:Al,_length:Ze,_literal:Sl,_lowercase:Gn,_lt:G,_lte:C,_map:kl,_max:C,_maxLength:De,_maxSize:Pe,_mime:qn,_min:A,_minLength:te,_minSize:le,_multipleOf:ce,_nan:oa,_nanoid:Nn,_nativeEnum:zl,_negative:ua,_never:na,_nonnegative:la,_nonoptional:Ul,_nonpositive:ca,_normalize:Yn,_null:Qo,_nullable:Nl,_number:Lo,_optional:xl,_overwrite:X,_parse:on,_parseAsync:an,_pipe:Zl,_positive:aa,_promise:Ll,_property:sa,_readonly:Tl,_record:yl,_refine:va,_regex:Vn,_safeParse:un,_safeParseAsync:cn,_set:Il,_size:Mn,_startsWith:Kn,_string:Uo,_stringFormat:ga,_stringbool:pa,_success:Pl,_symbol:Yo,_templateLiteral:El,_toLowerCase:Qn,_toUpperCase:et,_transform:jl,_trim:Hn,_tuple:da,_uint32:Vo,_uint64:qo,_ulid:Pn,_undefined:Ho,_union:$l,_unknown:ye,_uppercase:Wn,_url:jn,_uuid:In,_uuidv4:wn,_uuidv6:zn,_uuidv7:Sn,_void:ta,_xid:Dn,clone:F,config:E,flattenError:tn,formatError:rn,function:$a,globalConfig:he,globalRegistry:V,isValidBase64:mn,isValidBase64URL:Mi,isValidJWT:Wi,locales:xo,parse:Be,parseAsync:Ke,prettifyError:br,regexes:ni,registry:yn,safeParse:yr,safeParseAsync:kr,toDotPath:_r,toJSONSchema:_a,treeifyError:$r,util:Eu,version:ki},Symbol.toStringTag,{value:"Module"})),tt=c("ZodISODateTime",(n,t)=>{Zi.init(n,t),U.init(n,t)});function ba(n){return Zo(tt,n)}const rt=c("ZodISODate",(n,t)=>{Ti.init(n,t),U.init(n,t)});function ya(n){return To(rt,n)}const it=c("ZodISOTime",(n,t)=>{Ei.init(n,t),U.init(n,t)});function ka(n){return Eo(it,n)}const ot=c("ZodISODuration",(n,t)=>{Ai.init(n,t),U.init(n,t)});function Ia(n){return Ao(ot,n)}const Fl=Object.freeze(Object.defineProperty({__proto__:null,ZodISODate:rt,ZodISODateTime:tt,ZodISODuration:ot,ZodISOTime:it,date:ya,datetime:ba,duration:Ia,time:ka},Symbol.toStringTag,{value:"Module"})),wa=(n,t)=>{nn.init(n,t),n.name="ZodError",Object.defineProperties(n,{format:{value:i=>rn(n,i)},flatten:{value:i=>tn(n,i)},addIssue:{value:i=>n.issues.push(i)},addIssues:{value:i=>n.issues.push(...i)},isEmpty:{get(){return n.issues.length===0}}})},Jl=c("ZodError",wa),ve=c("ZodError",wa,{Parent:Error}),za=on(ve),Sa=an(ve),ja=un(ve),xa=cn(ve),S=c("ZodType",(n,t)=>(k.init(n,t),n.def=t,Object.defineProperty(n,"_def",{value:t}),n.check=(...i)=>n.clone({...t,checks:[...t.checks??[],...i.map(o=>typeof o=="function"?{_zod:{check:o,def:{check:"custom"},onattach:[]}}:o)]}),n.clone=(i,o)=>F(n,i,o),n.brand=()=>n,n.register=(i,o)=>(i.add(n,o),n),n.parse=(i,o)=>za(n,i,o,{callee:n.parse}),n.safeParse=(i,o)=>ja(n,i,o),n.parseAsync=async(i,o)=>Sa(n,i,o,{callee:n.parseAsync}),n.safeParseAsync=async(i,o)=>xa(n,i,o),n.spa=n.safeParseAsync,n.refine=(i,o)=>n.check(pu(i,o)),n.superRefine=i=>n.check(gu(i)),n.overwrite=i=>n.check(X(i)),n.optional=()=>we(n),n.nullable=()=>ze(n),n.nullish=()=>we(ze(n)),n.nonoptional=i=>ru(n,i),n.array=()=>St(n),n.or=i=>Fe([n,i]),n.and=i=>Ma(n,i),n.transform=i=>Se(n,Ot(i)),n.default=i=>eu(n,i),n.prefault=i=>tu(n,i),n.catch=i=>au(n,i),n.pipe=i=>Se(n,i),n.readonly=()=>lu(n),n.describe=i=>{const o=n.clone();return V.add(o,{description:i}),o},Object.defineProperty(n,"description",{get(){var i;return(i=V.get(n))==null?void 0:i.description},configurable:!0}),n.meta=(...i)=>{if(i.length===0)return V.get(n);const o=n.clone();return V.add(o,i[0]),o},n.isOptional=()=>n.safeParse(void 0).success,n.isNullable=()=>n.safeParse(null).success,n)),at=c("_ZodString",(n,t)=>{fe.init(n,t),S.init(n,t);const i=n._zod.bag;n.format=i.format??null,n.minLength=i.minimum??null,n.maxLength=i.maximum??null,n.regex=(...o)=>n.check(Vn(...o)),n.includes=(...o)=>n.check(Bn(...o)),n.startsWith=(...o)=>n.check(Kn(...o)),n.endsWith=(...o)=>n.check(Xn(...o)),n.min=(...o)=>n.check(te(...o)),n.max=(...o)=>n.check(De(...o)),n.length=(...o)=>n.check(Ze(...o)),n.nonempty=(...o)=>n.check(te(1,...o)),n.lowercase=o=>n.check(Gn(o)),n.uppercase=o=>n.check(Wn(o)),n.trim=()=>n.check(Hn()),n.normalize=(...o)=>n.check(Yn(...o)),n.toLowerCase=()=>n.check(Qn()),n.toUpperCase=()=>n.check(et())}),Te=c("ZodString",(n,t)=>{fe.init(n,t),at.init(n,t),n.email=i=>n.check(kn(ut,i)),n.url=i=>n.check(jn(ct,i)),n.jwt=i=>n.check(Jn(It,i)),n.emoji=i=>n.check(xn(lt,i)),n.guid=i=>n.check(be(ke,i)),n.uuid=i=>n.check(In(J,i)),n.uuidv4=i=>n.check(wn(J,i)),n.uuidv6=i=>n.check(zn(J,i)),n.uuidv7=i=>n.check(Sn(J,i)),n.nanoid=i=>n.check(Nn(st,i)),n.guid=i=>n.check(be(ke,i)),n.cuid=i=>n.check(On(dt,i)),n.cuid2=i=>n.check(Un(mt,i)),n.ulid=i=>n.check(Pn(ft,i)),n.base64=i=>n.check(Cn(bt,i)),n.base64url=i=>n.check(Rn(yt,i)),n.xid=i=>n.check(Dn(vt,i)),n.ksuid=i=>n.check(Zn(pt,i)),n.ipv4=i=>n.check(Tn(gt,i)),n.ipv6=i=>n.check(En(ht,i)),n.cidrv4=i=>n.check(An($t,i)),n.cidrv6=i=>n.check(Ln(_t,i)),n.e164=i=>n.check(Fn(kt,i)),n.datetime=i=>n.check(ba(i)),n.date=i=>n.check(ya(i)),n.time=i=>n.check(ka(i)),n.duration=i=>n.check(Ia(i))});function Ye(n){return Uo(Te,n)}const U=c("ZodStringFormat",(n,t)=>{O.init(n,t),at.init(n,t)}),ut=c("ZodEmail",(n,t)=>{zi.init(n,t),U.init(n,t)});function Ml(n){return kn(ut,n)}const ke=c("ZodGUID",(n,t)=>{Ii.init(n,t),U.init(n,t)});function Vl(n){return be(ke,n)}const J=c("ZodUUID",(n,t)=>{wi.init(n,t),U.init(n,t)});function Gl(n){return In(J,n)}function Wl(n){return wn(J,n)}function Bl(n){return zn(J,n)}function Kl(n){return Sn(J,n)}const ct=c("ZodURL",(n,t)=>{Si.init(n,t),U.init(n,t)});function Xl(n){return jn(ct,n)}const lt=c("ZodEmoji",(n,t)=>{ji.init(n,t),U.init(n,t)});function ql(n){return xn(lt,n)}const st=c("ZodNanoID",(n,t)=>{xi.init(n,t),U.init(n,t)});function Yl(n){return Nn(st,n)}const dt=c("ZodCUID",(n,t)=>{Ni.init(n,t),U.init(n,t)});function Hl(n){return On(dt,n)}const mt=c("ZodCUID2",(n,t)=>{Oi.init(n,t),U.init(n,t)});function Ql(n){return Un(mt,n)}const ft=c("ZodULID",(n,t)=>{Ui.init(n,t),U.init(n,t)});function es(n){return Pn(ft,n)}const vt=c("ZodXID",(n,t)=>{Pi.init(n,t),U.init(n,t)});function ns(n){return Dn(vt,n)}const pt=c("ZodKSUID",(n,t)=>{Di.init(n,t),U.init(n,t)});function ts(n){return Zn(pt,n)}const gt=c("ZodIPv4",(n,t)=>{Li.init(n,t),U.init(n,t)});function rs(n){return Tn(gt,n)}const ht=c("ZodIPv6",(n,t)=>{Ci.init(n,t),U.init(n,t)});function is(n){return En(ht,n)}const $t=c("ZodCIDRv4",(n,t)=>{Ri.init(n,t),U.init(n,t)});function os(n){return An($t,n)}const _t=c("ZodCIDRv6",(n,t)=>{Fi.init(n,t),U.init(n,t)});function as(n){return Ln(_t,n)}const bt=c("ZodBase64",(n,t)=>{Ji.init(n,t),U.init(n,t)});function us(n){return Cn(bt,n)}const yt=c("ZodBase64URL",(n,t)=>{Vi.init(n,t),U.init(n,t)});function cs(n){return Rn(yt,n)}const kt=c("ZodE164",(n,t)=>{Gi.init(n,t),U.init(n,t)});function ls(n){return Fn(kt,n)}const It=c("ZodJWT",(n,t)=>{Bi.init(n,t),U.init(n,t)});function ss(n){return Jn(It,n)}const Na=c("ZodCustomStringFormat",(n,t)=>{Ki.init(n,t),U.init(n,t)});function ds(n,t,i={}){return ga(Na,n,t,i)}const Ee=c("ZodNumber",(n,t)=>{fn.init(n,t),S.init(n,t),n.gt=(o,e)=>n.check(W(o,e)),n.gte=(o,e)=>n.check(A(o,e)),n.min=(o,e)=>n.check(A(o,e)),n.lt=(o,e)=>n.check(G(o,e)),n.lte=(o,e)=>n.check(C(o,e)),n.max=(o,e)=>n.check(C(o,e)),n.int=o=>n.check(He(o)),n.safe=o=>n.check(He(o)),n.positive=o=>n.check(W(0,o)),n.nonnegative=o=>n.check(A(0,o)),n.negative=o=>n.check(G(0,o)),n.nonpositive=o=>n.check(C(0,o)),n.multipleOf=(o,e)=>n.check(ce(o,e)),n.step=(o,e)=>n.check(ce(o,e)),n.finite=()=>n;const i=n._zod.bag;n.minValue=Math.max(i.minimum??Number.NEGATIVE_INFINITY,i.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,n.maxValue=Math.min(i.maximum??Number.POSITIVE_INFINITY,i.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,n.isInt=(i.format??"").includes("int")||Number.isSafeInteger(i.multipleOf??.5),n.isFinite=!0,n.format=i.format??null});function Oa(n){return Lo(Ee,n)}const ie=c("ZodNumberFormat",(n,t)=>{Xi.init(n,t),Ee.init(n,t)});function He(n){return Ro(ie,n)}function ms(n){return Fo(ie,n)}function fs(n){return Jo(ie,n)}function vs(n){return Mo(ie,n)}function ps(n){return Vo(ie,n)}const Ae=c("ZodBoolean",(n,t)=>{vn.init(n,t),S.init(n,t)});function Ua(n){return Go(Ae,n)}const Le=c("ZodBigInt",(n,t)=>{pn.init(n,t),S.init(n,t),n.gte=(o,e)=>n.check(A(o,e)),n.min=(o,e)=>n.check(A(o,e)),n.gt=(o,e)=>n.check(W(o,e)),n.gte=(o,e)=>n.check(A(o,e)),n.min=(o,e)=>n.check(A(o,e)),n.lt=(o,e)=>n.check(G(o,e)),n.lte=(o,e)=>n.check(C(o,e)),n.max=(o,e)=>n.check(C(o,e)),n.positive=o=>n.check(W(BigInt(0),o)),n.negative=o=>n.check(G(BigInt(0),o)),n.nonpositive=o=>n.check(C(BigInt(0),o)),n.nonnegative=o=>n.check(A(BigInt(0),o)),n.multipleOf=(o,e)=>n.check(ce(o,e));const i=n._zod.bag;n.minValue=i.minimum??null,n.maxValue=i.maximum??null,n.format=i.format??null});function gs(n){return Bo(Le,n)}const wt=c("ZodBigIntFormat",(n,t)=>{qi.init(n,t),Le.init(n,t)});function hs(n){return Xo(wt,n)}function $s(n){return qo(wt,n)}const Pa=c("ZodSymbol",(n,t)=>{Yi.init(n,t),S.init(n,t)});function _s(n){return Yo(Pa,n)}const Da=c("ZodUndefined",(n,t)=>{Hi.init(n,t),S.init(n,t)});function bs(n){return Ho(Da,n)}const Za=c("ZodNull",(n,t)=>{Qi.init(n,t),S.init(n,t)});function Ta(n){return Qo(Za,n)}const Ea=c("ZodAny",(n,t)=>{eo.init(n,t),S.init(n,t)});function ys(){return ea(Ea)}const Aa=c("ZodUnknown",(n,t)=>{_e.init(n,t),S.init(n,t)});function Ie(){return ye(Aa)}const La=c("ZodNever",(n,t)=>{no.init(n,t),S.init(n,t)});function Ce(n){return na(La,n)}const Ca=c("ZodVoid",(n,t)=>{to.init(n,t),S.init(n,t)});function ks(n){return ta(Ca,n)}const zt=c("ZodDate",(n,t)=>{ro.init(n,t),S.init(n,t),n.min=(o,e)=>n.check(A(o,e)),n.max=(o,e)=>n.check(C(o,e));const i=n._zod.bag;n.minDate=i.minimum?new Date(i.minimum):null,n.maxDate=i.maximum?new Date(i.maximum):null});function Is(n){return ra(zt,n)}const Ra=c("ZodArray",(n,t)=>{gn.init(n,t),S.init(n,t),n.element=t.element,n.min=(i,o)=>n.check(te(i,o)),n.nonempty=i=>n.check(te(1,i)),n.max=(i,o)=>n.check(De(i,o)),n.length=(i,o)=>n.check(Ze(i,o)),n.unwrap=()=>n.element});function St(n,t){return nt(Ra,n,t)}function ws(n){const t=n._zod.def.shape;return qa(Object.keys(t))}const Re=c("ZodObject",(n,t)=>{io.init(n,t),S.init(n,t),x(n,"shape",()=>t.shape),n.keyof=()=>Ka(Object.keys(n._zod.def.shape)),n.catchall=i=>n.clone({...n._zod.def,catchall:i}),n.passthrough=()=>n.clone({...n._zod.def,catchall:Ie()}),n.loose=()=>n.clone({...n._zod.def,catchall:Ie()}),n.strict=()=>n.clone({...n._zod.def,catchall:Ce()}),n.strip=()=>n.clone({...n._zod.def,catchall:void 0}),n.extend=i=>fr(n,i),n.merge=i=>vr(n,i),n.pick=i=>dr(n,i),n.omit=i=>mr(n,i),n.partial=(...i)=>pr(Ut,n,i[0]),n.required=(...i)=>gr(Pt,n,i[0])});function zs(n,t){const i={type:"object",get shape(){return re(this,"shape",{...n}),this.shape},...m(t)};return new Re(i)}function Ss(n,t){return new Re({type:"object",get shape(){return re(this,"shape",{...n}),this.shape},catchall:Ce(),...m(t)})}function js(n,t){return new Re({type:"object",get shape(){return re(this,"shape",{...n}),this.shape},catchall:Ie(),...m(t)})}const jt=c("ZodUnion",(n,t)=>{hn.init(n,t),S.init(n,t),n.options=t.options});function Fe(n,t){return new jt({type:"union",options:n,...m(t)})}const Fa=c("ZodDiscriminatedUnion",(n,t)=>{jt.init(n,t),oo.init(n,t)});function xs(n,t,i){return new Fa({type:"union",options:t,discriminator:n,...m(i)})}const Ja=c("ZodIntersection",(n,t)=>{ao.init(n,t),S.init(n,t)});function Ma(n,t){return new Ja({type:"intersection",left:n,right:t})}const Va=c("ZodTuple",(n,t)=>{Ue.init(n,t),S.init(n,t),n.rest=i=>n.clone({...n._zod.def,rest:i})});function Ns(n,t,i){const o=t instanceof k,e=o?i:t,r=o?t:null;return new Va({type:"tuple",items:n,rest:r,...m(e)})}const xt=c("ZodRecord",(n,t)=>{uo.init(n,t),S.init(n,t),n.keyType=t.keyType,n.valueType=t.valueType});function Ga(n,t,i){return new xt({type:"record",keyType:n,valueType:t,...m(i)})}function Os(n,t,i){return new xt({type:"record",keyType:Fe([n,Ce()]),valueType:t,...m(i)})}const Wa=c("ZodMap",(n,t)=>{co.init(n,t),S.init(n,t),n.keyType=t.keyType,n.valueType=t.valueType});function Us(n,t,i){return new Wa({type:"map",keyType:n,valueType:t,...m(i)})}const Ba=c("ZodSet",(n,t)=>{lo.init(n,t),S.init(n,t),n.min=(...i)=>n.check(le(...i)),n.nonempty=i=>n.check(le(1,i)),n.max=(...i)=>n.check(Pe(...i)),n.size=(...i)=>n.check(Mn(...i))});function Ps(n,t){return new Ba({type:"set",valueType:n,...m(t)})}const se=c("ZodEnum",(n,t)=>{so.init(n,t),S.init(n,t),n.enum=t.entries,n.options=Object.values(t.entries);const i=new Set(Object.keys(t.entries));n.extract=(o,e)=>{const r={};for(const a of o)if(i.has(a))r[a]=t.entries[a];else throw new Error(`Key ${a} not found in enum`);return new se({...t,checks:[],...m(e),entries:r})},n.exclude=(o,e)=>{const r={...t.entries};for(const a of o)if(i.has(a))delete r[a];else throw new Error(`Key ${a} not found in enum`);return new se({...t,checks:[],...m(e),entries:r})}});function Ka(n,t){const i=Array.isArray(n)?Object.fromEntries(n.map(o=>[o,o])):n;return new se({type:"enum",entries:i,...m(t)})}function Ds(n,t){return new se({type:"enum",entries:n,...m(t)})}const Xa=c("ZodLiteral",(n,t)=>{mo.init(n,t),S.init(n,t),n.values=new Set(t.values),Object.defineProperty(n,"value",{get(){if(t.values.length>1)throw new Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function qa(n,t){return new Xa({type:"literal",values:Array.isArray(n)?n:[n],...m(t)})}const Ya=c("ZodFile",(n,t)=>{fo.init(n,t),S.init(n,t),n.min=(i,o)=>n.check(le(i,o)),n.max=(i,o)=>n.check(Pe(i,o)),n.mime=(i,o)=>n.check(qn(Array.isArray(i)?i:[i],o))});function Zs(n){return ma(Ya,n)}const Nt=c("ZodTransform",(n,t)=>{$n.init(n,t),S.init(n,t),n._zod.parse=(i,o)=>{i.addIssue=r=>{if(typeof r=="string")i.issues.push(ee(r,i.value,t));else{const a=r;a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=i.value),a.inst??(a.inst=n),a.continue??(a.continue=!0),i.issues.push(ee(a))}};const e=t.transform(i.value,i);return e instanceof Promise?e.then(r=>(i.value=r,i)):(i.value=e,i)}});function Ot(n){return new Nt({type:"transform",transform:n})}const Ut=c("ZodOptional",(n,t)=>{vo.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.innerType});function we(n){return new Ut({type:"optional",innerType:n})}const Ha=c("ZodNullable",(n,t)=>{po.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.innerType});function ze(n){return new Ha({type:"nullable",innerType:n})}function Ts(n){return we(ze(n))}const Qa=c("ZodDefault",(n,t)=>{go.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.innerType,n.removeDefault=n.unwrap});function eu(n,t){return new Qa({type:"default",innerType:n,get defaultValue(){return typeof t=="function"?t():t}})}const nu=c("ZodPrefault",(n,t)=>{ho.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.innerType});function tu(n,t){return new nu({type:"prefault",innerType:n,get defaultValue(){return typeof t=="function"?t():t}})}const Pt=c("ZodNonOptional",(n,t)=>{$o.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.innerType});function ru(n,t){return new Pt({type:"nonoptional",innerType:n,...m(t)})}const iu=c("ZodSuccess",(n,t)=>{_o.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.innerType});function Es(n){return new iu({type:"success",innerType:n})}const ou=c("ZodCatch",(n,t)=>{bo.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.innerType,n.removeCatch=n.unwrap});function au(n,t){return new ou({type:"catch",innerType:n,catchValue:typeof t=="function"?t:()=>t})}const uu=c("ZodNaN",(n,t)=>{yo.init(n,t),S.init(n,t)});function As(n){return oa(uu,n)}const Dt=c("ZodPipe",(n,t)=>{_n.init(n,t),S.init(n,t),n.in=t.in,n.out=t.out});function Se(n,t){return new Dt({type:"pipe",in:n,out:t})}const cu=c("ZodReadonly",(n,t)=>{ko.init(n,t),S.init(n,t)});function lu(n){return new cu({type:"readonly",innerType:n})}const su=c("ZodTemplateLiteral",(n,t)=>{Io.init(n,t),S.init(n,t)});function Ls(n,t){return new su({type:"template_literal",parts:n,...m(t)})}const du=c("ZodLazy",(n,t)=>{zo.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.getter()});function mu(n){return new du({type:"lazy",getter:n})}const fu=c("ZodPromise",(n,t)=>{wo.init(n,t),S.init(n,t),n.unwrap=()=>n._zod.def.innerType});function Cs(n){return new fu({type:"promise",innerType:n})}const Je=c("ZodCustom",(n,t)=>{So.init(n,t),S.init(n,t)});function vu(n){const t=new P({check:"custom"});return t._zod.check=n,t}function Rs(n,t){return fa(Je,n??(()=>!0),t)}function pu(n,t={}){return va(Je,n,t)}function gu(n){const t=vu(i=>(i.addIssue=o=>{if(typeof o=="string")i.issues.push(ee(o,i.value,t._zod.def));else{const e=o;e.fatal&&(e.continue=!1),e.code??(e.code="custom"),e.input??(e.input=i.value),e.inst??(e.inst=t),e.continue??(e.continue=!t._zod.def.abort),i.issues.push(ee(e))}},n(i.value,i)));return t}function Fs(n,t={error:`Input not instance of ${n.name}`}){const i=new Je({type:"custom",check:"custom",fn:o=>o instanceof n,abort:!0,...m(t)});return i._zod.bag.Class=n,i}const Js=(...n)=>pa({Pipe:Dt,Boolean:Ae,String:Te,Transform:Nt},...n);function Ms(n){const t=mu(()=>Fe([Ye(n),Oa(),Ua(),Ta(),St(t),Ga(Ye(),t)]));return t}function Vs(n,t){return Se(Ot(n),t)}const Gs={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function Ws(n){E({customError:n})}function Bs(){return E().customError}function Ks(n){return Po(Te,n)}function Xs(n){return Co(Ee,n)}function qs(n){return Wo(Ae,n)}function Ys(n){return Ko(Le,n)}function Hs(n){return ia(zt,n)}const Qs=Object.freeze(Object.defineProperty({__proto__:null,bigint:Ys,boolean:qs,date:Hs,number:Xs,string:Ks},Symbol.toStringTag,{value:"Module"}));E(jo());const Ge=Object.freeze(Object.defineProperty({__proto__:null,$brand:rr,$input:Oo,$output:No,NEVER:tr,TimePrecision:Do,ZodAny:Ea,ZodArray:Ra,ZodBase64:bt,ZodBase64URL:yt,ZodBigInt:Le,ZodBigIntFormat:wt,ZodBoolean:Ae,ZodCIDRv4:$t,ZodCIDRv6:_t,ZodCUID:dt,ZodCUID2:mt,ZodCatch:ou,ZodCustom:Je,ZodCustomStringFormat:Na,ZodDate:zt,ZodDefault:Qa,ZodDiscriminatedUnion:Fa,ZodE164:kt,ZodEmail:ut,ZodEmoji:lt,ZodEnum:se,ZodError:Jl,ZodFile:Ya,ZodGUID:ke,ZodIPv4:gt,ZodIPv6:ht,ZodISODate:rt,ZodISODateTime:tt,ZodISODuration:ot,ZodISOTime:it,ZodIntersection:Ja,ZodIssueCode:Gs,ZodJWT:It,ZodKSUID:pt,ZodLazy:du,ZodLiteral:Xa,ZodMap:Wa,ZodNaN:uu,ZodNanoID:st,ZodNever:La,ZodNonOptional:Pt,ZodNull:Za,ZodNullable:Ha,ZodNumber:Ee,ZodNumberFormat:ie,ZodObject:Re,ZodOptional:Ut,ZodPipe:Dt,ZodPrefault:nu,ZodPromise:fu,ZodReadonly:cu,ZodRealError:ve,ZodRecord:xt,ZodSet:Ba,ZodString:Te,ZodStringFormat:U,ZodSuccess:iu,ZodSymbol:Pa,ZodTemplateLiteral:su,ZodTransform:Nt,ZodTuple:Va,ZodType:S,ZodULID:ft,ZodURL:ct,ZodUUID:J,ZodUndefined:Da,ZodUnion:jt,ZodUnknown:Aa,ZodVoid:Ca,ZodXID:vt,_ZodString:at,_default:eu,any:ys,array:St,base64:us,base64url:cs,bigint:gs,boolean:Ua,catch:au,check:vu,cidrv4:os,cidrv6:as,clone:F,coerce:Qs,config:E,core:Rl,cuid:Hl,cuid2:Ql,custom:Rs,date:Is,discriminatedUnion:xs,e164:ls,email:Ml,emoji:ql,endsWith:Xn,enum:Ka,file:Zs,flattenError:tn,float32:ms,float64:fs,formatError:rn,function:$a,getErrorMap:Bs,globalRegistry:V,gt:W,gte:A,guid:Vl,includes:Bn,instanceof:Fs,int:He,int32:vs,int64:hs,intersection:Ma,ipv4:rs,ipv6:is,iso:Fl,json:Ms,jwt:ss,keyof:ws,ksuid:ts,lazy:mu,length:Ze,literal:qa,locales:xo,looseObject:js,lowercase:Gn,lt:G,lte:C,map:Us,maxLength:De,maxSize:Pe,mime:qn,minLength:te,minSize:le,multipleOf:ce,nan:As,nanoid:Yl,nativeEnum:Ds,negative:ua,never:Ce,nonnegative:la,nonoptional:ru,nonpositive:ca,normalize:Yn,null:Ta,nullable:ze,nullish:Ts,number:Oa,object:zs,optional:we,overwrite:X,parse:za,parseAsync:Sa,partialRecord:Os,pipe:Se,positive:aa,prefault:tu,preprocess:Vs,prettifyError:br,promise:Cs,property:sa,readonly:lu,record:Ga,refine:pu,regex:Vn,regexes:ni,registry:yn,safeParse:ja,safeParseAsync:xa,set:Ps,setErrorMap:Ws,size:Mn,startsWith:Kn,strictObject:Ss,string:Ye,stringFormat:ds,stringbool:Js,success:Es,superRefine:gu,symbol:_s,templateLiteral:Ls,toJSONSchema:_a,toLowerCase:Qn,toUpperCase:et,transform:Ot,treeifyError:$r,trim:Hn,tuple:Ns,uint32:ps,uint64:$s,ulid:es,undefined:bs,union:Fe,unknown:Ie,uppercase:Wn,url:Xl,uuid:Gl,uuidv4:Wl,uuidv6:Bl,uuidv7:Kl,void:ks,xid:ns},Symbol.toStringTag,{value:"Module"}));function ed(){const n=$u({from:"/auth"}),{isPending:t}=We.useSession(),i=_u(),[o,e]=Zt.useState(!1),[r,a]=Zt.useState(""),u=ku({mutationFn:s=>i.email.sendPasswordResetEmail.mutate({email:s}),onSuccess:()=>{q.success("Password reset email sent! Please check your inbox."),e(!1),a("")},onError:s=>{q.error(s.message||"Failed to send password reset email")}}),l=yu({defaultValues:{email:"",password:""},onSubmit:async({value:s})=>{await We.signIn.email({email:s.email,password:s.password},{onSuccess:()=>{q.success("Sign in successful"),n({to:"/dashboard"})},onError:g=>{g.error.message==="Email not verified"?q.error("Please check your email and click the verification link before signing in. A new verification email has been sent to your inbox."):q.error(g.error.message)}})},validators:{onSubmit:Ge.object({email:Ge.email("Invalid email address"),password:Ge.string().min(8,"Password must be at least 8 characters")})}});return t?j.jsx(nr,{}):o?j.jsxs(Et,{className:"mx-auto w-full mt-10 max-w-md",children:[j.jsxs(At,{children:[j.jsx(Lt,{className:"text-2xl",children:"Reset Password"}),j.jsx(Ct,{children:"Enter your email address and we'll send you a link to reset your password"})]}),j.jsx(Rt,{children:j.jsxs("div",{className:"space-y-6",children:[j.jsxs("div",{className:"space-y-2",children:[j.jsx(Me,{htmlFor:"forgot-email",children:"Email Address"}),j.jsx(Ve,{id:"forgot-email",type:"email",placeholder:"Enter your email",value:r,onChange:s=>a(s.target.value)})]}),j.jsx(Tt,{type:"button",onClick:()=>{r?u.mutate(r):q.error("Please enter your email address")},disabled:u.isPending,className:"w-full",children:u.isPending?"Sending...":"Send Reset Email"}),j.jsx("div",{className:"text-center",children:j.jsx("button",{type:"button",onClick:()=>{e(!1),a("")},className:"text-sm text-muted-foreground hover:text-primary underline cursor-pointer",children:"Back to Sign In"})})]})})]}):j.jsxs(Et,{className:"mx-auto w-full mt-10 max-w-md",children:[j.jsxs(At,{children:[j.jsx(Lt,{className:"text-2xl",children:"Sign In to Soundmera"}),j.jsx(Ct,{children:"Enter your email and password to sign in to your account"})]}),j.jsx(Rt,{children:j.jsxs("form",{onSubmit:s=>{s.preventDefault(),s.stopPropagation(),l.handleSubmit()},className:"space-y-6",children:[j.jsx("div",{children:j.jsx(l.Field,{name:"email",children:s=>j.jsxs("div",{className:"space-y-2",children:[j.jsx(Me,{htmlFor:s.name,children:"Email"}),j.jsx(Ve,{id:s.name,name:s.name,type:"email",placeholder:"Enter your email",value:s.state.value,onBlur:s.handleBlur,onChange:g=>s.handleChange(g.target.value)}),s.state.meta.errors.map(g=>j.jsx("p",{className:"text-destructive text-sm",children:g==null?void 0:g.message},g==null?void 0:g.message))]})})}),j.jsx("div",{children:j.jsx(l.Field,{name:"password",children:s=>j.jsxs("div",{className:"space-y-2",children:[j.jsx(Me,{htmlFor:s.name,children:"Password"}),j.jsx(Ve,{id:s.name,name:s.name,type:"password",placeholder:"Enter your password",value:s.state.value,onBlur:s.handleBlur,onChange:g=>s.handleChange(g.target.value)}),s.state.meta.errors.map(g=>j.jsx("p",{className:"text-destructive text-sm",children:g==null?void 0:g.message},g==null?void 0:g.message))]})})}),j.jsx(l.Subscribe,{children:s=>j.jsx(Tt,{type:"submit",className:"w-full",disabled:!s.canSubmit||s.isSubmitting,children:s.isSubmitting?"Signing in...":"Sign In"})}),j.jsx("div",{className:"text-center",children:j.jsx("button",{type:"button",onClick:()=>e(!0),className:"text-sm text-muted-foreground hover:text-primary underline cursor-pointer",children:"Forgot your password?"})})]})})]})}const cd=function(){const{data:t,isPending:i}=We.useSession();return t?j.jsx(bu,{to:"/dashboard"}):i?j.jsx(nr,{}):j.jsx(ed,{})};export{cd as component};
