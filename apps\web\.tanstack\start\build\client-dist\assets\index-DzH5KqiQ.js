import{a as J,u as Ne,t as k,j as e,r as d,g as be,v as ve,k as G}from"./main-CiHxKC0e.js";import{a as W}from"./auth-client-Bm508aVF.js";import{E as Ce,A as Y,a as Z,b as ee,c as se,d as te,e as ae,f as ne,g as ie,h as le,u as we,t as ye,v as Se,w as Ae,x as ze,y as De,C as ke,T as Me,i as Ie,j as w,k as Pe,l as P,m as Fe,n as A,P as Te,o as Le,p as z,q as Ve,r as $e,s as Re}from"./table-TBKnZXRI.js";import{u as Ee}from"./useQuery-DciuO3_z.js";import{u as re}from"./useMutation-C9Oj6nVm.js";import{B as u,a as U}from"./button-mzA_gOdo.js";import{T as oe,C as y}from"./checkbox-CnzziXQ_.js";import{D as ce,a as de,b as me,e as He,f as F,d as Be,c as Ke,k as Ge,C as Ue}from"./dropdown-menu-BXhbheYO.js";import{I as qe,L as D}from"./label-C9y0GF5L.js";import{P as q,a as _,b as Q}from"./popover-CFVdoKjL.js";import{P as _e,h as Qe,i as Oe,S as Xe,d as Je,e as We,f as Ye,g as Ze}from"./select-Be91QLyZ.js";import{C as es}from"./create-artist-dialog-CTfN1z1l.js";import{B as O}from"./badge-BuhFyAsn.js";import{U as ss,P as ts}from"./platform-badge-D4DrBeJd.js";import{E as as}from"./eye-DEOsx-Wb.js";import{S as ns}from"./square-pen-cru0dwrj.js";import{C as T}from"./circle-alert-CqYvwAi4.js";import{c as is,M as ls}from"./genre-select-m4c2kc80.js";import{L as rs}from"./list-filter-BGL9woXK.js";import{C as os}from"./circle-x-6nZ_0hIQ.js";import{F as X}from"./filter-Du3VjfXf.js";import"./dialog-B3qaM3HV.js";import"./scroll-area-ASo8OkdS.js";import"./country-dropdown-u20PekMn.js";function cs({row:r,onArtistDeleted:m}){const o=J(),a=Ne(),l=re({mutationFn:async()=>o.artist.delete.mutate({id:r.original.id}),onSuccess:()=>{k.success(`Successfully deleted artist ${r.original.name}`),m()},onError:j=>{console.error("Failed to delete artist:",j),k.error("Failed to delete artist: "+(j.message||"Unknown error"))}}),p=async()=>{try{await l.mutateAsync()}catch{}},f=()=>{a({to:"/dashboard/artist/$id",params:{id:r.original.id}})};return e.jsxs(ce,{children:[e.jsx(de,{asChild:!0,children:e.jsx("div",{className:"flex justify-end",children:e.jsx(u,{size:"icon",variant:"ghost",className:"shadow-none cursor-pointer","aria-label":"Edit item",children:e.jsx(Ce,{size:16,"aria-hidden":"true"})})})}),e.jsxs(me,{align:"end",children:[e.jsxs(He,{children:[e.jsxs(F,{onClick:f,className:"cursor-pointer",children:[e.jsx(as,{className:"h-4 w-4"}),e.jsx("span",{children:"View Details"})]}),e.jsx(ss,{artist:r.original,onArtistUpdated:m,children:e.jsxs(F,{onSelect:j=>j.preventDefault(),className:"cursor-pointer",children:[e.jsx(ns,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit Artist"})]})})]}),e.jsx(Be,{className:"h-[2px]"}),e.jsxs(Y,{children:[e.jsx(Z,{asChild:!0,children:e.jsx(F,{className:"text-destructive focus:text-destructive cursor-pointer",onSelect:j=>j.preventDefault(),children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(oe,{size:16,className:"text-destructive focus:text-destructive"}),e.jsx("span",{children:"Delete"})]})})}),e.jsxs(ee,{children:[e.jsxs("div",{className:"flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4",children:[e.jsx("div",{className:"flex size-9 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(T,{className:"opacity-80",size:16})}),e.jsxs(se,{children:[e.jsx(te,{children:"Delete Artist"}),e.jsxs(ae,{children:["Are you sure you want to delete"," ",e.jsx("strong",{children:r.original.name}),"? This action cannot be undone and will permanently remove the artist and all associated data."]})]})]}),e.jsxs(ne,{children:[e.jsx(ie,{children:"Cancel"}),e.jsx(le,{onClick:p,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:l.isPending,children:l.isPending?"Deleting...":"Delete Artist"})]})]})]})]})]})}const ds=(r,m,o)=>{const a=`${r.original.name}`.toLowerCase(),l=(o??"").toLowerCase();return a.includes(l)},ms=(r,m,o)=>{if(!(o!=null&&o.length))return!0;const a=r.getValue(m);return o.includes(a)},xs=(r,m)=>{const o=[{id:"select",header:({table:a})=>e.jsx(y,{checked:a.getIsAllPageRowsSelected()||a.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:l=>a.toggleAllPageRowsSelected(!!l),"aria-label":"Select all"}),cell:({row:a})=>e.jsx(y,{checked:a.getIsSelected(),onCheckedChange:l=>a.toggleSelected(!!l),"aria-label":"Select row"}),size:28,enableSorting:!1,enableHiding:!1},{header:"Artist Name",accessorKey:"name",cell:({row:a})=>e.jsx("div",{className:"font-medium",children:a.getValue("name")}),size:180,filterFn:ds,enableHiding:!1},{header:"Genre",accessorKey:"genre",cell:({row:a})=>e.jsx("div",{className:"text-sm",children:a.getValue("genre")?e.jsx(O,{variant:"secondary",children:is(a.getValue("genre"))}):e.jsx("span",{className:"text-muted-foreground",children:"Not specified"})}),size:120,filterFn:ms},{header:"Country",accessorKey:"country",cell:({row:a})=>e.jsx("div",{className:"text-sm",children:a.getValue("country")||e.jsx("span",{className:"text-muted-foreground",children:"Not specified"})}),size:100},{header:"Label",accessorKey:"label",cell:({row:a})=>{var l;return e.jsx("div",{className:"text-sm",children:((l=a.original.label)==null?void 0:l.name)||e.jsx("span",{className:"text-muted-foreground",children:"Not specified"})})},size:130},{header:"Platforms",accessorKey:"identifiers",cell:({row:a})=>{var p;const l=((p=a.original.identifiers)==null?void 0:p.filter(f=>f.identifier!=="Create New"))||[];return l.length===0?e.jsx("span",{className:"text-muted-foreground text-sm",children:"No platforms"}):e.jsxs("div",{className:"flex flex-wrap gap-1",children:[l.slice(0,3).map(f=>e.jsx(ts,{service:f.service,identifier:f.identifier,width:16,height:16,className:"text-xs h-7"},f.service)),l.length>3&&e.jsxs(O,{variant:"outline",className:"text-xs mt-1",children:["+",l.length-3]})]})},size:150,enableSorting:!1},{header:"Instagram",accessorKey:"instagram",cell:({row:a})=>e.jsx("div",{className:"text-sm",children:a.getValue("instagram")?e.jsxs("a",{href:`https://instagram.com/${a.getValue("instagram")}`,target:"_blank",rel:"noopener noreferrer",className:"hover:underline",children:["@",a.getValue("instagram")]}):e.jsx("span",{className:"text-muted-foreground",children:"Not provided"})}),size:130},{header:"Created At",accessorKey:"createdAt",cell:({row:a})=>{const l=new Date(a.getValue("createdAt"));return e.jsx("div",{className:"text-sm text-muted-foreground",children:l.toLocaleDateString()})},size:120}];return o.push({id:"actions",header:()=>e.jsx("span",{className:"sr-only",children:"Actions"}),cell:({row:a})=>e.jsx(cs,{row:a,onArtistDeleted:r}),size:80,enableHiding:!1}),m&&o.splice(o.length-1,0,{header:"Created By",accessorKey:"user",cell:({row:a})=>{var l,p;return e.jsxs("div",{className:"text-sm min-w-0 truncate",children:[((l=a.original.user)==null?void 0:l.email)??"-"," | ",((p=a.original.user)==null?void 0:p.name)??"-"]})},size:200}),o};function hs(){var H,B;const r=d.useId(),m=be(),o=J(),{data:a}=W.useSession(),l=((H=a==null?void 0:a.user)==null?void 0:H.role)==="admin",[p,f]=d.useState([]),[j,xe]=d.useState({instagram:!1,identifiers:!1}),[x,b]=d.useState({pageIndex:0,pageSize:10}),M=d.useRef(null),[he,ue]=d.useState([{id:"name",desc:!1}]),[v,L]=d.useState(""),[g,V]=d.useState([]),{data:n,isLoading:N,error:$,refetch:C}=Ee(m.artist.getAll.queryOptions({page:x.pageIndex+1,limit:x.pageSize,search:v||void 0})),ge=re({mutationFn:async s=>o.artist.delete.mutate({id:s}),onSuccess:()=>{k.success("Artist deleted successfully"),C()},onError:s=>{k.error(`Failed to delete artist: ${s.message}`)}}),pe=d.useMemo(()=>{let s=(n==null?void 0:n.artists)||[];return g.length>0&&(s=s.filter(t=>{var c;const i=((c=t.label)==null?void 0:c.name)||"Not specified";return g.includes(i)})),s.map(t=>({...t,createdAt:new Date(t.createdAt),updatedAt:new Date(t.updatedAt)}))},[n==null?void 0:n.artists,g]),fe=async()=>{const t=h.getSelectedRowModel().rows.map(i=>i.original.id);try{const i=t.map(c=>ge.mutateAsync(c));await Promise.all(i),h.resetRowSelection()}catch{}},S=d.useMemo(()=>xs(C,l),[C,l]),h=we({data:pe,columns:S,getCoreRowModel:De(),getSortedRowModel:ze(),onSortingChange:ue,enableSortingRemoval:!1,getPaginationRowModel:Ae(),onPaginationChange:b,onColumnFiltersChange:f,onColumnVisibilityChange:xe,getFilteredRowModel:Se(),getFacetedUniqueValues:ye(),state:{sorting:he,pagination:x,columnFilters:p,columnVisibility:j}}),I=d.useMemo(()=>{const t=((n==null?void 0:n.artists)||[]).map(i=>{var c;return((c=i.label)==null?void 0:c.name)||"Not specified"}).filter(i=>!!i);return[...new Set(t)].sort()},[n==null?void 0:n.artists]),R=d.useMemo(()=>{const s=(n==null?void 0:n.artists)||[],t=new Map;return s.forEach(i=>{var K;const c=((K=i.label)==null?void 0:K.name)||"Not specified",je=t.get(c)||0;t.set(c,je+1)}),t},[n==null?void 0:n.artists]),E=(s,t)=>{V(s?[t]:[])};return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(qe,{id:`${r}-input`,ref:M,className:U("peer min-w-60 max-sm:min-w-48 ps-9 h-9",!!v&&"pe-9"),value:v,onChange:s=>L(s.target.value),placeholder:"Search by artist name...",type:"text","aria-label":"Search by artist name"}),e.jsx("div",{className:"text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50",children:e.jsx(rs,{size:16,"aria-hidden":"true"})}),!!v&&e.jsx("button",{className:"text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","aria-label":"Clear search",onClick:()=>{L(""),M.current&&M.current.focus()},disabled:N,children:e.jsx(os,{size:16,"aria-hidden":"true"})})]}),e.jsxs(q,{children:[e.jsx(_,{asChild:!0,children:e.jsxs(u,{variant:"outline",className:"sm:hidden",children:[e.jsx(X,{className:"opacity-60",size:16,"aria-hidden":"true"}),g.length>0&&e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:g.length})]})}),e.jsx(Q,{className:"w-80 p-4",align:"start",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-muted-foreground text-sm font-medium",children:"Label"}),e.jsx("div",{className:"space-y-2",children:I.map((s,t)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{id:`${r}-mobile-label-${t}`,checked:g.includes(s),onCheckedChange:i=>E(i,s)}),e.jsxs(D,{htmlFor:`${r}-mobile-label-${t}`,className:"flex grow justify-between gap-2 font-normal",children:[s," ",e.jsx("span",{className:"text-muted-foreground ms-2 text-xs",children:R.get(s)||0})]})]},s))})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-muted-foreground text-sm font-medium",children:"Columns"}),e.jsx("div",{className:"space-y-2",children:h.getAllColumns().filter(s=>s.getCanHide()).map(s=>{const t=typeof s.columnDef.header=="string"?s.columnDef.header:s.id;return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{id:`${r}-mobile-column-${s.id}`,checked:s.getIsVisible(),onCheckedChange:i=>s.toggleVisibility(!!i)}),e.jsx(D,{htmlFor:`${r}-mobile-column-${s.id}`,className:"flex grow justify-between gap-2 font-normal capitalize",children:t})]},s.id)})})]})]})})]}),e.jsx("div",{className:"hidden sm:flex items-center gap-3",children:I.length>0&&e.jsxs(q,{children:[e.jsx(_,{asChild:!0,children:e.jsxs(u,{variant:"outline",children:[e.jsx(X,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Label",g.length>0&&e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:g.length})]})}),e.jsx(Q,{className:"w-auto min-w-36 p-3",align:"start",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-muted-foreground text-xs font-medium",children:"Label Filters"}),e.jsx("div",{className:"space-y-3",children:I.map((s,t)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{id:`${r}-label-${t}`,checked:g.includes(s),onCheckedChange:i=>E(i,s)}),e.jsxs(D,{htmlFor:`${r}-label-${t}`,className:"flex grow justify-between gap-2 font-normal",children:[s," ",e.jsx("span",{className:"text-muted-foreground ms-2 text-xs",children:R.get(s)||0})]})]},s))})]})})]})}),e.jsxs(ce,{children:[e.jsx(de,{asChild:!0,children:e.jsxs(u,{variant:"outline",className:"hidden sm:flex",children:[e.jsx(ke,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"View"]})}),e.jsxs(me,{align:"end",children:[e.jsx(Ke,{children:"Toggle columns"}),h.getAllColumns().filter(s=>s.getCanHide()).map(s=>{const t=typeof s.columnDef.header=="string"?s.columnDef.header:s.id;return e.jsx(Ge,{className:"capitalize",checked:s.getIsVisible(),onCheckedChange:i=>s.toggleVisibility(!!i),onSelect:i=>i.preventDefault(),children:t},s.id)})]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[h.getSelectedRowModel().rows.length>0&&e.jsxs(Y,{children:[e.jsx(Z,{asChild:!0,children:e.jsxs(u,{variant:"outline",children:[e.jsx(oe,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Delete",e.jsx("span",{className:"bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium",children:h.getSelectedRowModel().rows.length})]})}),e.jsxs(ee,{children:[e.jsxs("div",{className:"flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4",children:[e.jsx("div",{className:"flex size-9 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(T,{className:"opacity-80",size:16})}),e.jsxs(se,{children:[e.jsx(te,{children:"Are you absolutely sure?"}),e.jsxs(ae,{children:["This action cannot be undone. This will permanently delete"," ",h.getSelectedRowModel().rows.length," selected"," ",h.getSelectedRowModel().rows.length===1?"artist":"artists","."]})]})]}),e.jsxs(ne,{children:[e.jsx(ie,{children:"Cancel"}),e.jsx(le,{onClick:fe,children:"Delete"})]})]})]}),e.jsx(es,{onArtistCreated:C,children:e.jsxs(u,{variant:"outline",children:[e.jsx(_e,{className:"-ms-1 opacity-60",size:16,"aria-hidden":"true"}),"Add Artist"]})})]})]}),e.jsx("div",{className:"bg-background rounded-md border",children:e.jsxs(Me,{className:"table-fixed",children:[e.jsx(Ie,{children:h.getHeaderGroups().map(s=>e.jsx(w,{className:"hover:bg-transparent",children:s.headers.map(t=>e.jsx(Pe,{style:{width:`${t.getSize()}px`},className:"h-11",children:t.isPlaceholder?null:t.column.getCanSort()?e.jsxs("div",{className:U(t.column.getCanSort()&&"flex h-full cursor-pointer items-center justify-between gap-2 select-none"),onClick:t.column.getToggleSortingHandler(),onKeyDown:i=>{var c;t.column.getCanSort()&&(i.key==="Enter"||i.key===" ")&&(i.preventDefault(),(c=t.column.getToggleSortingHandler())==null||c(i))},tabIndex:t.column.getCanSort()?0:void 0,children:[P(t.column.columnDef.header,t.getContext()),{asc:e.jsx(Oe,{className:"shrink-0 opacity-60",size:16,"aria-hidden":"true"}),desc:e.jsx(Qe,{className:"shrink-0 opacity-60",size:16,"aria-hidden":"true"})}[t.column.getIsSorted()]??null]}):P(t.column.columnDef.header,t.getContext())},t.id))},s.id))}),e.jsx(Fe,{children:N?e.jsx(w,{children:e.jsx(A,{colSpan:S.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"}),"Loading artists..."]})})}):$?e.jsx(w,{children:e.jsx(A,{colSpan:S.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-2",children:[e.jsx(T,{className:"h-8 w-8 text-red-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Failed to load artists"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:$.message||"An unknown error occurred"}),e.jsx(u,{variant:"outline",size:"sm",onClick:()=>C(),className:"mt-2",children:"Try again"})]})]})})}):(B=h.getRowModel().rows)!=null&&B.length?h.getRowModel().rows.map(s=>e.jsx(w,{"data-state":s.getIsSelected()&&"selected",children:s.getVisibleCells().map(t=>e.jsx(A,{className:"last:py-0",children:P(t.column.columnDef.cell,t.getContext())},t.id))},s.id)):e.jsx(w,{children:e.jsx(A,{colSpan:S.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-2 p-6",children:[e.jsx(ls,{className:"h-8 w-8 text-muted-foreground"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"No artists found"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:v||g.length>0?"Try adjusting your search or filters":"No artists have been created yet"})]})]})})})})]})}),e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3",children:[e.jsx(D,{htmlFor:r,className:"text-sm whitespace-nowrap",children:"Rows per page"}),e.jsxs(Xe,{value:x.pageSize.toString(),onValueChange:s=>{b(t=>({...t,pageSize:Number(s),pageIndex:0}))},disabled:N,children:[e.jsx(Je,{id:r,className:"w-fit whitespace-nowrap min-w-[4rem]",children:e.jsx(We,{placeholder:"Select number of results"})}),e.jsx(Ye,{className:"[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2",children:[5,10,25,50].map(s=>e.jsx(Ze,{value:s.toString(),children:s},s))})]})]}),e.jsxs("div",{className:"flex items-center gap-4 sm:gap-8",children:[e.jsx("div",{className:"text-muted-foreground text-sm whitespace-nowrap",children:e.jsx("p",{className:"text-muted-foreground text-sm whitespace-nowrap","aria-live":"polite",children:N?"Loading...":e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"text-foreground",children:[x.pageIndex*x.pageSize+1,"-",Math.min((x.pageIndex+1)*x.pageSize,(n==null?void 0:n.pagination.total)||0)]})," ","of"," ",e.jsx("span",{className:"text-foreground",children:(n==null?void 0:n.pagination.total)||0})]})})}),e.jsx("div",{children:e.jsx(Te,{children:e.jsxs(Le,{className:"gap-1",children:[e.jsx(z,{children:e.jsx(u,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>b(s=>({...s,pageIndex:0})),disabled:x.pageIndex===0||N,"aria-label":"Go to first page",children:e.jsx(Ve,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(z,{children:e.jsx(u,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>b(s=>({...s,pageIndex:s.pageIndex-1})),disabled:x.pageIndex===0||N,"aria-label":"Go to previous page",children:e.jsx($e,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(z,{children:e.jsx(u,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>b(s=>({...s,pageIndex:s.pageIndex+1})),disabled:N||!(n!=null&&n.pagination)||x.pageIndex>=n.pagination.totalPages-1,"aria-label":"Go to next page",children:e.jsx(Ue,{size:14,className:"sm:size-4","aria-hidden":"true"})})}),e.jsx(z,{children:e.jsx(u,{size:"icon",variant:"outline",className:"disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>{const s=(n==null?void 0:n.pagination.totalPages)||1;b(t=>({...t,pageIndex:s-1}))},disabled:N||!(n!=null&&n.pagination)||x.pageIndex>=n.pagination.totalPages-1,"aria-label":"Go to last page",children:e.jsx(Re,{size:14,className:"sm:size-4","aria-hidden":"true"})})})]})})})]})]})]})}const Rs=function(){const m=ve.useNavigate(),{data:o,isPending:a}=W.useSession();return d.useEffect(()=>{if(!o&&!a){m({to:"/auth"});return}},[o,a,m]),a?e.jsx(G,{}):!o||!o.user?e.jsx(G,{}):e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"pl-2 text-xl font-bold",children:"Artist Management"}),e.jsx("div",{className:"p-2",children:e.jsx(hs,{})})]})};export{Rs as component};
