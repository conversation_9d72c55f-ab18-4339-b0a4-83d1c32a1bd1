import{r as P,a as se,t as m,j as e,L as E}from"./main-CiHxKC0e.js";import{u as ae}from"./useQuery-DciuO3_z.js";import{u as O}from"./useMutation-C9Oj6nVm.js";import{a as ie}from"./auth-client-Bm508aVF.js";import{j as $,k as re,l as z,m as q,n as B,o as Y,p as R}from"./dialog-B3qaM3HV.js";import{B as j}from"./button-mzA_gOdo.js";import{L as u,I as S}from"./label-C9y0GF5L.js";import{G as le,T as oe}from"./genre-select-m4c2kc80.js";import{S as V,d as G,e as K,f as H,g as k,P as te}from"./select-Be91QLyZ.js";import{T as ne,C as ce}from"./checkbox-CnzziXQ_.js";import{C as de}from"./country-dropdown-u20PekMn.js";import{B as M}from"./badge-BuhFyAsn.js";const Q=[{value:"YOUTUBE",label:"YouTube"},{value:"SPOTIFY",label:"Spotify"},{value:"APPLE_MUSIC",label:"Apple Music"},{value:"SOUNDCLOUD",label:"SoundCloud"},{value:"AUDIOMACK",label:"Audiomack"}],W=["SPOTIFY","APPLE_MUSIC"];function Pe({children:I,artist:t,onArtistUpdated:v}){var T;const[f,g]=P.useState(!1),p=se(),{data:w}=ie.useSession(),A=((T=w==null?void 0:w.user)==null?void 0:T.role)==="admin",[i,c]=P.useState({name:"",instagram:"",biography:"",country:"",genre:"",labelId:"",identifiers:[],spotifyNoProfile:!1,appleMusicNoProfile:!1}),{data:h,refetch:F,isLoading:L}=ae({queryKey:["labelsForSelect",t.id],queryFn:()=>p.label.getForSelect.query({artistId:t.id}),enabled:f}),[_,x]=P.useState(!1),[y,D]=P.useState(""),C=O({mutationFn:async()=>p.label.create.mutate({name:y.trim()}),onSuccess:async s=>{m.success("Label created successfully"),await F(),c(r=>({...r,labelId:s.id})),D(""),x(!1)},onError:s=>{m.error(s.message||"Failed to create label")}});P.useEffect(()=>{if(f){const s=t.identifiers||[],r=s.find(n=>n.service==="SPOTIFY"),a=s.find(n=>n.service==="APPLE_MUSIC"),o=[...s];r||o.push({service:"SPOTIFY",identifier:""}),a||o.push({service:"APPLE_MUSIC",identifier:""});const l=(r==null?void 0:r.identifier)==="Create New",N=(a==null?void 0:a.identifier)==="Create New";c({name:t.name||"",instagram:t.instagram||"",biography:t.biography||"",country:t.country||"",genre:t.genre||"",labelId:t.labelId||"",identifiers:o,spotifyNoProfile:l,appleMusicNoProfile:N})}},[f,t]);const d=O({mutationFn:async s=>p.artist.update.mutate(s),onSuccess:()=>{m.success("Artist updated successfully"),g(!1),v()},onError:s=>{console.error("Failed to update artist:",s),m.error("Failed to update artist: "+(s.message||"Unknown error"))}}),J=async s=>{if(s.preventDefault(),!i.name.trim()){m.error("Artist name is required");return}const r=i.identifiers.find(l=>l.service==="SPOTIFY"),a=i.identifiers.find(l=>l.service==="APPLE_MUSIC");if(!i.spotifyNoProfile&&(!r||!r.identifier.trim())){m.error("Spotify identifier is required or mark as 'Create a new profile for me'");return}if(!i.appleMusicNoProfile&&(!a||!a.identifier.trim())){m.error("Apple Music identifier is required or mark as 'Create a new profile for me'");return}const o=i.identifiers.filter(l=>l.identifier.trim()!=="");try{await d.mutateAsync({id:t.id,name:i.name.trim(),instagram:i.instagram.trim()||void 0,biography:i.biography.trim()||void 0,country:i.country.trim()||void 0,genre:i.genre.trim()||void 0,identifiers:o,labelId:i.labelId||void 0})}catch{}},b=(s,r)=>{c(a=>({...a,[s]:r}))},X=()=>{const s=i.identifiers.map(a=>a.service),r=Q.find(a=>!s.includes(a.value));if(!r){m.error("All available platforms have been added.");return}c(a=>({...a,identifiers:[...a.identifiers,{service:r.value,identifier:""}]}))},Z=s=>{const r=i.identifiers[s];if(W.includes(r.service)){m.error(`${r.service.replace("_"," ")} is required. Use "I don't have profile" option instead.`);return}c(a=>({...a,identifiers:a.identifiers.filter((o,l)=>l!==s)}))},U=(s,r,a)=>{c(o=>({...o,identifiers:o.identifiers.map((l,N)=>N===s?{...l,[r]:a}:l)}))},ee=(s,r)=>{const a=s==="SPOTIFY"?"spotifyNoProfile":"appleMusicNoProfile";c(o=>({...o,[a]:r})),c(r?o=>({...o,identifiers:o.identifiers.map(l=>l.service===s?{...l,identifier:"Create New"}:l)}):o=>({...o,identifiers:o.identifiers.map(l=>l.service===s?{...l,identifier:""}:l)}))};return e.jsxs($,{open:f,onOpenChange:g,children:[e.jsx(re,{asChild:!0,children:I}),e.jsxs(z,{className:"sm:max-w-[1300px] max-h-[90vh] overflow-y-auto",children:[e.jsxs(q,{children:[e.jsxs(B,{children:["Edit Artist: ",t.name]}),e.jsx(Y,{children:"Update the artist information. Spotify and Apple Music identifiers are required."})]}),e.jsxs("form",{onSubmit:J,className:"space-y-4",children:[e.jsx("div",{className:"space-y-4",children:A&&e.jsxs("div",{className:"space-y-2 md:col-span-2",children:[e.jsx(u,{htmlFor:"createdBy",children:"Created By"}),e.jsx(S,{id:"createdBy",value:`${t.user.email} | ${t.user.name}`,disabled:!0})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(u,{htmlFor:"name",className:"block",children:["Artist Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",role:"region","aria-live":"polite",children:"This is the name that will be displayed on the artist page, please make sure to use the correct name."}),e.jsx(S,{id:"name",placeholder:"Enter artist name",value:i.name,onChange:s=>b("name",s.target.value),required:!0,disabled:d.isPending})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"label",children:"Label"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:"Assign to a record label or create a new one."}),e.jsxs(V,{value:i.labelId,onValueChange:s=>{s==="__create__"?x(!0):b("labelId",s)},disabled:d.isPending||L,children:[e.jsx(G,{className:"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 text-muted-foreground cursor-pointer",children:e.jsx(K,{placeholder:"Select label"})}),e.jsxs(H,{children:[h==null?void 0:h.map(s=>e.jsx(k,{value:s.id,children:s.name},s.id)),e.jsx(k,{value:"__create__",children:"+ Create new label"})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"genre",children:"Genre"}),e.jsx(le,{value:i.genre,onValueChange:s=>b("genre",s),disabled:d.isPending,placeholder:"Select genre"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"country",children:"Country"}),e.jsx(de,{defaultValue:i.country,onChange:s=>b("country",s.name),disabled:d.isPending})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"instagram",children:"Instagram Handle"}),e.jsx(S,{id:"instagram",placeholder:"username (without @)",value:i.instagram,onChange:s=>b("instagram",s.target.value),disabled:d.isPending})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(u,{htmlFor:"biography",children:"Biography"}),e.jsx(oe,{id:"biography",placeholder:"Brief artist biography...",value:i.biography,onChange:s=>b("biography",s.target.value),disabled:d.isPending,rows:3})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6 mt-6",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs(u,{className:"block",children:["Platform Identifiers ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",role:"region","aria-live":"polite",children:"Add the platform identifiers for the artist. If you don't have a profile on a platform, we will create a new profile for you."})]}),e.jsxs(j,{type:"button",variant:"outline",size:"sm",onClick:X,disabled:d.isPending,children:[e.jsx(te,{className:"h-4 w-4"}),"Add Platform"]})]}),i.identifiers.map((s,r)=>{const a=W.includes(s.service),o=s.service==="SPOTIFY"&&i.spotifyNoProfile||s.service==="APPLE_MUSIC"&&i.appleMusicNoProfile,l=i.identifiers.map(n=>n.service),N=Q.filter(n=>!l.includes(n.value)||n.value===s.service);return e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex flex-col md:flex-row gap-2 md:items-end",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs(u,{className:"mb-2 block",children:["Platform"," ",a&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(V,{value:s.service,onValueChange:n=>U(r,"service",n),disabled:d.isPending||a,children:[e.jsx(G,{children:e.jsx(K,{})}),e.jsx(H,{children:N.map(n=>e.jsx(k,{value:n.value,children:n.label},n.value))})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsxs(u,{className:"mb-2 block",children:["Artist Page URL"," ",a&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(S,{placeholder:"Artist Page URL",value:s.identifier,onChange:n=>U(r,"identifier",n.target.value),disabled:d.isPending||o})]}),e.jsx(j,{type:"button",variant:"outline",size:"sm",onClick:()=>Z(r),disabled:d.isPending||a,children:e.jsx(ne,{className:"h-4 w-4"})})]}),a&&e.jsxs("div",{className:"flex items-center space-x-3 mt-2",children:[e.jsx(ce,{id:`no-profile-${s.service}`,checked:o,onCheckedChange:n=>ee(s.service,!!n),disabled:d.isPending}),e.jsxs(u,{htmlFor:`no-profile-${s.service}`,className:"text-sm",children:["Create a new profile on"," ",s.service.replace("_"," ")," for me"]})]})]},r)})]}),e.jsxs(R,{children:[e.jsx(j,{type:"button",variant:"outline",onClick:()=>g(!1),disabled:d.isPending,children:"Cancel"}),e.jsx(j,{type:"submit",disabled:d.isPending||!i.name.trim(),children:d.isPending?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-4 w-4 animate-spin"}),"Updating..."]}):"Update Artist"})]})]})]}),e.jsx($,{open:_,onOpenChange:x,children:e.jsxs(z,{children:[e.jsxs(q,{children:[e.jsx(B,{children:"Create New Label"}),e.jsx(Y,{children:"Enter label name"})]}),e.jsx("div",{className:"space-y-4",children:e.jsx(S,{placeholder:"Label name",value:y,onChange:s=>D(s.target.value),disabled:C.isPending})}),e.jsxs(R,{children:[e.jsx(j,{type:"button",variant:"outline",onClick:()=>x(!1),disabled:C.isPending,children:"Cancel"}),e.jsx(j,{type:"button",onClick:()=>C.mutate(),disabled:C.isPending||!y.trim(),children:C.isPending?e.jsx(E,{className:"h-4 w-4 animate-spin"}):"Create"})]})]})})]})}const ue={spotify:{name:"Spotify",color:"1ED760",iconSlug:"spotify",textColor:"#000000",logoColor:"000000"},applemusic:{name:"Apple Music",color:"FA243C",iconSlug:"applemusic",textColor:"#ffffff",logoColor:"ffffff"},youtubemusic:{name:"YouTube Music",color:"FF0000",iconSlug:"youtubemusic",textColor:"#ffffff",logoColor:"ffffff"},audiomack:{name:"Audiomack",color:"FFA200",iconSlug:"audiomack",textColor:"#000000",logoColor:"000000"},soundcloud:{name:"SoundCloud",color:"FF5500",iconSlug:"soundcloud",textColor:"#ffffff",logoColor:"ffffff"},deezer:{name:"Deezer",color:"191919",iconSlug:"deezer",textColor:"#ffffff",logoColor:"ffffff"},amazonmusic:{name:"Amazon Music",color:"25D1DA",iconSlug:"amazonmusic",textColor:"#000000",logoColor:"000000"},tidal:{name:"Tidal",color:"000000",iconSlug:"tidal",textColor:"#ffffff",logoColor:"ffffff"}};function Se({service:I,identifier:t,width:v=20,height:f=20,className:g}){let p=I.toLowerCase().replace(/_/g,"");p={youtube:"youtubemusic",youtube_music:"youtubemusic",youtubemusic:"youtubemusic",apple_music:"applemusic",applemusic:"applemusic",sound_cloud:"soundcloud",soundcloud:"soundcloud",amazon_music:"amazonmusic",amazonmusic:"amazonmusic"}[p]||p;const A=ue[p],i=I.replace(/_/g," ").replace(/\b\w/g,y=>y.toUpperCase());if(!A)return e.jsxs(M,{variant:"outline",className:g,children:[e.jsx("div",{style:{width:v,height:f},className:"bg-muted rounded-sm mr-1",title:i}),i]});const{name:c,color:h,iconSlug:F,textColor:L,logoColor:_}=A;if(!t||t==="Create New")return e.jsx(M,{variant:"outline",className:`border-2 border-dashed border-muted-foreground/50 bg-muted/30 text-muted-foreground h-10 ${g}`,title:`We will create a ${c} profile for you`,children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("img",{height:f,width:v,src:`https://cdn.simpleicons.org/${F}/9CA3AF`,alt:`${c} logo`,className:"rounded-sm opacity-60"}),e.jsx("span",{className:"text-sm",children:"No Profile"})]})});const x={backgroundColor:`#${h}`,color:L,borderColor:`#${h}`};return e.jsx(M,{asChild:!0,variant:"outline",className:`border-2 text-sm cursor-pointer hover:opacity-80 transition-opacity ${g}`,style:x,children:e.jsxs("a",{href:t,target:"_blank",rel:"noopener noreferrer",title:`View on ${c}`,children:[e.jsx("img",{height:f,width:v,src:`https://cdn.simpleicons.org/${F}/${_}`,alt:`${c} logo`,className:"rounded-sm mr-1"}),c]})})}export{Se as P,Pe as U};
